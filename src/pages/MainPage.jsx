import { useEffect, useState, useRef, useMemo, useCallback } from 'react';
import '../styles/MainPage.css'; // standardized to styles directory
import { MapContainer, TileLayer, Marker, useMap } from 'react-leaflet';
import { useNavigate } from 'react-router-dom';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import 'leaflet.markercluster';
import 'leaflet.markercluster/dist/MarkerCluster.css';
import 'leaflet.markercluster/dist/MarkerCluster.Default.css';
import '../styles/MapComponent.css';
import '../styles/MapSidebar.css'; // 确保你的样式文件中包含下面给出的示例CSS
import EnhancedMapControls from '../components/main-map/EnhancedMapControls'; // 增强的地图控制按钮
import EnhancedMapZoomControl from '../components/main-map/EnhancedMapZoomControl'; // 增强的地图缩放控制
import CustomMarkerHandler from '../components/main-map/CustomMarkerHandler'; // 自定义标记处理组件
import MapLayersControl from '../components/main-map/MapLayersControl'; // 地图图层控制组件
import USStatesLayer from '../components/layers/USStatesLayer'; // 美国州高亮图层
import ThailandProvincesLayer from '../components/layers/ThailandProvincesLayer'; // 泰国省份高亮图层
import EconomicHotspotLayer from '../components/layers/EconomicHotspotLayer'; // 经济热点图层
import EconomicHotspotControls from '../components/main-map/EconomicHotspotControls'; // 经济热点控制面板
import EconomicHotspotLegend from '../components/main-map/EconomicHotspotLegend'; // 经济热点图例
import EnergyDataLayer from '../components/layers/EnergyDataLayer'; // 能源数据可视化图层
import translations from '../utils/translations'; // 导入翻译对象
import { createStableMarker } from '../utils/markerUtils'; // 导入标记创建函数
import ClusterDataService from '../services/clusterDataService'; // 导入cluster数据服务
import ClusterInfoPanel from '../components/main-map/ClusterInfoPanel'; // 导入cluster信息面板
import ClusterBoundaryLayer from '../components/layers/ClusterBoundaryLayer'; // 导入cluster边界图层
import LoadingSpinner from '../components/common/LoadingSpinner'; // 导入加载动画组件
import SidebarSkeleton from '../components/common/SidebarSkeleton'; // 导入侧边栏骨架屏组件



// === 各种坐标与配置常量（保持原有功能） ===
const STATE_COORDINATES = {
  AL: { center: [32.806671, -86.791130], zoom: 7 },
  AK: { center: [61.370716, -152.404419], zoom: 4 },
  // ...其余州省略，保持不变
  DEFAULT: { center: [37.0902, -95.7129], zoom: 4 }
};

const COUNTRY_COORDINATES = {
  USA: { center: [37.0902, -95.7129], zoom: 4, name: "USA" },
  THAILAND: { center: [15.8700, 100.9925], zoom: 6, name: "Thailand" },
  UZBEKISTAN: { center: [41.3775, 64.5853], zoom: 6, name: "Uzbekistan" }
};

const THAILAND_PROVINCE_COORDINATES = {
  BANGKOK: { center: [13.7563, 100.5018], zoom: 10, name: "Bangkok" },
  CHIANG_MAI: { center: [18.7883, 98.9853], zoom: 9, name: "Chiang Mai" },
  PHUKET: { center: [7.9519, 98.3381], zoom: 10, name: "Phuket" },
  PATTAYA: { center: [12.9236, 100.8824], zoom: 10, name: "Pattaya" },
  AYUTTHAYA: { center: [14.3692, 100.5876], zoom: 10, name: "Ayutthaya" },
  DEFAULT: { center: [15.8700, 100.9925], zoom: 6, name: "Thailand" }
};

// === 组件：自动飞到指定州 ===
const StateZoomer = ({ targetState, resetTrigger }) => {
  const map = useMap();
  const timeoutRef = useRef(null);

  useEffect(() => {
    if (targetState) {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
      timeoutRef.current = setTimeout(() => {
        const stateCoord = STATE_COORDINATES[targetState] || STATE_COORDINATES.DEFAULT;
        map.flyTo(stateCoord.center, stateCoord.zoom, { duration: 0.8 });
      }, 100);
    }
    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, [map, targetState, resetTrigger]);

  return null;
};

// === 组件：标记聚合 ===
const MarkerClusterComponent = ({ markers, handleViewDetails, handleFocusPark }) => {
  const map = useMap();
  const clusterGroupRef = useRef(null);

  useEffect(() => {
    if (!map || !markers || markers.length === 0) return;

    // 创建智能标记聚合组
    const markerClusterGroup = L.markerClusterGroup({
      // 动态聚合半径：根据缩放级别调整
      maxClusterRadius: (zoom) => {
        if (zoom <= 8) return 80;      // 远距离：大聚合半径
        if (zoom <= 12) return 50;     // 中距离：中等聚合半径
        return 30;                     // 近距离：小聚合半径
      },
      spiderfyOnMaxZoom: true,
      showCoverageOnHover: false,
      zoomToBoundsOnClick: true,
      // 聚合距离阈值
      disableClusteringAtZoom: 16,     // 缩放级别16以上不聚合
      // 设置较高的z-index，但允许鼠标事件穿透
      zIndexOffset: 1000,
      // 添加特殊的CSS类，允许鼠标事件穿透
      polygonOptions: {
        className: 'marker-cluster-polygon'
      },
      // 智能聚合图标创建
      iconCreateFunction: (cluster) => {
        const count = cluster.getChildCount();
        const zoom = map.getZoom();

        // 根据缩放级别和聚合数量动态调整大小和样式
        let size = 35;
        let className = 'small';

        if (count > 100) {
          size = zoom <= 8 ? 70 : 60;
          className = 'xlarge';
        } else if (count > 50) {
          size = zoom <= 8 ? 60 : 50;
          className = 'large';
        } else if (count > 20) {
          size = zoom <= 8 ? 50 : 45;
          className = 'medium';
        } else if (count > 5) {
          size = 40;
          className = 'small';
        }

        // 添加聚合密度指示
        const density = count > 100 ? 'high' : count > 20 ? 'medium' : 'low';

        return L.divIcon({
          html: `
            <div class="cluster-marker cluster-${density}">
              <span class="cluster-count">${count}</span>
              <span class="cluster-label">地块</span>
            </div>
          `,
          className: `marker-cluster marker-cluster-${className}`,
          iconSize: L.point(size, size),
          iconAnchor: L.point(size/2, size/2)
        });
      }
    });

    // 添加标记到聚合组
    markers.forEach(marker => {
      const { position, icon, parkItem } = marker;

      // 创建标记，添加特殊选项允许鼠标事件穿透
      const leafletMarker = L.marker(position, {
        icon,
        // 设置较高的z-index，但不阻止鼠标事件
        zIndexOffset: 1000,
        // 添加特殊的CSS类，允许鼠标事件穿透
        className: 'marker-with-events-passthrough',
        // 设置交互选项
        interactive: true,
        bubblingMouseEvents: true
      });

      // 创建弹出窗口内容
      const popupContent = document.createElement('div');
      popupContent.className = 'park-popup-container';

      const header = document.createElement('div');
      header.className = 'park-popup-header';

      const title = document.createElement('h4');
      title.className = 'park-popup-title';
      title.textContent = parkItem.park || parkItem.name;
      header.appendChild(title);

      const content = document.createElement('div');
      content.className = 'park-popup-content';

      const location = document.createElement('p');
      location.className = 'park-popup-location';
      location.textContent = parkItem.province
        ? parkItem.province
        : ((parkItem.city || '') + (parkItem.state ? `, ${parkItem.state}` : ''));
      content.appendChild(location);

      // 添加关键数据信息区域
      const keyDataContainer = document.createElement('div');
      keyDataContainer.className = 'park-popup-key-data';

      // 获取当前语言
      const currentLang = localStorage.getItem('preferredLanguage') || 'en';

      // 租金信息
      const rentLabel = document.createElement('span');
      rentLabel.className = 'key-data-label';
      rentLabel.textContent = currentLang === 'en' ? 'Rent: ' : '租金: ';

      const rentValue = document.createElement('span');
      rentValue.className = 'key-data-value';
      rentValue.textContent = `$${(parkItem.monthly_leasing_cost ?? 0).toLocaleString()}`;

      const rentRow = document.createElement('div');
      rentRow.className = 'key-data-row';
      rentRow.appendChild(rentLabel);
      rentRow.appendChild(rentValue);
      keyDataContainer.appendChild(rentRow);

      // 面积信息
      const areaLabel = document.createElement('span');
      areaLabel.className = 'key-data-label';
      areaLabel.textContent = currentLang === 'en' ? 'Area: ' : '用地面积: ';

      const areaValue = document.createElement('span');
      areaValue.className = 'key-data-value';
      areaValue.textContent = `${(parkItem.square_footage ?? 0).toLocaleString()} ${currentLang === 'en' ? 'sq ft' : '平方英尺'}`;

      const areaRow = document.createElement('div');
      areaRow.className = 'key-data-row';
      areaRow.appendChild(areaLabel);
      areaRow.appendChild(areaValue);
      keyDataContainer.appendChild(areaRow);

      // 状态信息
      const statusLabel = document.createElement('span');
      statusLabel.className = 'key-data-label';
      statusLabel.textContent = currentLang === 'en' ? 'Status: ' : '状态: ';

      const statusValue = document.createElement('span');
      statusValue.className = 'key-data-value';
      statusValue.textContent = parkItem.status || (currentLang === 'en' ? 'Unknown' : '未知');

      const statusRow = document.createElement('div');
      statusRow.className = 'key-data-row';
      statusRow.appendChild(statusLabel);
      statusRow.appendChild(statusValue);
      keyDataContainer.appendChild(statusRow);

      // 添加可建面积信息（使用虚拟数据）
      const buildableLabel = document.createElement('span');
      buildableLabel.className = 'key-data-label';
      buildableLabel.textContent = currentLang === 'en' ? 'Buildable: ' : '可建面积: ';

      const buildableValue = document.createElement('span');
      buildableValue.className = 'key-data-value';
      // 使用虚拟数据：假设可建面积是总面积的80%
      const buildableArea = Math.round((parkItem.square_footage ?? 0) * 0.8);
      buildableValue.textContent = `${buildableArea.toLocaleString()} ${currentLang === 'en' ? 'sq ft' : '平方英尺'}`;

      const buildableRow = document.createElement('div');
      buildableRow.className = 'key-data-row';
      buildableRow.appendChild(buildableLabel);
      buildableRow.appendChild(buildableValue);
      keyDataContainer.appendChild(buildableRow);

      content.appendChild(keyDataContainer);

      const button = document.createElement('button');
      button.className = 'park-popup-button';
      button.textContent = currentLang === 'en' ? 'View Details' : '查看详情';
      button.onclick = (e) => {
        if (handleViewDetails) {
          handleViewDetails(parkItem, e);
        }
      };
      content.appendChild(button);

      popupContent.appendChild(header);
      popupContent.appendChild(content);

      // 添加弹出窗口到标记
      leafletMarker.bindPopup(popupContent);


      // 添加点击事件处理，点击标记时高亮对应的侧边栏项
      if (handleFocusPark) {
        leafletMarker.on('click', () => {
          handleFocusPark(parkItem);
        });
      }

      // 将标记添加到聚合组
      markerClusterGroup.addLayer(leafletMarker);
    });

    // 将聚合组添加到地图
    map.addLayer(markerClusterGroup);
    clusterGroupRef.current = markerClusterGroup;

    // 清理函数
    return () => {
      if (clusterGroupRef.current) {
        map.removeLayer(clusterGroupRef.current);
        clusterGroupRef.current = null;
      }
    };
  }, [map, markers, handleViewDetails]);

  return null;
};







// === 组件：Marker带自动打开Popup的功能 ===
const MarkerWithHoverCircle = ({ position, icon, tooltip, popup, parkItem, isFocused, handleViewDetails }) => {
  const markerRef = useRef(null);

  // 当标记被聚焦时自动打开弹窗
  useEffect(() => {
    if (isFocused && markerRef.current) {
      // 使用延时确保标记已经完全加载
      setTimeout(() => {
        try {
          // 创建并绑定弹窗
          const popupContent = document.createElement('div');
          popupContent.className = 'park-popup-container';

          const header = document.createElement('div');
          header.className = 'park-popup-header';

          const title = document.createElement('h4');
          title.className = 'park-popup-title';
          title.textContent = parkItem.park || parkItem.name;
          header.appendChild(title);

          const content = document.createElement('div');
          content.className = 'park-popup-content';

          const location = document.createElement('p');
          location.className = 'park-popup-location';
          // 根据数据类型构建位置文本（支持美国与其他国家）
          if (parkItem.city || parkItem.province || parkItem.admin_name) {
            const parts = [];
            if (parkItem.city) parts.push(parkItem.city);
            if (parkItem.state) parts.push(parkItem.state);
            if (!parkItem.state && parkItem.admin_name) parts.push(parkItem.admin_name);
            if (parkItem.province && !parts.includes(parkItem.province)) parts.push(parkItem.province);
            location.textContent = parts.join(', ');
          } else {
            location.textContent = '';
          }
          content.appendChild(location);

          // 添加关键数据信息区域
          const keyDataContainer = document.createElement('div');
          keyDataContainer.className = 'park-popup-key-data';

          // 获取当前语言
          const currentLang = localStorage.getItem('preferredLanguage') || 'en';

          // === 公共/通用字段（美国/海外通用） ===
          // 如果有 status
          if (parkItem.status) {
            const statusRow = document.createElement('div');
            statusRow.className = 'key-data-row';
            statusRow.innerHTML = `<span class=\"key-data-label\">🛈 ${currentLang === 'en' ? 'Status' : '状态'}</span>`+
                                  `<span class=\"key-data-value\">${parkItem.status}</span>`;
            keyDataContainer.appendChild(statusRow);
          }

          // 如果有租金/面积（美国数据）
          if (parkItem.monthly_leasing_cost != null) {
            const rentRow = document.createElement('div');
            rentRow.className = 'key-data-row';
            rentRow.innerHTML = `<span class=\"key-data-label\">💲 ${currentLang === 'en' ? 'Rent' : '租金'}</span>`+
                                `<span class=\"key-data-value\">$${Number(parkItem.monthly_leasing_cost||0).toLocaleString()}</span>`;
            keyDataContainer.appendChild(rentRow);
          }
          if (parkItem.square_footage != null) {
            const areaRow = document.createElement('div');
            areaRow.className = 'key-data-row';
            areaRow.innerHTML = `<span class=\"key-data-label\">📐 ${currentLang === 'en' ? 'Area' : '用地面积'}</span>`+
                                `<span class=\"key-data-value\">${Number(parkItem.square_footage||0).toLocaleString()} ${currentLang === 'en' ? 'sq ft' : '平方英尺'}</span>`;
            keyDataContainer.appendChild(areaRow);
          }

          // === 乌兹别克斯坦特有字段（分行显示，更结构化） ===
          if (parkItem.admin_name) {
            const row = document.createElement('div');
            row.className = 'key-data-row';
            row.innerHTML = `<span class="key-data-label">🏙️ ${currentLang === 'en' ? 'Admin' : '行政区'}</span>`+
                            `<span class="key-data-value">${parkItem.admin_name}</span>`;
            keyDataContainer.appendChild(row);
          }
          if (parkItem.capital) {
            const row = document.createElement('div');
            row.className = 'key-data-row';
            row.innerHTML = `<span class="key-data-label">⭐ ${currentLang === 'en' ? 'Capital' : '首都等级'}</span>`+
                            `<span class="key-data-value">${parkItem.capital}</span>`;
            keyDataContainer.appendChild(row);
          }
          if (parkItem.iso2) {
            const row = document.createElement('div');
            row.className = 'key-data-row';
            row.innerHTML = `<span class="key-data-label">🌐 ISO2</span>`+
                            `<span class="key-data-value">${parkItem.iso2}</span>`;
            keyDataContainer.appendChild(row);
          }

          if (parkItem.population || parkItem.population_proper) {
            const popRow = document.createElement('div');
            popRow.className = 'key-data-row';
            const pop = Number(parkItem.population || 0).toLocaleString();
            const proper = Number(parkItem.population_proper || 0).toLocaleString();
            const label = currentLang === 'en' ? 'Population' : '人口';
            const value = parkItem.population_proper ? `${pop} (proper: ${proper})` : `${pop}`;
            popRow.innerHTML = `<span class="key-data-label">${label}</span>`+
                               `<span class="key-data-value">${value}</span>`;
            keyDataContainer.appendChild(popRow);
          }

          content.appendChild(keyDataContainer);

          const button = document.createElement('button');
          button.className = 'park-popup-button';
          button.textContent = currentLang === 'en' ? 'View Details' : '查看详情';
          button.onclick = (e) => {
            // 使用正确的handleViewDetails函数处理导航
            if (handleViewDetails) {
              handleViewDetails(parkItem, e);
            }
          };
          content.appendChild(button);

          popupContent.appendChild(header);
          popupContent.appendChild(content);

          // 绑定弹窗并打开
          markerRef.current.bindPopup(popupContent).openPopup();
          console.log('聚焦标记成功打开弹窗');
        } catch (error) {
          console.error('打开聚焦标记弹窗时出错:', error);
        }
      }, 500);
    }
  }, [isFocused, parkItem]);

  return (
    <Marker position={position} icon={icon} ref={markerRef}>
      {tooltip}
      {popup}
    </Marker>
  );
};

// === 主组件：MapComponent ===
export default function MapComponent() {
  const navigate = useNavigate();

  // --- 状态 ---
  const [focusedPark, setFocusedPark] = useState(null);
  const [selectedParkId, setSelectedParkId] = useState(null); // 用于跟踪当前选中的园区ID
  const [mergedData, setMergedData] = useState([]);
  const [leaseMaxRange, setLeaseMaxRange] = useState(1000000); // Keep track of overall max for potential validation/defaults
  const [minLease, setMinLease] = useState('0');             // NEW state for min lease cost
  const [maxLease, setMaxLease] = useState('');            // NEW state for max lease cost (string to allow empty input)
  const [sortOrder, setSortOrder] = useState('asc');
  const [selectedStates, setSelectedStates] = useState([]);
  const [selectedCities, setSelectedCities] = useState([]); // USA cities
  const [citySearchTerm, setCitySearchTerm] = useState('');
  const [showAllCities, setShowAllCities] = useState(false);
  const [mapStyle, setMapStyle] = useState('day');
  const [targetState, setTargetState] = useState(null);
  const [zoomTrigger, setZoomTrigger] = useState(0);
  const [showStateTooltip, setShowStateTooltip] = useState(false);
  const [showCityTooltip, setShowCityTooltip] = useState(false);
  const [displayMode, setDisplayMode] = useState('all');
  const [selectedCountry, setSelectedCountry] = useState('USA');
  const [thaiProvinceTarget, setThaiProvinceTarget] = useState(null);
  const [thaiParksData, setThaiParksData] = useState([]);
  const [uzbekistanData, setUzbekistanData] = useState([]);
  const [uzIndustrialParks, setUzIndustrialParks] = useState([]); // 乌兹别克斯坦园区数据（演示）
  const [expandedUzCities, setExpandedUzCities] = useState({}); // 侧边栏展开状态
  // Uzbekistan filters
  const [selectedUzCities, setSelectedUzCities] = useState([]);
  const [uzCitySearchTerm, setUzCitySearchTerm] = useState('');
  const [selectedUzCapitalLevels, setSelectedUzCapitalLevels] = useState([]);
  const [minPop, setMinPop] = useState('');
  const [maxPop, setMaxPop] = useState('');

  // --- 自定义标记相关状态 ---
  const [isCustomMarkerMode, setIsCustomMarkerMode] = useState(false);
  const [customMarkers, setCustomMarkers] = useState([]);

  // --- 图层相关状态 ---
  const [visibleLayers, setVisibleLayers] = useState([]);

  // --- 经济热点相关状态 ---
  const [isEconomicHotspotActive, setIsEconomicHotspotActive] = useState(false);
  const [showEconomicControls, setShowEconomicControls] = useState(false);
  const [economicHotspotSettings, setEconomicHotspotSettings] = useState({
    modelType: 'lq', // 'lq' 或 'gravity'
    intensity: 50,   // 强度 0-100
    industry: 'manufacturing', // 行业类型
    radius: 50000,   // 服务半径（米）
    country: selectedCountry, // 当前选择的国家
    showLabels: true,
    showCircles: true,
    showDetails: true
  });

  // --- 能源数据可视化相关状态 ---
  const [isEnergyDataActive, setIsEnergyDataActive] = useState(false);
  const [energyDataType, setEnergyDataType] = useState('customers'); // 'customers', 'price', 'revenue', 'sales'
  const [energyData, setEnergyData] = useState([]);
  const [isLoadingEnergyData, setIsLoadingEnergyData] = useState(false);
  const [hideIndustrialMarkers, setHideIndustrialMarkers] = useState(false);
  const [hideEnergyMarkers, setHideEnergyMarkers] = useState(false);
  const [energyDataFilters, setEnergyDataFilters] = useState({
    stateId: '',
    sectorId: ''
  });

  // --- 语言相关状态 ---
  const [language, setLanguage] = useState(() => {
    // 优先使用用户登录后的语言偏好
    const userLang = localStorage.getItem('preferredLanguage');
    return userLang || 'en';
  });

  // --- Cluster相关状态 ---
  const [selectedCluster, setSelectedCluster] = useState(null);
  const [showClusterInfo, setShowClusterInfo] = useState(false);
  const [clusterSummary, setClusterSummary] = useState(null);

  // --- 数据加载状态 ---
  const [isLoadingClusterData, setIsLoadingClusterData] = useState(true);
  const [loadingProgress, setLoadingProgress] = useState({ stage: 'starting', progress: 0, message: '准备加载数据...' });
  const [hasQuickData, setHasQuickData] = useState(false);

  // 语言切换处理函数
  const toggleLanguage = useCallback(() => {
    const newLang = language === 'en' ? 'zh' : 'en';
    setLanguage(newLang);
    localStorage.setItem('preferredLanguage', newLang);
  }, [language]);

  // 清除可能存在的错误状态并保存当前地图状态
  useEffect(() => {
    // 检查是否是从详情页面返回
    const isReturningFromDetail = document.referrer.includes('/park/');
    console.log('Initial load - Is returning from detail page:', isReturningFromDetail, 'Referrer:', document.referrer);

    // 检查是否有原始地图状态
    const originalMapState = localStorage.getItem('originalMapState');
    console.log('Initial load - Original map state exists:', originalMapState !== null);

    // 只在组件首次加载时执行一次
    // 如果没有保存的状态，或者是首次加载，则清除可能存在的错误状态
    if (!isReturningFromDetail && !originalMapState) {
      console.log('Not returning from detail page and no original map state, clearing saved states');
      localStorage.removeItem('mapState');
      localStorage.removeItem('originalMapState');
    } else if (isReturningFromDetail && originalMapState) {
      console.log('Returning from detail page with original map state, preserving it');
      // 确保有一份备份
      localStorage.setItem('mapState', originalMapState);
    }

    // 添加页面卸载事件监听器，在页面关闭前保存当前地图状态
    const saveMapStateBeforeUnload = () => {
      if (mapRef.current) {
        try {
          const currentMapCenter = [mapRef.current.getCenter().lat, mapRef.current.getCenter().lng];
          const currentMapZoom = mapRef.current.getZoom();

          const mapState = {
            selectedCountry,
            mapStyle,
            displayMode,
            selectedStates,
            selectedCities,
            minLease,
            maxLease,
            sortOrder,
            targetState,
            thaiProvinceTarget,
            showAllCities,
            citySearchTerm,
            mapCenter: currentMapCenter,
            mapZoom: currentMapZoom
          };

          console.log('Saving map state before page unload:', mapState);
          localStorage.setItem('mapState', JSON.stringify(mapState));
          localStorage.setItem('originalMapState', JSON.stringify(mapState));
        } catch (error) {
          console.error('Error saving map state before unload:', error);
        }
      }
    };

    window.addEventListener('beforeunload', saveMapStateBeforeUnload);

    // 添加定期保存地图状态的机制，以防意外关闭
    const intervalId = setInterval(() => {
      if (mapRef.current) {
        try {
          const currentMapCenter = [mapRef.current.getCenter().lat, mapRef.current.getCenter().lng];
          const currentMapZoom = mapRef.current.getZoom();

          const mapState = {
            selectedCountry,
            mapStyle,
            displayMode,
            selectedStates,
            selectedCities,
            minLease,
            maxLease,
            sortOrder,
            targetState,
            thaiProvinceTarget,
            showAllCities,
            citySearchTerm,
            mapCenter: currentMapCenter,
            mapZoom: currentMapZoom
          };

          console.log('Auto-saving map state:', mapState);
          localStorage.setItem('mapState', JSON.stringify(mapState));
          localStorage.setItem('originalMapState', JSON.stringify(mapState));
        } catch (error) {
          console.error('Error auto-saving map state:', error);
        }
      }
    }, 10000); // 每10秒保存一次

    return () => {
      window.removeEventListener('beforeunload', saveMapStateBeforeUnload);
      clearInterval(intervalId);
    };
  }, [selectedCountry, mapStyle, displayMode, selectedStates, selectedCities,
      minLease, maxLease, sortOrder, targetState, thaiProvinceTarget, showAllCities, citySearchTerm]);

  const mapRef = useRef(null);

  // 全局弹窗清理函数
  const clearAllPopupsAndTooltips = useCallback(() => {
    if (mapRef.current) {
      // 关闭所有弹窗
      mapRef.current.closePopup();

      // 清理所有工具提示和弹窗DOM元素
      setTimeout(() => {
        const tooltips = document.querySelectorAll('.leaflet-tooltip');
        tooltips.forEach(tooltip => tooltip.remove());

        const popups = document.querySelectorAll('.leaflet-popup');
        popups.forEach(popup => popup.remove());

        // 清理特定类型的工具提示
        const specificTooltips = document.querySelectorAll('.compact-state-tooltip, .cluster-boundary-tooltip, .parcel-sample-tooltip');
        specificTooltips.forEach(tooltip => {
          const parent = tooltip.closest('.leaflet-tooltip');
          if (parent) {
            parent.remove();
          }
        });
      }, 10);
    }
  }, []);

  // 添加全局地图点击事件监听器
  useEffect(() => {
    if (mapRef.current) {
      const handleGlobalMapClick = () => {
        clearAllPopupsAndTooltips();
      };

      mapRef.current.on('click', handleGlobalMapClick);

      return () => {
        if (mapRef.current) {
          mapRef.current.off('click', handleGlobalMapClick);
        }
      };
    }
  }, [clearAllPopupsAndTooltips]);

  // 使用 useState 管理初始地图位置，而不是静态变量
  const [initialMapState] = useState(() => {
    // 首先检查是否有从详情页面返回的状态
    const originalMapStateJson = localStorage.getItem('originalMapState');
    if (originalMapStateJson) {
      try {
        const originalState = JSON.parse(originalMapStateJson);
        console.log(`MapComponent: Initialized with originalMapState: center=${originalState.mapCenter}, zoom=${originalState.mapZoom}`);
        return {
          position: originalState.mapCenter,
          zoom: originalState.mapZoom
        };
      } catch (e) {
        console.error('Error parsing originalMapState:', e);
      }
    }

    // 如果没有 originalMapState，尝试使用普通的 mapState
    const savedMapStateJson = localStorage.getItem('mapState');
    if (savedMapStateJson) {
      try {
        const savedState = JSON.parse(savedMapStateJson);
        console.log(`MapComponent: Initialized with saved map state: center=${savedState.mapCenter}, zoom=${savedState.mapZoom}`);
        return {
          position: savedState.mapCenter,
          zoom: savedState.mapZoom
        };
      } catch (e) {
        console.error('Error parsing mapState:', e);
      }
    }

    // 如果没有保存的状态，使用默认值
    const defaultPosition = COUNTRY_COORDINATES[selectedCountry]?.center || [39.8283, -98.5795];
    const defaultZoom = COUNTRY_COORDINATES[selectedCountry]?.zoom || 5;
    console.log(`MapComponent: Using default position: center=${defaultPosition}, zoom=${defaultZoom}`);
    return {
      position: defaultPosition,
      zoom: defaultZoom
    };
  });

  // 使用解构赋值获取初始位置和缩放级别
  const initialPosition = initialMapState.position;
  const initialZoom = initialMapState.zoom;

  // 不同地图样式
  const mapTileUrls = {
    day: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
    night: "https://tiles.stadiamaps.com/tiles/alidade_smooth_dark/{z}/{x}/{y}{r}.png",
    satellite: "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
  };
  const mapAttributions = {
    day: " OpenStreetMap contributors",
    night: " Stadia Maps, OpenMapTiles, OpenStreetMap contributors",
    satellite: " Esri — Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community"
  };

  const stateFullNames = {
    AL: 'Alabama',
    AK: 'Alaska',
    AZ: 'Arizona',
    AR: 'Arkansas',
    CA: 'California',
    CO: 'Colorado',
    CT: 'Connecticut',
    DE: 'Delaware',
    FL: 'Florida',
    GA: 'Georgia',
    HI: 'Hawaii',
    ID: 'Idaho',
    IL: 'Illinois',
    IN: 'Indiana',
    IA: 'Iowa',
    KS: 'Kansas',
    KY: 'Kentucky',
    LA: 'Louisiana',
    ME: 'Maine',
    MD: 'Maryland',
    MA: 'Massachusetts',
    MI: 'Michigan',
    MN: 'Minnesota',
    MS: 'Mississippi',
    MO: 'Missouri',
    MT: 'Montana',
    NE: 'Nebraska',
    NV: 'Nevada',
    NH: 'New Hampshire',
    NJ: 'New Jersey',
    NM: 'New Mexico',
    NY: 'New York',
    NC: 'North Carolina',
    ND: 'North Dakota',
    OH: 'Ohio',
    OK: 'Oklahoma',
    OR: 'Oregon',
    PA: 'Pennsylvania',
    RI: 'Rhode Island',
    SC: 'South Carolina',
    SD: 'South Dakota',
    TN: 'Tennessee',
    TX: 'Texas',
    UT: 'Utah',
    VT: 'Vermont',
    VA: 'Virginia',
    WA: 'Washington',
    WV: 'West Virginia',
    WI: 'Wisconsin',
    WY: 'Wyoming'
  };

  // --- 加载cluster数据 - 优化版本 ---
  useEffect(() => {
    async function fetchClusterData() {
      try {
        setIsLoadingClusterData(true);
        setLoadingProgress({ stage: 'starting', progress: 0, message: '准备加载数据...' });

        // 首先快速加载预览数据
        console.log('� Loading quick preview data...');
        const quickData = await ClusterDataService.loadQuickPreview((progress) => {
          setLoadingProgress({
            ...progress,
            message: progress.message || '加载预览数据...'
          });
        });

        if (quickData && quickData.length > 0) {
          console.log('✅ Quick preview loaded:', quickData.length, 'items');
          setMergedData(quickData);
          setHasQuickData(true);

          // 计算预览数据的最大成本
          let maxCost = 0;
          for (const item of quickData) {
            const cost = item.monthly_leasing_cost ?? item.noi ?? 0;
            if (cost > maxCost) maxCost = cost;
          }
          setLeaseMaxRange(maxCost);

          // 暂时设置加载完成，让用户可以看到预览数据
          setIsLoadingClusterData(false);
        }

        // 然后在后台加载完整数据
        console.log('🔄 Loading complete cluster data in background...');
        const clusterData = await ClusterDataService.loadClusterData((progress) => {
          setLoadingProgress({
            ...progress,
            message: progress.message || '加载完整数据...'
          });
        });

        console.log('✅ Complete data loaded:', clusterData?.length || 0, 'items');

        // 加载cluster summary数据
        const summary = ClusterDataService.getClusterSummary();
        console.log('📈 Cluster summary loaded:', summary?.length || 0, 'clusters');
        setClusterSummary(summary);

        // 计算最大租金成本用于筛选器
        let maxCost = 0;
        for (const item of clusterData || []) {
          const cost = item.monthly_leasing_cost ?? item.noi ?? 0;
          if (cost > maxCost) maxCost = cost;
        }
        console.log('💰 Max cost calculated:', maxCost);

        setMergedData(clusterData || []);
        setLeaseMaxRange(maxCost);
        setHasQuickData(false); // 现在有完整数据了

        // 只在没有保存状态的情况下设置默认值
        if (!localStorage.getItem('mapState')) {
          setMaxLease(String(maxCost));
        }

        setLoadingProgress({ stage: 'complete', progress: 100, message: '数据加载完成！' });
        console.log(`✅ Successfully loaded ${clusterData?.length || 0} industrial parcels from cluster analysis`);

      } catch (e) {
        console.error('❌ Error loading cluster data:', e);
        setLoadingProgress({ stage: 'error', progress: 0, message: '数据加载失败，请刷新重试' });
        // 设置空数据以防止应用崩溃
        setMergedData([]);
        setLeaseMaxRange(1000000);
      } finally {
        setIsLoadingClusterData(false);
      }
    }
    fetchClusterData();
  }, []);

  // --- 加载泰国数据并添加随机字段 ---
  useEffect(() => {
    const fetchThaiParksData = async () => {
      try {
        const response = await fetch('/data/thai_parks_data.json');
        const data = await response.json();
        const enhancedThaiParks = data.map((park, index) => ({
          ...park,
          id: index,
          monthly_leasing_cost: Math.floor(Math.random() * 5000) + 2000,
          square_footage: Math.floor(Math.random() * 100000) + 50000,
          status: Math.random() > 0.3 ? "For Lease" : "Leased"
        }));
        setThaiParksData(enhancedThaiParks);
      } catch (error) {
        console.error('Error loading Thai parks data:', error);
      }
    };
    fetchThaiParksData();
  }, []);

  // --- 加载乌兹别克斯坦交通坐标数据（真实数据） ---
  useEffect(() => {
    const fetchUzbekistanData = async () => {
      try {
        const response = await fetch('/data/Uzbekistan_data/Transportation/uz Coordinates.json');
        const data = await response.json();
        // 规范化为通用结构：name、position、admin_name、population 等
        const normalized = (Array.isArray(data) ? data : []).map((item, index) => ({
          id: index,
          name: item.city,
          city: item.city,
          position: [item.lat, item.lng],
          lat: item.lat,
          lng: item.lng,
          admin_name: item.admin_name,
          population: item.population,
          iso2: item.iso2,
          capital: item.capital
        }));
        setUzbekistanData(normalized);
      } catch (error) {
        console.error('Error loading Uzbekistan coordinates data:', error);
      }
    };



    fetchUzbekistanData();
  }, []);

  // --- 计算可选州、可选城市 ---
  const stateOptions = useMemo(() => {
    if (!mergedData.length) return [];
    return [...new Set(mergedData.map(d => d.state))].filter(Boolean).sort();
  }, [mergedData]);

  const cityOptions = useMemo(() => {
    if (!mergedData.length) return [];
    return [...new Set(mergedData.map(d => d.city))].filter(Boolean).sort();
  }, [mergedData]);

  const filteredCityOptions = useMemo(() => {
    if (!citySearchTerm) return cityOptions;
    return cityOptions.filter(city =>
      city.toLowerCase().includes(citySearchTerm.toLowerCase())
    );
  }, [citySearchTerm, cityOptions]);

  // 单独加载乌兹别克斯坦园区演示数据（避免嵌套 useEffect）
  useEffect(() => {
    const fetchUzParks = async () => {
      try {
        const res = await fetch('/data/Uzbekistan_data/industrial_parks.json');
        const data = await res.json();
        const parks = (Array.isArray(data) ? data : []).map((p, idx) => ({ id: p.id || `uz-park-${idx}`, ...p }));
        setUzIndustrialParks(parks);
      } catch (e) {
        console.error('Error loading Uzbekistan industrial parks demo data:', e);
      }
    };
    fetchUzParks();
  }, []);

  // 乌兹别克斯坦城市列表 + capital 等级集
  const uzCityOptions = useMemo(() => {
    if (!uzbekistanData.length) return [];
    const base = [...new Set(uzbekistanData.map(d => d.city))].filter(Boolean).sort();
    if (!uzCitySearchTerm) return base;
    return base.filter(c => c.toLowerCase().includes(uzCitySearchTerm.toLowerCase()));
  }, [uzbekistanData, uzCitySearchTerm]);

  const uzCapitalOptions = useMemo(() => {
    if (!uzbekistanData.length) return [];
    return [...new Set(uzbekistanData.map(d => d.capital).filter(Boolean))].sort();
  }, [uzbekistanData]);



  // --- 根据用户选择过滤美国数据 ---
  const filteredData = useMemo(() => {
    const parsedMinLease = parseInt(minLease, 10);
    const parsedMaxLease = parseInt(maxLease, 10);

    // 确定有效的最小和最大值用于过滤
    const filterMin = !isNaN(parsedMinLease) && parsedMinLease >= 0 ? parsedMinLease : 0;
    const filterMax = !isNaN(parsedMaxLease) && parsedMaxLease >= filterMin ? parsedMaxLease : Infinity; // 如果最大值为空或无效，则使用 Infinity

    let data = mergedData.filter(item => {
      const cost = item.monthly_leasing_cost ?? 0;
      return cost >= filterMin && cost <= filterMax; // 按范围过滤
    });
    if (selectedStates.length > 0) {
      data = data.filter(item => selectedStates.includes(item.state));
    }
    if (selectedCities.length > 0) {
      data = data.filter(item => selectedCities.includes(item.city));
    }
    // 排序
    data.sort((a, b) => {
      const costA = a.monthly_leasing_cost ?? 0;
      const costB = b.monthly_leasing_cost ?? 0;
      return sortOrder === 'asc' ? costA - costB : costB - costA;
    });
    // 根据 displayMode 显示
    if (displayMode === 'ports') {
      data = data.filter(item => {
        const portMinCost = item['Port_Min_Cost'] ?? 0;
        const cityMinCost = item['City_Min_Cost'] ?? 0;
        return portMinCost < cityMinCost;
      });
    } else if (displayMode === 'cities') {
      data = data.filter(item => {
        const portMinCost = item['Port_Min_Cost'] ?? 0;
        const cityMinCost = item['City_Min_Cost'] ?? 0;
        return cityMinCost <= portMinCost;
      });
    }
    return data;
  }, [mergedData, minLease, maxLease, selectedStates, selectedCities, sortOrder, displayMode]);

  // --- 过滤泰国数据 ---
  const filteredThaiData = useMemo(() => {
    let data = [...thaiParksData];

    // 根据displayMode过滤泰国数据
    if (displayMode === 'ports') {
      // 只显示有港口坐标的数据
      data = data.filter(park => park.port_coordinates && park.port_coordinates[0] != null && park.port_coordinates[1] != null);
    } else if (displayMode === 'cities') {
      // 只显示有城市坐标的数据
      data = data.filter(park => park.city_coordinates && park.city_coordinates[0] != null && park.city_coordinates[1] != null);
    }

    return data;
  }, [thaiParksData, displayMode]);

  // --- 过滤乌兹别克斯坦数据 ---
  const filteredUzbekistanData = useMemo(() => {
    let data = [...uzbekistanData];

    // 根据displayMode过滤乌兹别克斯坦数据（当前仅cities/默认；无港口）
    if (displayMode === 'cities') {
      // 坐标来自 position
      data = data.filter(location => Array.isArray(location.position) && location.position[0] != null && location.position[1] != null);
    }

    // 人口范围过滤（如果有输入）
    const minP = parseInt(minPop, 10);
    const maxP = parseInt(maxPop, 10);
    if (!isNaN(minP) || !isNaN(maxP)) {
      data = data.filter(loc => {
        const pop = parseInt(loc.population, 10) || 0;
        const minOk = isNaN(minP) ? true : pop >= minP;
        const maxOk = isNaN(maxP) ? true : pop <= maxP;
        return minOk && maxOk;
      });
    }

    // 城市选择过滤（使用乌兹别克斯坦独立选择）
    if (selectedUzCities.length > 0) {
      data = data.filter(loc => selectedUzCities.includes(loc.city));
    }

    // capital 等级过滤（如 'primary' 等）
    if (selectedUzCapitalLevels.length > 0) {
      data = data.filter(loc => selectedUzCapitalLevels.includes(loc.capital));
    }

    return data;
  }, [uzbekistanData, displayMode, minPop, maxPop, selectedUzCities, selectedUzCapitalLevels]);

  // --- 根据国家选择，生成对应的 marker 数据 ---
  const markerData = useMemo(() => {
    if (selectedCountry === 'USA') {
      return filteredData
        .map((parkItem, idx) => {
          const lat = parkItem.latitude;
          const lng = parkItem.longitude;
          if (lat == null || lng == null) return null;

          // 使用cluster颜色区分不同的cluster
          const clusterColor = ClusterDataService.getClusterColor(parkItem.cluster_id || 0);

          return {
            id: idx,
            position: [lat, lng],
            icon: createStableMarker(clusterColor),
            parkItem: {
              ...parkItem,
              clusterColor // 保存颜色信息用于后续使用
            }
          };
        })
        .filter(Boolean);
    } else if (selectedCountry === 'THAILAND') {
      return filteredThaiData
        .map((park, idx) => {
          // 根据显示模式选择不同的坐标
          let position;
          if (displayMode === 'ports') {
            // 在ports模式下，只使用港口坐标
            position = park.port_coordinates;
          } else if (displayMode === 'cities') {
            // 在cities模式下，只使用城市坐标
            position = park.city_coordinates;
          } else {
            // 默认使用工业园区坐标
            position = park.position;
          }

          if (!position || position[0] == null || position[1] == null) return null;

          const useBlue = park.status === "For Lease";
          // 根据显示模式选择不同的图标颜色
          const iconColor = displayMode === 'ports' ? 'blue' : (displayMode === 'cities' ? 'red' : (useBlue ? 'blue' : 'red'));

          return {
            id: idx,
            position: position,
            icon: createStableMarker(iconColor),
            parkItem: park
          };
        })
        .filter(Boolean);
    } else if (selectedCountry === 'UZBEKISTAN') {
      const cityMarkers = filteredUzbekistanData
        .map((location, idx) => {
          const position = location.position;
          if (!position || position[0] == null || position[1] == null) return null;
          const iconColor = '#4CAF50';
          return {
            id: `uz-city-${idx}`,
            position,
            icon: createStableMarker(iconColor),
            parkItem: location
          };
        })
        .filter(Boolean);

      const parkMarkers = (uzIndustrialParks || [])
        .map((park, idx) => {
          const position = park.position;
          if (!position || position[0] == null || position[1] == null) return null;
          const iconColor = '#FF5722';
          return {
            id: `uz-park-${idx}`,
            position,
            icon: createStableMarker(iconColor),
            parkItem: park
          };
        })
        .filter(Boolean);

      return [...cityMarkers, ...parkMarkers];
    }
    return [];
  }, [selectedCountry, filteredData, filteredThaiData, filteredUzbekistanData, uzIndustrialParks, displayMode]);



  // --- 切换国家 ---
  const handleCountryChange = useCallback((country) => {
    setSelectedCountry(country);
    if (mapRef.current) {
      const countryCoord = COUNTRY_COORDINATES[country];
      mapRef.current.flyTo(countryCoord.center, countryCoord.zoom, { duration: 1.5 });
    }
    // 重置筛选
    setSelectedStates([]);
    setSelectedCities([]);
    setTargetState(null);
    setThaiProvinceTarget(null);
    setFocusedPark(null);
    setSelectedParkId(null); // 重置选中的园区ID

    // 如果经济热点图层处于激活状态，更新其国家设置
    if (isEconomicHotspotActive) {
      setEconomicHotspotSettings(prevSettings => ({
        ...prevSettings,
        country: country
      }));
    }
  }, [isEconomicHotspotActive]);

  // --- 处理侧边栏点击或地图标记点击，聚焦到园区 ---
  const handleFocusPark = useCallback((parkItem) => {
    // 先设置聚焦园区，这将触发 MarkerWithHoverCircle 组件的 useEffect 来打开弹窗（如果需要）
    setFocusedPark(parkItem);

    // 设置选中的园区ID，用于高亮显示侧边栏中的对应项
    const parkId = parkItem.id || parkItem.park || parkItem.name;
    setSelectedParkId(parkId);

    console.log('Focusing on park:', parkItem, 'with ID:', parkId); // 添加日志

    // 根据国家处理地图移动
    if (parkItem.position && mapRef.current) {
      // 泰国/乌兹别克斯坦等使用 position
      mapRef.current.flyTo(parkItem.position, 12, { duration: 1.2 });
    } else if (parkItem.latitude && parkItem.longitude && mapRef.current) { // 美国有 latitude/longitude
      // 对美国园区也使用 flyTo
      mapRef.current.flyTo([parkItem.latitude, parkItem.longitude], 14, { duration: 1.2 });
    } else {
      console.warn('Cannot fly to park, missing required coordinates:', parkItem);
    }

    // 移除了尝试在 flyTo 后打开弹窗的 setTimeout 逻辑，因为它不可靠。
    // MarkerWithHoverCircle 组件内部的 useEffect 会处理聚焦后的弹窗打开。

    // 添加自动滚动到选中项的功能
    setTimeout(() => {
      try {
        // 查找选中的元素
        const selectedElement = document.querySelector(`.park-button.selected`);
        if (selectedElement) {
          // 获取侧边栏滚动容器
          const sidebarNav = document.querySelector('.main-sidebar-nav');
          if (sidebarNav) {
            // 计算需要滚动的位置
            const elementRect = selectedElement.getBoundingClientRect();
            const containerRect = sidebarNav.getBoundingClientRect();

            // 检查元素是否在可视区域内
            const isInView = (
              elementRect.top >= containerRect.top &&
              elementRect.bottom <= containerRect.bottom
            );

            // 如果不在可视区域内，则滚动到该元素
            if (!isInView) {
              // 计算元素顶部相对于容器的偏移量
              const offsetTop = selectedElement.offsetTop;
              // 计算目标滚动位置，将元素滚动到容器的1/3处，提供更好的视觉效果
              const targetScrollTop = offsetTop - containerRect.height / 3;

              // 获取当前滚动位置
              const startScrollTop = sidebarNav.scrollTop;
              // 计算滚动距离
              const distance = targetScrollTop - startScrollTop;

              // 使用自定义动画而不是内置的scrollTo，以获得更优雅的效果
              const duration = 800; // 更长的持续时间，使动画更加平缓
              const startTime = performance.now();

              // 动画函数
              function animateScroll(currentTime) {
                const elapsedTime = currentTime - startTime;

                if (elapsedTime < duration) {
                  // 使用缓动函数使动画更自然
                  // 这是一个缓入缓出的函数，开始和结束时速度较慢，中间速度较快
                  const progress = easeInOutCubic(elapsedTime / duration);

                  // 计算当前滚动位置
                  const currentScrollTop = startScrollTop + (distance * progress);

                  // 设置滚动位置
                  sidebarNav.scrollTop = currentScrollTop;

                  // 继续动画
                  requestAnimationFrame(animateScroll);
                } else {
                  // 动画结束，确保滚动到精确位置
                  sidebarNav.scrollTop = targetScrollTop;
                }
              }

              // 缓动函数：缓入缓出三次方
              function easeInOutCubic(t) {
                return t < 0.5
                  ? 4 * t * t * t
                  : 1 - Math.pow(-2 * t + 2, 3) / 2;
              }

              // 开始动画
              requestAnimationFrame(animateScroll);
              console.log('Scrolling sidebar to selected item with custom animation');
            }
          }
        }
      } catch (error) {
        console.error('Error scrolling to selected item:', error);
      }
    }, 100); // 短暂延迟，确保DOM已更新

  }, []); // 依赖项为空，因为 setFocusedPark、setSelectedParkId 和 mapRef.current 通常是稳定的

  const handleFocusThaiProvince = useCallback((province) => {
    if (province) {
      setThaiProvinceTarget(province);
      setZoomTrigger(prev => prev + 1);
    }
  }, []);

  // --- 切换排序顺序 ---
  const handleSort = useCallback(() => {
    setSortOrder(prev => (prev === 'asc' ? 'desc' : 'asc'));
  }, []);

  // --- 全选/清空 州 ---
  const handleSelectAllStates = useCallback(() => {
    setSelectedStates([...stateOptions]);
  }, [stateOptions]);

  const handleClearStates = useCallback(() => {
    setSelectedStates([]);
  }, []);

  // --- 自定义标记相关函数 ---
  const toggleCustomMarkerMode = useCallback(() => {
    setIsCustomMarkerMode(prev => !prev);
  }, []);

  const clearAllCustomMarkers = useCallback(() => {
    console.log('Clearing all custom markers');
    setCustomMarkers([]);
  }, []);

  // --- 图层相关函数 ---
  const toggleLayer = useCallback((layerId) => {
    setVisibleLayers(prev => {
      if (prev.includes(layerId)) {
        return prev.filter(id => id !== layerId);
      } else {
        return [...prev, layerId];
      }
    });
  }, []);

  // --- 经济热点相关函数 ---
  // 切换经济热点图层的显示状态
  const toggleEconomicHotspot = useCallback(() => {
    setIsEconomicHotspotActive(prev => {
      const newState = !prev;
      if (newState) {
        // 激活时默认显示控制面板
        setShowEconomicControls(true);

        // 更新国家设置
        setEconomicHotspotSettings(prevSettings => ({
          ...prevSettings,
          country: selectedCountry
        }));
      }
      // 注意：这里不再在关闭图层时自动关闭控制面板
      return newState;
    });
  }, [selectedCountry]);

  // 单独控制经济热点设置面板的显示/隐藏
  const toggleEconomicControls = useCallback(() => {
    setShowEconomicControls(prev => !prev);
  }, []);

  // 更新经济热点设置
  const updateEconomicHotspotSettings = useCallback((newSettings) => {
    console.log("更新经济热点设置:", newSettings);
    setEconomicHotspotSettings(prevSettings => ({
      ...prevSettings,
      ...newSettings
    }));
  }, []);

  // --- 能源数据可视化相关函数 ---
  // 创建一个可以接受特定数据类型的加载函数
  const loadEnergyDataWithType = useCallback(async (dataType = energyDataType) => {
    setIsLoadingEnergyData(true);
    try {
      // 动态导入能源数据服务
      const { fetchEnergyData, testEIAAPI } = await import('../utils/energyDataService');

      // First, test the API to see if it's working
      console.log('Testing EIA API connectivity...');
      const testResult = await testEIAAPI();
      if (!testResult) {
        console.error('EIA API test failed');
      }

      const options = {
        dataTypes: [dataType],
        stateId: energyDataFilters.stateId,
        sectorId: energyDataFilters.sectorId,
        offset: 0,
        length: 5000
      };

      const data = await fetchEnergyData(options);
      setEnergyData(data);
      console.log('Energy data loaded with type:', dataType, data);
    } catch (error) {
      console.error('Failed to load energy data:', error);
      // 可以在这里添加错误提示
    } finally {
      setIsLoadingEnergyData(false);
    }
  }, [energyDataType, energyDataFilters]);

  const loadEnergyData = useCallback(async () => {
    return loadEnergyDataWithType(energyDataType);
  }, [energyDataType, loadEnergyDataWithType]);

  const toggleEnergyDataVisualization = useCallback(() => {
    setIsEnergyDataActive(prev => {
      const newState = !prev;
      if (newState) {
        // 激活时加载数据并自动隐藏工业园区标记以提供更清晰的能源数据视图
        loadEnergyData();
        setHideIndustrialMarkers(true);
      } else {
        // 关闭时清除数据并恢复工业园区标记
        setEnergyData([]);
        setHideIndustrialMarkers(false);
      }
      return newState;
    });
  }, [loadEnergyData]);

  const handleEnergyDataTypeChange = useCallback((newDataType) => {
    setEnergyDataType(newDataType);
    if (isEnergyDataActive) {
      // 如果能源数据可视化已激活，重新加载数据
      // 使用新的数据类型立即加载数据
      loadEnergyDataWithType(newDataType);
    }
  }, [isEnergyDataActive, energyDataFilters]);



  const handleEnergyDataFiltersChange = useCallback((newFilters) => {
    setEnergyDataFilters(prev => ({
      ...prev,
      ...newFilters
    }));
    if (isEnergyDataActive) {
      // 如果能源数据可视化已激活，重新加载数据
      loadEnergyDataWithType(energyDataType);
    }
  }, [isEnergyDataActive, energyDataType, loadEnergyDataWithType]);

  const toggleIndustrialMarkersVisibility = useCallback(() => {
    setHideIndustrialMarkers(prev => !prev);
  }, []);

  const toggleEnergyMarkersVisibility = useCallback(() => {
    setHideEnergyMarkers(prev => !prev);
  }, []);

  // 处理能源数据点击事件 - 现在使用新的 V2 弹窗系统
  const handleEnergyDataClick = useCallback((stateData) => {
    console.log('Energy data clicked (V2 system):', stateData);
    // 新的 V2 弹窗系统已经在 EnergyDataLayer.jsx 中处理了弹窗显示
    // 这里只需要记录点击事件，不需要额外的弹窗处理
    // 如果需要额外的分析面板或侧边栏，可以在这里添加
  }, [energyDataType, language]);

  // 旧的弹窗函数已完全移除 - 现在使用 EnergyDataLayer.jsx 中的新 V2 弹窗系统

  // --- 查看详情，显示cluster信息或跳转路由 ---
  const handleViewDetails = useCallback((parkItem, e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // 如果是美国地块数据且有APN，导航到地块详情页面
    if (parkItem.cluster_id !== undefined && selectedCountry === 'USA') {
      // 检查是否有APN信息，如果有则导航到地块详情页面
      const apn = parkItem.apn;
      if (apn) {
        // 保存当前地图状态
        if (mapRef.current) {
          const currentMapCenter = [mapRef.current.getCenter().lat, mapRef.current.getCenter().lng];
          const currentMapZoom = mapRef.current.getZoom();

          const mapState = {
            selectedCountry: 'USA',
            mapStyle,
            displayMode,
            selectedStates,
            selectedCities,
            minLease,
            maxLease,
            sortOrder,
            targetState,
            thaiProvinceTarget,
            showAllCities,
            citySearchTerm,
            mapCenter: currentMapCenter,
            mapZoom: currentMapZoom
          };

          const mapStateJson = JSON.stringify(mapState);
          localStorage.setItem('mapState', mapStateJson);
          localStorage.setItem('originalMapState', mapStateJson);
          localStorage.setItem('selectedParcel', JSON.stringify(parkItem));
        }

        navigate(`/parcel/${encodeURIComponent(apn)}`);
        return;
      }

      // 如果没有APN信息，显示cluster信息面板
      setSelectedCluster(parkItem.cluster_id);
      setShowClusterInfo(true);
      return;
    }

    // 检查是否为乌兹别克斯坦城市数据（有city字段但没有id字段，或者id不是园区格式）
    const isUzbekistanCity = selectedCountry === 'UZBEKISTAN' &&
                            parkItem.city &&
                            parkItem.iso2 === 'UZ' &&
                            (!parkItem.id || !parkItem.id.includes('-'));

    if (isUzbekistanCity) {
      // 对于乌兹别克斯坦城市数据，不执行导航，只显示弹窗信息
      console.log('乌兹别克斯坦城市弹窗点击:', parkItem.city);
      // 可以在这里添加城市详情弹窗或信息显示逻辑
      return;
    }

    // 原有的详情页跳转逻辑（用于泰国数据和乌兹别克斯坦园区数据）
    if (!mapRef.current) {
      console.error('Map reference is not available when trying to save state');
      return;
    }

    const currentMapCenter = [mapRef.current.getCenter().lat, mapRef.current.getCenter().lng];
    const currentMapZoom = mapRef.current.getZoom();

    // Determine the correct country based on the park item
    // Check if it's a Thai park (has province and matches Thai provinces)
    // Check if it's an Uzbekistan location (has province but not Thai)
    // Otherwise assume it's USA
    let parkCountry = 'USA';
    if (parkItem.position && Array.isArray(parkItem.position)) {
      // 非美国（使用position）——可能是泰国或乌兹别克斯坦
      // 依据是否有 province 判断是否为泰国
      if (parkItem.province) {
        const isThaiProvince = Object.keys(THAILAND_PROVINCE_COORDINATES).some(
          key => THAILAND_PROVINCE_COORDINATES[key].name === parkItem.province
        );
        parkCountry = isThaiProvince ? 'THAILAND' : 'UZBEKISTAN';
      } else {
        // 无 province 则视为乌兹别克斯坦（坐标来自城市）
        parkCountry = 'UZBEKISTAN';
      }
    }

    const mapState = {
      selectedCountry: parkCountry, // Use the determined country
      mapStyle,
      displayMode,
      selectedStates,
      selectedCities,
      minLease,
      maxLease,
      sortOrder,
      targetState,
      thaiProvinceTarget,
      showAllCities,
      citySearchTerm,
      mapCenter: currentMapCenter,
      mapZoom: currentMapZoom
    };
    console.log('Saving map state before navigating to detail page:', mapState);
    console.log(`Current map position: center=[${currentMapCenter}], zoom=${currentMapZoom}`);

    // 保存当前地图状态到mapState和originalMapState
    // mapState用于初始化详情页面，originalMapState用于返回时恢复
    const mapStateJson = JSON.stringify(mapState);
    localStorage.setItem('mapState', mapStateJson);
    localStorage.setItem('originalMapState', mapStateJson); // 保存原始状态以便返回时恢复

    // 确保清除可能存在的previousMapView，避免干扰状态恢复
    localStorage.removeItem('previousMapView');

    localStorage.setItem('selectedPark', JSON.stringify(parkItem));
    const parkId = parkItem.id || parkItem.park || parkItem.name;
    navigate(`/park/${encodeURIComponent(parkId)}`);
  }, [navigate, mapStyle, displayMode, selectedStates, selectedCities,
      minLease, maxLease, sortOrder, targetState, thaiProvinceTarget, showAllCities, citySearchTerm,
      selectedCountry, setSelectedCluster, setShowClusterInfo]);

  // 城市列表展示逻辑已移至 MainMapControls 组件

  // === 渲染 ===
  return (
    <div className={`map-page-container ${mapStyle === 'night' ? 'map-night-mode' : ''}`}>
      {/* 主侧边栏：可滚动、可调整宽度 */}
      <div className="main-sidebar">
        <div className="main-sidebar-header">
          <div className="header-top-row">
            <h2>{language === 'en' ? 'Industrial Geo Explorer' : '工域探索'}</h2>
            {/* 语言切换按钮已移至左侧工具栏 */}
          </div>
          <hr className="title-separator" />

          {/* 国家选择器 - 移至侧边栏 */}
          <div className="country-selector-simple">
            <select
              className="country-select-dropdown"
              value={selectedCountry}
              onChange={(e) => handleCountryChange(e.target.value)}
            >
              {Object.keys(COUNTRY_COORDINATES).map(country => (
                <option key={country} value={country}>
                  {COUNTRY_COORDINATES[country].name}
                </option>
              ))}
            </select>
          </div>

          <div className="locations-count">
            {selectedCountry === 'USA' ? filteredData.length : filteredThaiData.length} {language === 'en' ? 'locations' : '个位置'}
          </div>
        </div>

        {/* 侧边栏内容区，可滚动 */}
        <div className="main-sidebar-nav">
          {isLoadingClusterData && selectedCountry === 'USA' ? (
            // 显示加载状态
            <div className="loading-section">
              <LoadingSpinner
                size="medium"
                color="primary"
                text={loadingProgress.message}
              />
              <div className="loading-progress">
                <div className="progress-bar">
                  <div
                    className="progress-fill"
                    style={{ width: `${loadingProgress.progress}%` }}
                  ></div>
                </div>
                <div className="progress-text">
                  {loadingProgress.progress}% - {loadingProgress.stage}
                </div>
              </div>
              <SidebarSkeleton itemCount={6} />
            </div>
          ) : selectedCountry === 'USA' ? (
            <>
              {/* 筛选器已移至地图控件 */}
              <div className="results-section">
                <div className="section-header">
                  <h3>{translations[language].results}</h3>
                  <button className="btn-sort" onClick={handleSort}>
                    <i className="sort-icon">{sortOrder === 'asc' ? '↑' : '↓'}</i>
                    {translations[language][sortOrder === 'asc' ? 'sortAsc' : 'sortDesc']}
                  </button>
                </div>
                {hasQuickData && (
                  <div className="quick-data-notice">
                    <span>📊 显示预览数据，完整数据加载中...</span>
                  </div>
                )}
                <ul className="park-list">
                  {filteredData.map((parkItem, idx) => {
                    // 确定当前项是否被选中
                    const parkId = parkItem.id || parkItem.park || parkItem.name;
                    const isSelected = selectedParkId === parkId;

                    return (
                      <li key={idx} className="park-item">
                        <button
                          onClick={() => handleFocusPark(parkItem)}
                          className={`park-button ${isSelected ? 'selected' : ''}`}
                        >
                          <div className="park-name">{parkItem.park}</div>
                          <div className="park-details">
                            <span className="park-location">
                              {parkItem.city}, {parkItem.state}
                            </span>
                            <span className="park-cost">
                              ${(parkItem.monthly_leasing_cost ?? 0).toLocaleString()}
                            </span>
                          </div>
                        </button>
                      </li>
                    );
                  })}
                </ul>
              </div>
            </>
          ) : selectedCountry === 'THAILAND' ? (
            <>
              {/* 泰国省份筛选器已移至地图控件 */}
              <div className="results-section">
                <div className="section-header">
                  <h3>Thai Parks</h3>
                </div>
                <ul className="park-list">
                  {filteredThaiData.map((parkItem, idx) => {
                    // 确定当前项是否被选中
                    const parkId = parkItem.id || parkItem.park || parkItem.name;
                    const isSelected = selectedParkId === parkId;

                    return (
                      <li key={idx} className="park-item">
                        <button
                          onClick={() => handleFocusPark(parkItem)}
                          className={`park-button ${isSelected ? 'selected' : ''}`}
                        >
                          <div className="park-name">{parkItem.name}</div>
                          <div className="park-details">
                            <span className="park-location">
                              {THAILAND_PROVINCE_COORDINATES[parkItem.province]?.name || 'Thailand'}
                            </span>
                            <span className="park-cost">
                              ${parkItem.monthly_leasing_cost.toLocaleString()}
                            </span>
                          </div>
                        </button>
                      </li>
                    );
                  })}
                </ul>
              </div>
            </>
          ) : selectedCountry === 'UZBEKISTAN' ? (
            <>
              {/* 乌兹别克斯坦工业区 - 城市下拉 + 园区列表 */}
              <div className="results-section">
                <div className="section-header">
                  <h3>Uzbekistan Industrial Zones</h3>
                </div>
                <ul className="park-list">
                  {filteredUzbekistanData.map((locationItem, idx) => {
                    const locationId = locationItem.id || locationItem.city;
                    const isSelected = selectedParkId === locationId;

                    // 找到该城市下的园区（演示数据按city匹配）
                    const cityParks = (uzIndustrialParks || []).filter(p => p.city === locationItem.city);
                    const isExpanded = !!expandedUzCities[locationItem.city];

                    return (
                      <li key={idx} className="park-item">
                        <button
                          onClick={() => handleFocusPark(locationItem)}
                          className={`park-button ${isSelected ? 'selected' : ''}`}
                        >
                          <div className="park-name">{locationItem.city}</div>
                          <div className="park-details">
                            <span className="park-location">
                              {locationItem.admin_name || 'Uzbekistan'}
                            </span>
                            {locationItem.population ? (
                              <span className="park-cost">
                                Pop: {Number(locationItem.population).toLocaleString()}
                              </span>
                            ) : null}
                          </div>
                        </button>

                        {cityParks.length > 0 && (
                          <div className="city-parks">
                            <button
                              className="expand-collapse-button"
                              onClick={(e) => {
                                e.stopPropagation();
                                setExpandedUzCities(prev => ({
                                  ...prev,
                                  [locationItem.city]: !prev[locationItem.city]
                                }));
                              }}
                            >
                              {isExpanded ? '收起园区' : `展开园区 (${cityParks.length})`}
                            </button>
                            {isExpanded && (
                              <ul className="nested-park-list">
                                {cityParks.map((park, pidx) => {
                                  const pid = park.id || park.name;
                                  const pSelected = selectedParkId === pid;
                                  return (
                                    <li key={pidx} className="park-item nested">
                                      <button
                                        onClick={() => handleFocusPark(park)}
                                        className={`park-button ${pSelected ? 'selected' : ''}`}
                                      >
                                        <div className="park-name">{park.name}</div>
                                        <div className="park-details">
                                          <span className="park-location">{park.admin_name || 'Uzbekistan'}</span>
                                          {park.monthly_leasing_cost != null && (
                                            <span className="park-cost">${(park.monthly_leasing_cost || 0).toLocaleString()}</span>
                                          )}
                                        </div>
                                      </button>
                                      <div className="park-actions">
                                        <button className="btn-view" onClick={(e) => handleViewDetails(park, e)}>
                                          {language === 'en' ? 'View Details' : '查看详情'}
                                        </button>
                                      </div>
                                    </li>
                                  );
                                })}
                              </ul>
                            )}
                          </div>
                        )}
                      </li>
                    );
                  })}
                </ul>
              </div>
            </>
          ) : null}
        </div>
      </div>

      {/* 地图容器 */}
      <div className="map-container" style={{ position: 'relative', width: '100%', height: '100%' }}>
        {/* EnhancedMapControls will handle its own positioning */}
        <EnhancedMapControls
          // 地图控件属性
          mapStyle={mapStyle}
          onStyleChange={setMapStyle}
          displayMode={displayMode}
          onDisplayModeChange={setDisplayMode}
          t={translations[language]} // 使用当前语言的翻译对象
          language={language}
          toggleLanguage={toggleLanguage}

          // 国家选择
          selectedCountry={selectedCountry}
          handleCountryChange={handleCountryChange}

          // 自定义标记模式
          isCustomMarkerMode={isCustomMarkerMode}
          toggleCustomMarkerMode={toggleCustomMarkerMode}
          clearAllCustomMarkers={clearAllCustomMarkers}

          // 图层控制
          visibleLayers={visibleLayers}
          toggleLayer={toggleLayer}

          // 经济热点图层
          isEconomicHotspotActive={isEconomicHotspotActive}
          toggleEconomicHotspot={toggleEconomicHotspot}
          showEconomicControls={showEconomicControls}
          toggleEconomicControls={toggleEconomicControls}
          economicHotspotSettings={economicHotspotSettings}
          updateEconomicHotspotSettings={updateEconomicHotspotSettings}

          // 园区数据和操作 - 用于搜索功能
          filteredData={filteredData}
          filteredThaiData={filteredThaiData}
          handleFocusPark={handleFocusPark}

          // 美国筛选器
          leaseMaxRange={leaseMaxRange}
          minLease={minLease}
          setMinLease={setMinLease}
          maxLease={maxLease}
          setMaxLease={setMaxLease}
          stateOptions={stateOptions}
          selectedStates={selectedStates}
          setSelectedStates={setSelectedStates}
          handleSelectAllStates={handleSelectAllStates}
          handleClearStates={handleClearStates}
          stateFullNames={stateFullNames}
          cityOptions={cityOptions}
          filteredCityOptions={filteredCityOptions}
          selectedCities={selectedCities}
          setSelectedCities={setSelectedCities}
          citySearchTerm={citySearchTerm}
          setCitySearchTerm={setCitySearchTerm}
          showAllCities={showAllCities}
          setShowAllCities={setShowAllCities}
          showStateTooltip={showStateTooltip}
          setShowStateTooltip={setShowStateTooltip}
          showCityTooltip={showCityTooltip}
          setShowCityTooltip={setShowCityTooltip}

          // 乌兹别克斯坦过滤器
          selectedUzCities={selectedUzCities}
          setSelectedUzCities={setSelectedUzCities}
          uzCitySearchTerm={uzCitySearchTerm}
          setUzCitySearchTerm={setUzCitySearchTerm}
          uzCapitalOptions={uzCapitalOptions}
          selectedUzCapitalLevels={selectedUzCapitalLevels}
          setSelectedUzCapitalLevels={setSelectedUzCapitalLevels}

          // 泰国筛选器
          handleFocusThaiProvince={handleFocusThaiProvince}
          THAILAND_PROVINCE_COORDINATES={THAILAND_PROVINCE_COORDINATES}
          COUNTRY_COORDINATES={COUNTRY_COORDINATES}

          // 乌兹别克斯坦过滤器
          uzCityOptions={uzCityOptions}
          minPop={minPop}
          setMinPop={setMinPop}
          maxPop={maxPop}
          setMaxPop={setMaxPop}

          // 能源数据可视化
          isEnergyDataActive={isEnergyDataActive}
          toggleEnergyDataVisualization={toggleEnergyDataVisualization}
          energyDataType={energyDataType}
          handleEnergyDataTypeChange={handleEnergyDataTypeChange}
          energyDataFilters={energyDataFilters}
          handleEnergyDataFiltersChange={handleEnergyDataFiltersChange}
          isLoadingEnergyData={isLoadingEnergyData}
          hideIndustrialMarkers={hideIndustrialMarkers}
          toggleIndustrialMarkersVisibility={toggleIndustrialMarkersVisibility}
          hideEnergyMarkers={hideEnergyMarkers}
          toggleEnergyMarkersVisibility={toggleEnergyMarkersVisibility}
        />

        <MapContainer
          center={initialPosition}
          zoom={initialZoom}
          style={{ height: '100%', width: '100%' }}
          ref={mapRef}
          zoomControl={false} // 禁用默认的 Leaflet 缩放控件
        >
          <TileLayer
            url={mapTileUrls[mapStyle]}
            attribution={mapAttributions[mapStyle]}
          />
          <EnhancedMapZoomControl
            t={translations[language]}
            isDarkMode={mapStyle === 'night'}
            initialPosition={initialPosition}
            initialZoom={initialZoom}
            selectedCountry={selectedCountry}
            COUNTRY_COORDINATES={COUNTRY_COORDINATES}
          />
          {selectedCountry === 'USA' && targetState && (
            <StateZoomer targetState={targetState} resetTrigger={zoomTrigger} />
          )}
          {selectedCountry === 'USA' && (
            <USStatesLayer show={true} mapStyle={mapStyle} />
          )}
          {selectedCountry === 'THAILAND' && (
            <ThailandProvincesLayer show={true} mapStyle={mapStyle} />
          )}

          <MapController
            mapRef={mapRef}
            selectedCountry={selectedCountry}
            targetState={targetState}
            thaiProvinceTarget={thaiProvinceTarget}
            zoomTrigger={zoomTrigger}
            setSelectedCountry={setSelectedCountry}
            setMapStyle={setMapStyle}
            setDisplayMode={setDisplayMode}
            setSelectedStates={setSelectedStates}
            setSelectedCities={setSelectedCities}
            setMinLease={setMinLease}
            setMaxLease={setMaxLease}
            setSortOrder={setSortOrder}
            setTargetState={setTargetState}
            setThaiProvinceTarget={setThaiProvinceTarget}
            setShowAllCities={setShowAllCities}
            setCitySearchTerm={setCitySearchTerm}
          />

          {/* Cluster边界图层 - 智能分层显示 */}
          {!hideIndustrialMarkers && selectedCountry === 'USA' && (
            <ClusterBoundaryLayer
              onDataLoad={(data) => {
                // 当高缩放级别时，用详细数据更新marker
                if (data && data.length > 0) {
                  console.log('🔄 Updating markers with detailed data:', data.length);
                  // 这里可以更新markerData状态，但需要避免无限循环
                }
              }}
              onClusterClick={(clusterId, clusterData) => {
                console.log('🎯 Cluster clicked:', clusterId, clusterData);
                setSelectedCluster(clusterId);
                setShowClusterInfo(true);
              }}
              isVisible={true}
            />
          )}

          {/* 使用标记聚合组件 - 根据hideIndustrialMarkers状态决定是否显示 */}
          {!hideIndustrialMarkers && (
            <MarkerClusterComponent
              markers={markerData}
              handleViewDetails={handleViewDetails}
              handleFocusPark={handleFocusPark}
            />
          )}

          {/* 保留单个标记渲染，但设置为不可见，仅用于处理聚焦功能 - 根据hideIndustrialMarkers状态决定是否显示 */}
          {!hideIndustrialMarkers && focusedPark && markerData.map(marker => {
            const isFocused = focusedPark && (
              (focusedPark.park && marker.parkItem.park === focusedPark.park) ||
              (focusedPark.name && marker.parkItem.name === focusedPark.name) ||
              (focusedPark.city && marker.parkItem.city === focusedPark.city)
            );
            if (isFocused) {
              return (
                <MarkerWithHoverCircle
                  key={marker.id}
                  position={marker.position}
                  icon={marker.icon}
                  tooltip={null}
                  popup={null}
                  parkItem={marker.parkItem}
                  isFocused={isFocused}
                  handleViewDetails={handleViewDetails}
                />
              );
            }
            return null;
          })}

          {/* 自定义标记处理组件 */}
          <CustomMarkerHandler
            isActive={isCustomMarkerMode}
            customMarkers={customMarkers}
            setCustomMarkers={setCustomMarkers}
            markerColor="green"
            t={translations[language]}
          />

          {/* 地图图层控制组件 */}
          <MapLayersControl
            visibleLayers={visibleLayers}
            mapStyle={mapStyle}
          />

          {/* 经济热点图层 */}
          <EconomicHotspotLayer
            show={isEconomicHotspotActive}
            mapStyle={mapStyle}
            modelType={economicHotspotSettings.modelType}
            intensity={economicHotspotSettings.intensity}
            industry={economicHotspotSettings.industry}
            radius={economicHotspotSettings.radius}
            country={selectedCountry}
            showLabels={economicHotspotSettings.showLabels}
            showCircles={economicHotspotSettings.showCircles}
            showDetails={economicHotspotSettings.showDetails}
            onSettingsChange={updateEconomicHotspotSettings}
          />

          {/* 能源数据可视化图层 - 仅在美国时显示 */}
          {selectedCountry === 'USA' && (
            <EnergyDataLayer
              show={isEnergyDataActive && !hideEnergyMarkers}
              energyData={energyData}
              energyDataType={energyDataType}
              mapStyle={mapStyle}
              onDataClick={handleEnergyDataClick}
            />
          )}
        </MapContainer>

        {/* 经济热点控制面板 - 不再限制在isEconomicHotspotActive为true时才显示 */}
        {showEconomicControls && (
          <EconomicHotspotControls
            isDarkMode={mapStyle === 'night'}
            onClose={toggleEconomicControls}
            settings={economicHotspotSettings}
            onSettingsChange={updateEconomicHotspotSettings}
            t={translations[language]}
            country={selectedCountry}
          />
        )}

        {/* 经济热点图例 */}
        {isEconomicHotspotActive && (
          <div className="economic-hotspot-ui-container">
            {/* 经济热点设置按钮 - 网页版本 */}
            <div
              className={`settings-toggle-button ${showEconomicControls ? 'active' : ''}`}
              onClick={toggleEconomicControls}
              title={translations[language].adjustEconomicSettings || '调整经济热点设置'}
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.21,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.21,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.67 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z" fill="currentColor"/>
              </svg>
            </div>
            <EconomicHotspotLegend
              isDarkMode={mapStyle === 'night'}
              modelType={economicHotspotSettings.modelType}
              country={economicHotspotSettings.country}
              t={translations[language]}
            />
          </div>
        )}

        {/* Cluster信息面板 - 仅在美国模式下显示 */}
        {selectedCountry === 'USA' && (
          <ClusterInfoPanel
            selectedCluster={selectedCluster}
            clusterStats={clusterSummary?.find(cluster => cluster.cluster_id === selectedCluster)}
            isVisible={showClusterInfo}
            onClose={() => setShowClusterInfo(false)}
            clusterColor={selectedCluster !== null ? ClusterDataService.getClusterColor(selectedCluster) : '#000000'}
          />
        )}
      </div>
    </div>
  );
}

// === 用于捕获并操控地图实例 ===
const MapController = ({
  mapRef,
  selectedCountry,
  targetState,
  thaiProvinceTarget,
  zoomTrigger,
  setSelectedCountry,
  setMapStyle,
  setDisplayMode,
  setSelectedStates,
  setSelectedCities,
  setMinLease,
  setMaxLease,
  setSortOrder,
  setTargetState,
  setThaiProvinceTarget,
  setShowAllCities,
  setCitySearchTerm
}) => {
  const map = useMap();
  const stateRestoredRef = useRef(false); // Tracks if state has been restored in this session
  const initialLoadRef = useRef(false); // Tracks if initial setup (restore or default) is done

  useEffect(() => {
    if (map && mapRef) {
      mapRef.current = map;
    }
  }, [map, mapRef]);

  // Primary Effect for Initial State Restoration or Default View
  useEffect(() => {
    if (map && !initialLoadRef.current) {
      // 检查是否是从详情页面返回
      // 使用更可靠的方法检测是否从详情页面返回
      const hasReturnFlag = sessionStorage.getItem('returningToMainMap') === 'true';
      const isReturningFromDetail = document.referrer.includes('/park/') ||
                                   localStorage.getItem('originalMapState') !== null ||
                                   hasReturnFlag;
      console.log('Is returning from detail page:', isReturningFromDetail,
                 'Referrer:', document.referrer,
                 'Return flag:', hasReturnFlag);

      // 清除返回标记，避免影响下次加载
      if (hasReturnFlag) {
        sessionStorage.removeItem('returningToMainMap');
        console.log('Cleared returningToMainMap flag');
      }
      console.log('Current map center:', map.getCenter(), 'zoom:', map.getZoom());

      if (isReturningFromDetail) {
        // 如果是从详情页面返回，使用originalMapState
        const originalMapStateJson = localStorage.getItem('originalMapState');
        if (originalMapStateJson) {
          try {
            const originalState = JSON.parse(originalMapStateJson);
            console.log('MapController: Restoring original map state:', originalState);

            // 恢复地图视图
            if (originalState.mapCenter && originalState.mapZoom) {
              console.log(`MapController: Setting restored view from originalMapState: center=${originalState.mapCenter}, zoom=${originalState.mapZoom}`);

              // 先恢复其他状态，然后再设置地图视图
              if (originalState.selectedCountry) setSelectedCountry(originalState.selectedCountry);
              if (originalState.mapStyle) setMapStyle(originalState.mapStyle);
              if (originalState.displayMode) setDisplayMode(originalState.displayMode);
              if (originalState.selectedStates) setSelectedStates(originalState.selectedStates);
              if (originalState.selectedCities) setSelectedCities(originalState.selectedCities);
              if (originalState.minLease) setMinLease(originalState.minLease);
              if (originalState.maxLease) setMaxLease(originalState.maxLease);
              if (originalState.sortOrder) setSortOrder(originalState.sortOrder);
              if (originalState.targetState) setTargetState(originalState.targetState);
              if (originalState.thaiProvinceTarget) setThaiProvinceTarget(originalState.thaiProvinceTarget);
              if (originalState.showAllCities !== undefined) setShowAllCities(originalState.showAllCities);
              if (originalState.citySearchTerm) setCitySearchTerm(originalState.citySearchTerm);

              // 使用更长的延迟确保地图已完全初始化和渲染
              setTimeout(() => {
                try {
                  // 先检查当前地图状态
                  console.log('Before setView - Current map center:', map.getCenter(), 'zoom:', map.getZoom());

                  // 标记状态已恢复，避免其他 useEffect 触发额外的缩放操作
                  stateRestoredRef.current = true;

                  // 先使用 setView 立即设置位置，不使用动画
                  map.setView(originalState.mapCenter, originalState.mapZoom, { animate: false });
                  console.log('Map view set immediately with setView');

                  // 然后使用延迟再次确认位置正确
                  setTimeout(() => {
                    // 再次检查地图状态
                    console.log('After setView - Current map center:', map.getCenter(), 'zoom:', map.getZoom());

                    // 如果位置不正确，再次尝试设置
                    const currentCenter = map.getCenter();
                    const currentZoom = map.getZoom();
                    const targetCenter = L.latLng(originalState.mapCenter[0], originalState.mapCenter[1]);

                    // 计算当前位置与目标位置的距离
                    const distance = currentCenter.distanceTo(targetCenter);
                    const zoomDiff = Math.abs(currentZoom - originalState.mapZoom);

                    console.log(`Position check: distance=${distance}m, zoomDiff=${zoomDiff}`);

                    // 如果位置或缩放级别不正确，再次设置
                    if (distance > 1000 || zoomDiff > 0.5) {
                      console.log('Position not correct, setting again');
                      map.setView(originalState.mapCenter, originalState.mapZoom, { animate: false });
                    }
                  }, 500);
                } catch (err) {
                  console.error('Error setting map view:', err);
                  // 如果 setView 失败，尝试 flyTo
                  try {
                    map.flyTo(originalState.mapCenter, originalState.mapZoom, {
                      duration: 0.5,
                      easeLinearity: 0.5
                    });
                    console.log('Fallback to flyTo successful');
                    stateRestoredRef.current = true;
                  } catch (flyToErr) {
                    console.error('Fallback flyTo also failed:', flyToErr);
                  }
                }
              }, 500); // 增加延迟时间
            }

            // 成功恢复后清除originalMapState，避免影响下次加载
            // 延迟清除，确保地图视图已设置
            setTimeout(() => {
              localStorage.removeItem('originalMapState');
              console.log('Removed originalMapState from localStorage');
            }, 1000); // 增加延迟时间

          } catch (error) {
            console.error('MapController: Error restoring original map state:', error);
            // 如果恢复失败，尝试使用普通的mapState
            tryRestoreFromMapState();
          }
        } else {
          // 如果没有originalMapState，尝试从普通的mapState恢复
          tryRestoreFromMapState();
        }
      } else {
        // 如果不是从详情页面返回，使用默认视图
        const countryCoord = COUNTRY_COORDINATES[selectedCountry];
        console.log(`MapController: Setting default view for ${selectedCountry}`);
        map.setView(countryCoord.center, countryCoord.zoom, { animate: false });
      }

      initialLoadRef.current = true; // 标记初始加载已完成
    }
  }, [map, selectedCountry, setSelectedCountry, setMapStyle, setDisplayMode, setSelectedStates, setSelectedCities,
      setMinLease, setMaxLease, setSortOrder, setTargetState, setThaiProvinceTarget, setShowAllCities, setCitySearchTerm]); // 添加所有依赖项

  // 从普通mapState恢复的辅助函数
  const tryRestoreFromMapState = () => {
    const mapStateJson = localStorage.getItem('mapState');
    if (mapStateJson) {
      try {
        const mapState = JSON.parse(mapStateJson);
        console.log('MapController: Restoring from regular map state:', mapState);

        // 恢复地图视图
        if (mapState.mapCenter && mapState.mapZoom) {
          console.log(`MapController: Setting view from mapState: center=${mapState.mapCenter}, zoom=${mapState.mapZoom}`);

          // 先恢复其他状态
          if (mapState.selectedCountry) setSelectedCountry(mapState.selectedCountry);
          if (mapState.mapStyle) setMapStyle(mapState.mapStyle);
          if (mapState.displayMode) setDisplayMode(mapState.displayMode);
          if (mapState.selectedStates) setSelectedStates(mapState.selectedStates);
          if (mapState.selectedCities) setSelectedCities(mapState.selectedCities);
          if (mapState.minLease) setMinLease(mapState.minLease);
          if (mapState.maxLease) setMaxLease(mapState.maxLease);
          if (mapState.sortOrder) setSortOrder(mapState.sortOrder);
          if (mapState.targetState) setTargetState(mapState.targetState);
          if (mapState.thaiProvinceTarget) setThaiProvinceTarget(mapState.thaiProvinceTarget);
          if (mapState.showAllCities !== undefined) setShowAllCities(mapState.showAllCities);
          if (mapState.citySearchTerm) setCitySearchTerm(mapState.citySearchTerm);

          // 使用更长的延迟确保地图已完全初始化
          setTimeout(() => {
            try {
              // 先检查当前地图状态
              console.log('Before setView (mapState) - Current map center:', map.getCenter(), 'zoom:', map.getZoom());

              // 标记状态已恢复，避免其他 useEffect 触发额外的缩放操作
              stateRestoredRef.current = true;

              // 先使用 setView 立即设置位置，不使用动画
              map.setView(mapState.mapCenter, mapState.mapZoom, { animate: false });
              console.log('Map view set immediately with setView from mapState');

              // 然后使用延迟再次确认位置正确
              setTimeout(() => {
                // 再次检查地图状态
                console.log('After setView (mapState) - Current map center:', map.getCenter(), 'zoom:', map.getZoom());

                // 如果位置不正确，再次尝试设置
                const currentCenter = map.getCenter();
                const currentZoom = map.getZoom();
                const targetCenter = L.latLng(mapState.mapCenter[0], mapState.mapCenter[1]);

                // 计算当前位置与目标位置的距离
                const distance = currentCenter.distanceTo(targetCenter);
                const zoomDiff = Math.abs(currentZoom - mapState.mapZoom);

                console.log(`Position check (mapState): distance=${distance}m, zoomDiff=${zoomDiff}`);

                // 如果位置或缩放级别不正确，再次设置
                if (distance > 1000 || zoomDiff > 0.5) {
                  console.log('Position not correct (mapState), setting again');
                  map.setView(mapState.mapCenter, mapState.mapZoom, { animate: false });
                }
              }, 500);
            } catch (err) {
              console.error('Error setting map view from mapState:', err);
              // 如果 setView 失败，尝试 flyTo
              try {
                map.flyTo(mapState.mapCenter, mapState.mapZoom, {
                  duration: 0.5,
                  easeLinearity: 0.5
                });
                console.log('Fallback to flyTo successful from mapState');
                stateRestoredRef.current = true;
              } catch (flyToErr) {
                console.error('Fallback flyTo also failed from mapState:', flyToErr);
              }
            }
          }, 500); // 增加延迟时间
        }
      } catch (error) {
        console.error('MapController: Error restoring from map state:', error);
        // 应用默认视图
        const countryCoord = COUNTRY_COORDINATES[selectedCountry];
        map.setView(countryCoord.center, countryCoord.zoom, { animate: false });
      }
    } else {
      // 没有找到地图状态，应用所选国家的默认视图
      const countryCoord = COUNTRY_COORDINATES[selectedCountry];
      console.log(`MapController: No map state found, setting default view for ${selectedCountry}`);
      map.setView(countryCoord.center, countryCoord.zoom, { animate: false });
    }
  }

  // Effect for handling country changes *after* initial load
  useEffect(() => {
    // 添加检查，确保不是从详情页面返回时才执行
    const hasReturnFlag = sessionStorage.getItem('returningToMainMap') === 'true';
    const isReturningFromDetail = document.referrer.includes('/park/') ||
                               localStorage.getItem('originalMapState') !== null ||
                               hasReturnFlag;

    if (map && initialLoadRef.current && !stateRestoredRef.current && !isReturningFromDetail) {
      // Only fly if state was NOT restored (meaning it's a user action or default load)
      // 且不是从详情页面返回
      const countryCoord = COUNTRY_COORDINATES[selectedCountry];
      console.log(`MapController: Flying to ${selectedCountry} post-load`);
      map.flyTo(countryCoord.center, countryCoord.zoom, { duration: 1.5 });
    }

    // 不要在这里重置 stateRestoredRef，而是在状态恢复后的一段时间后重置
    // 这样可以避免其他 useEffect 触发额外的缩放操作
    if (stateRestoredRef.current) {
      // 使用延迟重置，确保所有缩放操作已完成
      setTimeout(() => {
        console.log('Resetting stateRestoredRef after delay');
        stateRestoredRef.current = false;
      }, 2000); // 延迟2秒重置
    }

  }, [map, selectedCountry]); // Dependency on selectedCountry

  // Effects for handling target state/province changes *after* initial load
  useEffect(() => {
    // 添加检查，确保不是从详情页面返回时才执行
    const hasReturnFlag = sessionStorage.getItem('returningToMainMap') === 'true';
    const isReturningFromDetail = document.referrer.includes('/park/') ||
                               localStorage.getItem('originalMapState') !== null ||
                               hasReturnFlag;

    if (map && initialLoadRef.current && !stateRestoredRef.current && !isReturningFromDetail &&
        targetState && selectedCountry === 'USA') {
      const stateCoord = STATE_COORDINATES[targetState] || STATE_COORDINATES.DEFAULT;
      console.log(`MapController: Flying to US state: ${targetState}`);
      map.flyTo(stateCoord.center, stateCoord.zoom, { duration: 0.8 });
    }
  }, [map, targetState, selectedCountry, zoomTrigger]); // zoomTrigger might be needed if state doesn't change but zoom is desired

  useEffect(() => {
    // 添加检查，确保不是从详情页面返回时才执行
    const hasReturnFlag = sessionStorage.getItem('returningToMainMap') === 'true';
    const isReturningFromDetail = document.referrer.includes('/park/') ||
                               localStorage.getItem('originalMapState') !== null ||
                               hasReturnFlag;

    if (map && initialLoadRef.current && !stateRestoredRef.current && !isReturningFromDetail &&
        thaiProvinceTarget && selectedCountry === 'THAILAND') {
      const provinceCoord = THAILAND_PROVINCE_COORDINATES[thaiProvinceTarget] || THAILAND_PROVINCE_COORDINATES.DEFAULT;
      console.log(`MapController: Flying to Thai province: ${thaiProvinceTarget}`);
      map.flyTo(provinceCoord.center, provinceCoord.zoom, { duration: 0.8 });
    }
  }, [map, thaiProvinceTarget, selectedCountry, zoomTrigger]);

  return null;
};
