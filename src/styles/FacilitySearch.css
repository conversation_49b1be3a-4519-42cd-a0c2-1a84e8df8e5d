/* 设施搜索组件样式 */
.facility-search-container {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
  /* 默认移除背景、阴影、边框 */
  /* background: rgba(255, 255, 255, 0.98); */
  /* box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15); */
  /* border: 1px solid rgba(255, 255, 255, 0.2); */
  /* backdrop-filter: blur(12px); */
  /* -webkit-backdrop-filter: blur(12px); */
  border-radius: 20px; /* 保留圆角 */
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

/* 折叠状态 */
.facility-search-container.collapsed {
  padding: 0; /* 折叠时无内边距 */
  max-height: 44px; /* 调整高度以适应无padding的header */
  width: auto; /* 根据内容自适应，避免裁切 */
  background: none; /* 确保无背景 */
  box-shadow: none; /* 确保无阴影 */
  border: none; /* 确保无边框 */
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
}

/* 展开或有结果时，添加背景和样式 */
.facility-search-container.expanded,
.facility-search-container.with-results {
  padding: 12px;
  width: 360px; /* 放大以匹配目标布局 */
  background: rgba(255, 255, 255, 0.98);
  box-shadow: none; /* 去除强阴影，避免误判为外层卡片 */
  border: 1px solid rgba(255, 255, 255, 0.18);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* 采用欢迎页顶部菜单的玻璃质感：任何模式保持半透明浅色卡片 */
.park-detail-page .facility-search-container.expanded,
.park-detail-page .facility-search-container.with-results {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(0, 0, 0, 0.06) !important;
  box-shadow:
    0 8px 24px rgba(0,0,0,0.08),
    0 1px 0 rgba(255,255,255,0.6) inset;
  backdrop-filter: blur(14px) !important;
  -webkit-backdrop-filter: blur(14px) !important;
}

/* 输入与按钮也统一为浅色卡片风格 */
.park-detail-page .facility-search-form {
  background: rgba(255, 255, 255, 0.92) !important;
  border: 1px solid rgba(0, 0, 0, 0.06) !important;
}

/* 输入框：ParkDetailPage 强制浅色样式（含夜间模式覆盖） */
.park-detail-page .facility-search-input,
.map-night-mode .park-detail-page .facility-search-input {
  background: linear-gradient(180deg, #ffffff, #f6f7f9) !important;
  color: #1f2937 !important;
  border-radius: 18px 0 0 18px;
  border: 1px solid rgba(0,0,0,0.08);
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.06);
}

.park-detail-page .facility-search-input::placeholder,
.map-night-mode .park-detail-page .facility-search-input::placeholder {
  color: rgba(0,0,0,0.45) !important;
}

/* 提交按钮：ParkDetailPage 强制浅色样式（含夜间模式覆盖） */
.park-detail-page .facility-search-button,
.map-night-mode .park-detail-page .facility-search-button {
  background: #2f7af8 !important; /* 更接近截图的蓝 */
  color: #fff !important;
  border: 1px solid rgba(255,255,255,0.9) !important;
  box-shadow: 0 2px 6px rgba(47,122,248,0.4);
  border-radius: 0 18px 18px 0 !important;
}

/* 展开/折叠按钮：白色独立圆按钮 */
.park-detail-page .facility-toggle-button {
  background: linear-gradient(180deg, #ffffff, #f6f7f9) !important;
  border: 1px solid rgba(0,0,0,0.06) !important;
  box-shadow: 0 2px 6px rgba(0,0,0,0.12);
}

/* ParkDetailPage: 任何模式都保持半透明浅色条（覆盖夜间暗色） */
.park-detail-page .facility-search-container {
  background: rgba(255, 255, 255, 0.88) !important;
  border: 1px solid rgba(0, 0, 0, 0.06) !important;
  box-shadow:
    0 8px 24px rgba(0,0,0,0.08),
    0 1px 0 rgba(255,255,255,0.6) inset;
  backdrop-filter: blur(14px) !important;
  -webkit-backdrop-filter: blur(14px) !important;
}

/* 结果面板统一浅色卡片 */
.park-detail-page .facility-search-results,
.park-detail-page .facility-search-container.collapsed .facility-search-results {
  background: rgba(255, 255, 255, 0.98) !important;
  border: 1px solid rgba(0, 0, 0, 0.06) !important;
}

.park-detail-page .results-header {
  background: rgba(248, 250, 252, 0.8) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06) !important;
}


.facility-search-container.expanded {
  max-height: 500px;
}

.facility-search-container.with-results:not(.expanded) {
  max-height: 400px; /* 仅显示结果时的高度 */
  /* 如果希望结果面板在折叠时也有背景，则保留上面的样式 */
  /* 如果希望结果面板在折叠时无背景，则在此处覆盖 */
  /* background: none; */
  /* box-shadow: none; */
}

/* 头部容器 */
.facility-search-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 0;
  padding: 4px 6px;
}

/* 折叠时给header一点padding */
.facility-search-container.collapsed .facility-search-header {
  padding: 4px;
}

.facility-search-form {
  flex-grow: 1;
  display: flex;
  border-radius: 18px;
  overflow: hidden;
  margin-bottom: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0,0,0,0.08);
}

/* 折叠时的独立样式 */
.facility-search-container.collapsed .facility-search-form {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

/* 展开时的样式 */
.facility-search-container.expanded .facility-search-form {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.facility-search-form:focus-within {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

.facility-search-input {
  flex: 1;
  padding: 10px 14px; /* 调整padding */
  border: none;
  font-size: 14px; /* 调整字体大小 */
  outline: none;
  background: transparent;
  color: #333;
}

.facility-search-button {
  background: var(--primary-color, #2f7af8);
  color: white;
  border: none;
  width: 42px; /* 更接近截图的较厚按钮 */
  height: 38px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 0 18px 18px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.facility-search-button:hover {
  background: var(--secondary-color, #1e61e6);
  transform: scale(1.03);
}

.facility-search-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* 展开/折叠按钮 */
.facility-toggle-button {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  color: #555;
}

/* 折叠时的独立样式 */
.facility-search-container.collapsed .facility-toggle-button {
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 展开时的样式 */
.facility-search-container.expanded .facility-toggle-button {
  background: #f8f8f8;
  border: 1px solid #eaeaea;
  box-shadow: none;
}

.facility-toggle-button:hover {
  background: #f0f0f0;
  transform: translateY(-1px);
}

.facility-toggle-button:active {

/* 展开时让下拉箭头翻转 */
.facility-search-container.expanded .facility-toggle-button svg {
  transform: rotate(180deg);
  transition: transform 0.2s ease;
}

  transform: scale(0.95);
}

/* 内容区域，仅在展开时显示和滚动 */
.facility-search-content {
  margin-top: 12px;
  max-height: 350px;
  overflow-y: auto;
  padding-right: 5px;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.facility-search-content::-webkit-scrollbar {
  width: 6px;
}

.facility-search-content::-webkit-scrollbar-track {
  background: transparent;
}

.facility-search-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

/* 半径选择器样式 */
.radius-selector {
  margin: 8px 0 12px;
  padding: 10px;
  background: rgba(240, 240, 240, 0.8); /* 调整背景透明度 */
  border-radius: 10px;
  font-size: 13px;
  color: #333;
  touch-action: none; /* 阻止触摸事件 */
  position: relative;
}

.radius-selector label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
  font-weight: 500;
  cursor: default;
}

.radius-icon {
  font-size: 16px;
}

.radius-slider {
  width: 100%;
  height: 8px;
  -webkit-appearance: none;
  appearance: none;
  background: #d8d8d8; /* 调整滑轨颜色 */
  outline: none;
  border-radius: 4px;
  cursor: pointer;
  touch-action: none; /* 阻止触摸事件 */
}

.radius-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color, #0070f3);
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.radius-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color, #0070f3);
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.radius-slider::-webkit-slider-thumb:hover,
.radius-slider::-moz-range-thumb:hover {
  transform: scale(1.1);
}

/* 设施分类样式 */
.facility-categories {
  margin-top: 8px;
  border-top: 1px solid #f0f0f0;
  padding-top: 10px;
}

.facility-category {
  margin-bottom: 12px;
}

.facility-category h4 {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 600;
  color: #444;
  text-transform: capitalize;
  display: flex;
  align-items: center;
}

.facility-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.facility-type-button {
  background: rgba(245, 245, 245, 0.9); /* 调整按钮背景透明度 */
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 6px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #333;
  display: flex;
  align-items: center;
  gap: 6px;
}

.facility-type-button:hover {
  background: #e8e8e8;
  transform: translateY(-1px);
}

.facility-type-button:active {
  transform: translateY(1px);
}

.facility-type-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.facility-icon {
  font-size: 14px;
}

.facility-search-error {
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(255, 0, 0, 0.1);
  border-left: 3px solid #f44336;
  color: #d32f2f;
  font-size: 13px;
  border-radius: 4px;
}

/* 搜索结果样式 */
.facility-search-results {
  margin-top: 10px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.3s ease;
  max-height: 320px; /* 增加最大高度以适应底部按钮 */
  overflow: hidden; /* 修改为hidden，让内部滚动 */
  display: flex;
  flex-direction: column;
}

/* 折叠时也显示结果列表的背景 */
.facility-search-container.collapsed .facility-search-results {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  margin-top: 8px; /* 调整与header的间距 */
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  position: sticky;
  top: 0;
  z-index: 1;
}

.results-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.close-results-button {
  background: none;
  border: none;
  color: #999;
  font-size: 18px;
  cursor: pointer;
  padding: 0 5px;
  line-height: 1;
  transition: all 0.2s;
}

.close-results-button:hover {
  color: #333;
  transform: scale(1.1);
}

.facility-search-results ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.facility-result-item {
  padding: 12px 15px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
}

.facility-result-item:last-child {
  border-bottom: none;
}

.facility-result-item:hover {
  background: #f9f9f9;
}

.facility-result-item:active {
  background: #f0f0f0;
}

.facility-result-item strong {
  display: block;
  font-size: 14px;
  margin-bottom: 3px;
  color: #333;
}

.facility-result-item small {
  display: block;
  font-size: 12px;
  color: #666;
  margin-top: 3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.result-type {
  display: inline-block;
  font-size: 11px;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 10px;
  color: #555;
  margin-right: 5px;
}

.no-results {
  padding: 15px;
  text-align: center;
  color: #666;
  font-size: 14px;
}

/* 设施弹出窗口样式 */
.facility-popup {
  min-width: 180px;
}

.facility-popup h3 {
  margin: 0 0 5px 0;
  font-size: 15px;
  color: #333;
  font-weight: 600;
}

.facility-popup p {
  margin: 5px 0;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.facility-popup a {
  color: var(--primary-color, #0070f3);
  text-decoration: none;
  display: inline-block;
  margin-top: 3px;
}

.facility-popup a:hover {
  text-decoration: underline;
}

/* 设施标记样式 */
.facility-marker {
  transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.facility-marker:hover {
  transform: scale(1.3);
  z-index: 1000 !important;
}

/* 夜间模式样式支持 */
.map-night-mode .facility-search-container {
  background: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.map-night-mode .facility-search-container.collapsed {
  background: none;
}

.map-night-mode .facility-search-container.expanded,
.map-night-mode .facility-search-container.with-results {
  /* 统一夜间与日间：保持搜索头部和结果为亮色卡片，避免“黑底”误判为外层控件 */
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.map-night-mode .facility-search-container.collapsed .facility-search-form {
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.map-night-mode .facility-search-container.expanded .facility-search-form {
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.map-night-mode .facility-search-input {
  background: transparent;
  color: #f3f4f6;
}

.map-night-mode .facility-search-input::placeholder {
  color: #9ca3af;
}

.map-night-mode .facility-search-input:focus {
  color: #f9fafb;
}

.map-night-mode .facility-search-container.collapsed .facility-toggle-button {
  background: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #d1d5db;
}

.map-night-mode .facility-search-container.expanded .facility-toggle-button {
  background: rgba(55, 65, 81, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #d1d5db;
}

.map-night-mode .facility-toggle-button:hover {
  background: rgba(75, 85, 99, 0.8);
  color: #f3f4f6;
}

.map-night-mode .radius-selector {
  background: rgba(55, 65, 81, 0.8);
  color: #e5e7eb;
}

.map-night-mode .radius-selector label {
  color: #d1d5db;
}

.map-night-mode .facility-categories {
  border-top-color: rgba(255, 255, 255, 0.1);
}

.map-night-mode .facility-category h4 {
  color: #d1d5db;
}

.map-night-mode .facility-type-button {
  background: rgba(55, 65, 81, 0.8);
  border-color: rgba(255, 255, 255, 0.1);
  color: #e5e7eb;
}

.map-night-mode .facility-type-button:hover {
  background: rgba(75, 85, 99, 0.8);
  border-color: rgba(255, 255, 255, 0.2);
  color: #f3f4f6;
}

.map-night-mode .facility-search-error {
  background: rgba(239, 68, 68, 0.1);
  border-left-color: #f87171;
  color: #fca5a5;
}

.map-night-mode .facility-search-results {
  background: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.map-night-mode .facility-search-container.collapsed .facility-search-results {
  background: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.map-night-mode .results-header {
  background-color: rgba(55, 65, 81, 0.8);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.map-night-mode .results-header h3 {
  color: #f3f4f6;
}

.map-night-mode .close-results-button {
  color: #d1d5db;
}

.map-night-mode .close-results-button:hover {
  color: #f3f4f6;
}

.map-night-mode .facility-result-item {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.map-night-mode .facility-result-item:hover {
  background: rgba(55, 65, 81, 0.6);
}

.map-night-mode .facility-result-item:active {
  background: rgba(75, 85, 99, 0.6);
}

.map-night-mode .facility-result-item strong {
  color: #f3f4f6;
}

.map-night-mode .facility-result-item small {
  color: #9ca3af;
}

.map-night-mode .result-type {
  background: rgba(255, 255, 255, 0.1);
  color: #d1d5db;
}

.map-night-mode .no-results {
  color: #9ca3af;
}

.map-night-mode .results-footer {
  background: linear-gradient(to bottom, rgba(31, 41, 55, 0.8), rgba(31, 41, 55, 1));
  border-top-color: rgba(255, 255, 255, 0.1);
}

.map-night-mode .clear-all-button {
  background: #dc2626;
  color: white;
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
}

.map-night-mode .clear-all-button:hover {
  background: #b91c1c;
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
}

.map-night-mode .clear-all-button:active {
  transform: translateY(1px);
  box-shadow: 0 1px 3px rgba(220, 38, 38, 0.3);
}

/* 夜间模式下，搜索头部保持亮色外观（白色输入与蓝色按钮） */
.map-night-mode .facility-search-container .facility-search-form {
  background: rgba(255, 255, 255, 0.98) !important;
  border: 1px solid rgba(0, 0, 0, 0.06) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.map-night-mode .facility-search-container .facility-search-input {
  color: #333 !important; /* 文本深色以便白底可读 */
}

.map-night-mode .facility-search-container .facility-search-input::placeholder {
  color: #9aa0a6 !important;
}

.map-night-mode .facility-search-container.collapsed .facility-toggle-button {
  background: #ffffff !important;
  border: 1px solid rgba(0, 0, 0, 0.05) !important;
  color: #555 !important;
}

.map-night-mode .facility-search-container.expanded .facility-toggle-button {
  background: #f8f9fb !important;
  border: 1px solid rgba(0, 0, 0, 0.06) !important;
  color: #555 !important;
}

/* 确保夜间模式下搜索按钮的正确样式 */
.map-night-mode .facility-search-button {
  background: var(--primary-color, #0070f3);
  color: white;
  border: none;
}

.map-night-mode .facility-search-button:hover {
  background: var(--secondary-color, #0051b3);
}

.map-night-mode .facility-search-button:disabled {
  background: #6b7280;
  cursor: not-allowed;
}

/* 确保夜间模式下滚动条的正确样式 */
.map-night-mode .facility-search-content::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

.map-night-mode .facility-search-results ul::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .facility-search-container {
    width: calc(100% - 20px);
    max-width: 280px;
    right: 10px; /* 调整右边距 */
    top: 10px;   /* 调整上边距 */
  }

  .facility-search-container.collapsed {
    max-height: 40px; /* 调整折叠高度 */
    width: 230px; /* 调整折叠宽度 */
  }

  .facility-search-container.expanded {
    max-height: 70vh;
    width: 280px;
  }

  .facility-search-content {
    max-height: calc(70vh - 55px);
  }

  .facility-buttons {
    gap: 5px;
  }

  .facility-type-button {
    padding: 5px 8px;
    font-size: 11px;
  }

  .facility-icon {
    font-size: 13px;
  }

  .facility-label {
    font-size: 11px;
  }

  .facility-search-input {
    padding: 8px 12px;
    font-size: 13px;
  }

  .facility-search-button {
    width: 36px;
    height: 36px;
    font-size: 15px;
  }

  .facility-toggle-button {
    width: 32px;
    height: 32px;
  }
}

/* 清除按钮样式 */
.clear-search-button {
  background: rgba(255, 59, 48, 0.1);
  color: #ff3b30;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 5px;
  transition: all 0.2s ease;
}

.clear-search-button:hover {
  background: rgba(255, 59, 48, 0.2);
  transform: scale(1.05);
}

.clear-results-button {
  background: rgba(255, 59, 48, 0.1);
  color: #ff3b30;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  margin-right: 5px;
}

.clear-results-button:hover {
  background: rgba(255, 59, 48, 0.2);
  transform: scale(1.05);
}

/* 结果操作按钮组 */
.results-action-buttons {
  display: flex;
  align-items: center;
}

/* 结果容器，允许底部固定按钮 */
.results-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 280px;
  position: relative;
}

.results-container ul {
  flex: 1;
  overflow-y: auto;
  max-height: 230px; /* 留出底部按钮的空间 */
}

/* 结果底部区域 */
.results-footer {
  padding: 10px;
  background: linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,1));
  border-top: 1px solid #eaeaea;
  display: flex;
  justify-content: center;
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 5;
}

/* 底部清除按钮 */
.clear-all-button {
  background: #ff3b30;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(255, 59, 48, 0.3);
  width: 80%;
}

.clear-all-button:hover {
  background: #e0302a;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 59, 48, 0.4);
}

.clear-all-button:active {
  transform: translateY(1px);
  box-shadow: 0 1px 3px rgba(255, 59, 48, 0.3);
}

.clear-icon {
  font-size: 16px;
}