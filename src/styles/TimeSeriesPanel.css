/* 全新现代化设计的时间序列面板 */
.time-series-panel {
  position: absolute;
  top: 0;
  right: 0;
  width: 50vw;
  min-width: 800px;
  max-width: 1200px;
  height: 100vh;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.97), rgba(252, 252, 255, 0.99));
  box-shadow: -5px 0 30px rgba(0, 0, 0, 0.12);
  transform: translateX(110%);
  transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  backdrop-filter: blur(12px);
  border-left: 1px solid rgba(84, 110, 122, 0.1);
}

.time-series-panel.show {
  transform: translateX(0);
}

/* 面板头部 - 统一风格 */
.panel-header {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
}

.panel-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  letter-spacing: 0.5px;
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  color: #666;
  cursor: pointer;
  padding: 6px;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #333;
  transform: rotate(90deg);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden; /* 防止水平滚动 */
  padding: 0;
  display: flex;
  flex-direction: column;
  background: transparent;
}

/* 新的统一控制区域样式 */
.unified-control-section {
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 10px; /* 减少间距 */
  background: white;
  padding: 10px 20px 15px; /* 减少顶部填充 */
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.unified-control-row {
  display: flex;
  flex-direction: column;
  gap: 8px; /* 减少间距 */
}

.unified-section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px; /* 减少底部边距 */
}

/* 图表类型按钮容器 - 对称设计 */
.chart-type-container {
  display: flex;
  width: 100%;
  gap: 10px;
  margin-bottom: 10px; /* 减少底部边距 */
}

.chart-type-button {
  flex: 1;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  background: white;
  color: #333;
  cursor: pointer;
  font-size: 14px;
  font-weight: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.chart-type-button:hover {
  background: #f5f5f5;
  border-color: #d5d5d5;
}

.chart-type-button.active {
  background: #0277bd;
  border-color: #0277bd;
  color: white;
  font-weight: 500;
}

/* 数据类别网格 - 更合理的布局 */
.category-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  width: 100%;
}

.category-button {
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  background: white;
  color: #333;
  cursor: pointer;
  font-size: 13px;
  font-weight: normal;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.category-button:hover {
  background: #f5f5f5;
  border-color: #d5d5d5;
}

.category-button.active {
  background: #0277bd;
  border-color: #0277bd;
  color: white;
  font-weight: 500;
}

.category-icon {
  font-size: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  padding: 4px;
}

.category-label {
  flex: 1;
  text-align: left;
}

.button-icon {
  font-size: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 保留原有的控制按钮样式，但不再使用 */
.control-section {
  display: none;
}

.control-group {
  display: none;
}

.button-group {
  display: none;
}

.control-button {
  display: none;
}

/* 指标选择区域 - 统一风格 */
.metrics-section {
  margin: 0;
  background: white;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.metrics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.metrics-actions {
  display: flex;
  gap: 8px;
}

/* 统一按钮样式 */
.action-button {
  padding: 6px 10px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  background: white;
  color: #333;
  font-size: 12px;
  font-weight: normal;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.2s ease;
}

.action-icon {
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.action-button.clear {
  background: white;
  border-color: rgba(244, 67, 54, 0.3);
  color: #f44336;
}

.action-button:hover {
  background: #f5f5f5;
}

.action-button.clear:hover {
  background: rgba(244, 67, 54, 0.05);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
  gap: 10px;
  margin-top: 5px;
  max-height: 150px;
  overflow-y: auto;
  padding: 5px;
  border-radius: 4px;
  background: white;
  border: 1px solid #e0e0e0;
}

.metric-item {
  padding: 8px 12px;
  background: white;
  border: 1px solid #eeeeee;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: normal;
  color: #333;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.metric-item.active {
  background: #e3f2fd;
  border-color: #90caf9;
  color: #0277bd;
  font-weight: 500;
}

.metric-item:hover {
  background: #f5f5f5;
}

.metric-item input {
  margin-right: 6px;
  accent-color: #0277bd;
  width: 14px;
  height: 14px;
}

/* 时间轴容器设计 - 统一风格 */
.time-slider-container {
  margin: 0;
  padding: 10px 20px; /* 减少上下填充 */
  position: relative;
  background: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative; /* 添加相对定位，确保子元素的绝对定位基于此容器 */
}

.time-slider-container .section-title {
  margin: 0 0 10px 0; /* 减少底部边距 */
}

.time-slider-wrapper {
  position: relative;
  padding: 10px 0 30px 0; /* 移除左右内边距，仅保留上下 */
  width: 100%;
}

/* 滑块轨道设计 */
.time-slider {
  -webkit-appearance: none;
  width: 100%;
  height: 30px;
  background: transparent;
  outline: none;
  opacity: 1;
  transition: opacity 0.2s;
  margin: 0;
  position: relative;
  z-index: 5;
}

.time-slider:before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  height: 4px;
  background: linear-gradient(to right, #0288d1, #26a69a, #f44336);
  border-radius: 4px;
  transform: translateY(-50%);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 滑块按钮设计 */
.time-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0288d1, #00bcd4);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 0 10px rgba(3, 169, 244, 0.4), 0 0 0 5px rgba(3, 169, 244, 0.1);
  position: relative;
  z-index: 10;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.time-slider::-moz-range-thumb {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0288d1, #00bcd4);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 0 10px rgba(3, 169, 244, 0.4), 0 0 0 5px rgba(3, 169, 244, 0.1);
  position: relative;
  z-index: 10;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.time-slider::-webkit-slider-thumb:hover,
.time-slider::-webkit-slider-thumb:active {
  transform: scale(1.15);
  box-shadow: 0 0 15px rgba(3, 169, 244, 0.5), 0 0 0 8px rgba(3, 169, 244, 0.15);
}

.time-slider::-moz-range-thumb:hover,
.time-slider::-moz-range-thumb:active {
  transform: scale(1.15);
  box-shadow: 0 0 15px rgba(3, 169, 244, 0.5), 0 0 0 8px rgba(3, 169, 244, 0.15);
}

/* 时间轴标记点设计 */
.time-ticks {
  position: relative;
  height: 35px; /* 调整高度以使布局更紧凑 */
  width: 100%;
  margin-top: 5px;
  z-index: 2000; /* 进一步增加z-index确保时间轴标记点容器显示在最上层 */
}

.time-tick-with-label {
  position: absolute;
  width: 100%;
  z-index: 2100; /* 增加z-index确保每个标记点和标签的容器显示在最上层 */
}

/* 小球样式 */
.time-tick {
  position: absolute;
  width: 8px; /* 缩小小球大小 */
  height: 8px; /* 缩小小球大小 */
  background: #eceff1;
  border: 1px solid #b0bec5; /* 减小边框 */
  border-radius: 50%;
  top: 10px; /* 调整小球位置 */
  transform: translate(-50%, -50%);
  z-index: 2200; /* 增加z-index确保时间轴小球显示在最上层 */
  transition: all 0.3s ease;
  cursor: pointer;
}

.time-tick:hover {
  transform: translate(-50%, -50%) scale(1.2);
  box-shadow: 0 0 0 4px rgba(3, 169, 244, 0.1);
}

.time-tick.passed {
  background: #0288d1;
  border-color: white;
  box-shadow: 0 0 0 2px rgba(3, 169, 244, 0.15);
}

.time-tick.active {
  background: #f44336;
  border-color: white;
  transform: translate(-50%, -50%) scale(1.3);
  box-shadow: 0 0 0 4px rgba(244, 67, 54, 0.15), 0 0 10px rgba(244, 67, 54, 0.3);
  animation: pulse-tick 2s infinite;
}

@keyframes pulse-tick {
  0% { box-shadow: 0 0 0 4px rgba(244, 67, 54, 0.15), 0 0 10px rgba(244, 67, 54, 0.3); }
  50% { box-shadow: 0 0 0 6px rgba(244, 67, 54, 0.2), 0 0 15px rgba(244, 67, 54, 0.4); }
  100% { box-shadow: 0 0 0 4px rgba(244, 67, 54, 0.15), 0 0 10px rgba(244, 67, 54, 0.3); }
}

/* 时间标签样式 - 改为显示在小球下方 */
.time-tick-label {
  position: absolute;
  top: 18px; /* 调整标签位置使其更靠近小球 */
  transform: translateX(-50%);
  font-size: 9px; /* 缩小字体 */
  font-weight: 500;
  color: #607d8b;
  text-align: center;
  transition: all 0.3s ease;
  white-space: nowrap;
  padding: 0;
  background: transparent;
  border: none;
  z-index: 2300; /* 进一步增加z-index值，确保所有标签始终显示在最上层 */
}

.time-tick-label.active {
  color: #f44336;
  font-weight: 600;
  transform: translateX(-50%) scale(1.05);
  z-index: 2400; /* 进一步增加激活状态标签的z-index，确保它显示在最顶层 */
}

/* 图表区域 - 统一风格 */
.chart-section {
  flex: 1;
  background: white;
  padding: 15px 20px;
  position: relative;
  display: flex;
  flex-direction: column;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.chart-container {
  height: 100%;
  width: 100%;
  min-height: 240px;
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  background: white;
  border: none;
}

.chart-container canvas {
  transition: opacity 0.3s ease;
  position: relative;
  z-index: 2;
}

.empty-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px dashed #e0e0e0;
  border-radius: 4px;
  background: white;
  gap: 15px;
  min-height: 240px;
  padding: 20px;
}

.empty-icon {
  font-size: 42px;
  opacity: 0.15;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-8px); }
  100% { transform: translateY(0px); }
}

.empty-text {
  color: #78909c;
  font-size: 14px;
  text-align: center;
  max-width: 80%;
  line-height: 1.5;
  font-weight: 500;
}

/* 允许图表组件内部的平滑动画 */
.chartjs-render-monitor {
  animation: chartjs-render-animation 0.5s;
}

@keyframes chartjs-render-animation {
  from { opacity: 0.8; }
  to { opacity: 1; }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .time-series-panel {
    width: 100%;
  }

  .metrics-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
  /* width: 6px; */
  display: none; /* 隐藏滚动条 */
}

.panel-content::-webkit-scrollbar-track {
  /* background: rgba(0, 0, 0, 0.02); */
  /* border-radius: 3px; */
}

.panel-content::-webkit-scrollbar-thumb {
  /* background: rgba(3, 169, 244, 0.2); */
  /* border-radius: 3px; */
}

.panel-content::-webkit-scrollbar-thumb:hover {
  /* background: rgba(3, 169, 244, 0.4); */
}