import React from 'react';
import { useMap } from 'react-leaflet';
import '../../styles/MapZoomControl.css';

/**
 * 地图缩放控制组件
 * 提供放大和缩小按钮，与尺子按钮样式一致
 */
const MapZoomControl = ({ t, isDarkMode }) => {
  const map = useMap();

  const handleZoomIn = () => {
    map.zoomIn(1);
  };

  const handleZoomOut = () => {
    map.zoomOut(1);
  };

  return (
    <div className={`map-zoom-control ${isDarkMode ? 'dark-mode' : ''}`}>
      <button 
        className="zoom-button zoom-in" 
        onClick={handleZoomIn}
        title={t?.zoomIn || "放大"}
      >
        +
      </button>
      <button 
        className="zoom-button zoom-out" 
        onClick={handleZoomOut}
        title={t?.zoomOut || "缩小"}
      >
        −
      </button>
    </div>
  );
};

export default MapZoomControl;