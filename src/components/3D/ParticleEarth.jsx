import React, { useMemo, useRef } from 'react';
import * as THREE from 'three';
import { useFrame } from '@react-three/fiber';
import { useTexture } from '@react-three/drei';

/**
 * Lightweight particle-based earth.
 * Props:
 * - scale?: number
 * - position?: [number, number, number]
 * - quality?: 'auto' | 'low' | 'medium' | 'high'
 */
export default function ParticleEarth({
  scale = 1.6,
  position = [2.8, 0, 0],
  quality = 'auto',
  points
}) {
  const pointsRef = useRef();

  const resolvedQuality = useMemo(() => {
    if (quality !== 'auto') return quality;
    if (typeof window === 'undefined') return 'medium';
    const dpr = Math.min(window.devicePixelRatio || 1, 2);
    const cores = (navigator && navigator.hardwareConcurrency) || 4;
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent || ''
    );
    if (isMobile) return 'low';
    if (dpr >= 2 && cores >= 8) return 'high';
    if (cores >= 6) return 'medium';
    return 'low';
  }, [quality]);

  const numPoints = useMemo(() => {
    if (typeof points === 'number' && points > 0) return Math.floor(points);
    switch (resolvedQuality) {
      case 'high':
        return 12000;
      case 'medium':
        return 8000;
      case 'low':
      default:
        return 4500;
    }
  }, [resolvedQuality, points]);

  const earthTexture = useTexture('/textures/earth/earthmap1k.jpg');

  const pointsMaterialProps = useMemo(
    () => ({
      vertexColors: true,
      transparent: true,
      opacity: 0.85,
      sizeAttenuation: true,
      depthWrite: false,
      size: 0.012
    }),
    []
  );

  const [particlePositions, particleColors] = useMemo(() => {
    const positions = new Float32Array(numPoints * 3);
    const colors = new Float32Array(numPoints * 3);
    const tempCanvas = typeof document !== 'undefined' ? document.createElement('canvas') : null;
    const ctx = tempCanvas ? tempCanvas.getContext('2d') : null;
    const radius = 1;

    if (earthTexture && earthTexture.image && ctx && tempCanvas) {
      tempCanvas.width = earthTexture.image.width;
      tempCanvas.height = earthTexture.image.height;
      ctx.drawImage(earthTexture.image, 0, 0);

      const imageData = ctx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
      const data = imageData.data;

      let particleCount = 0;

      for (let i = 0; particleCount < numPoints && i < numPoints * 4; i++) {
        const phi = Math.acos(-1 + 2 * Math.random());
        const theta = Math.random() * Math.PI * 2;

        const x = radius * Math.sin(phi) * Math.cos(theta);
        const y = radius * Math.sin(phi) * Math.sin(theta);
        const z = radius * Math.cos(phi);

        const u = 0.5 + Math.atan2(x, z) / (2 * Math.PI);
        const v = 0.5 - Math.asin(y) / Math.PI;

        const col = Math.floor(u * tempCanvas.width);
        const row = Math.floor(v * tempCanvas.height);
        const pixelIndex = (row * tempCanvas.width + col) * 4;

        const r = data[pixelIndex] / 255;
        const g = data[pixelIndex + 1] / 255;
        const b = data[pixelIndex + 2] / 255;
        const brightness = (r + g + b) / 3;

        const blueness = b - (r + g) / 2;
        const isOcean = blueness > 0.1 && brightness < 0.7;

        const threshold = 0.3;
        if (brightness > threshold || Math.random() > 0.95) {
          positions[particleCount * 3] = x;
          positions[particleCount * 3 + 1] = y;
          positions[particleCount * 3 + 2] = z;

          if (isOcean) {
            colors[particleCount * 3] = Math.max(0.1, r * 0.4);
            colors[particleCount * 3 + 1] = Math.max(0.3, g * 0.6);
            colors[particleCount * 3 + 2] = Math.max(0.6, b * 0.9 + 0.1);
          } else {
            colors[particleCount * 3] = Math.min(1.0, r * 1.1);
            colors[particleCount * 3 + 1] = Math.min(1.0, g * 1.1);
            colors[particleCount * 3 + 2] = Math.min(1.0, b * 0.9 + 0.1);
          }

          particleCount++;
        }
      }

      while (particleCount < numPoints) {
        const phi = Math.acos(-1 + 2 * Math.random());
        const theta = Math.random() * Math.PI * 2;
        positions[particleCount * 3] = radius * Math.sin(phi) * Math.cos(theta);
        positions[particleCount * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
        positions[particleCount * 3 + 2] = radius * Math.cos(phi);
        colors[particleCount * 3] = 0.8;
        colors[particleCount * 3 + 1] = 0.9;
        colors[particleCount * 3 + 2] = 1.0;
        particleCount++;
      }
    } else {
      for (let i = 0; i < numPoints; i++) {
        const phi = Math.acos(-1 + 2 * Math.random());
        const theta = Math.random() * Math.PI * 2;
        positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
        positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
        positions[i * 3 + 2] = radius * Math.cos(phi);
        colors[i * 3] = 0.8;
        colors[i * 3 + 1] = 0.9;
        colors[i * 3 + 2] = 1.0;
      }
    }

    return [positions, colors];
  }, [numPoints, earthTexture]);

  useFrame(({ clock }) => {
    const elapsedTime = clock.getElapsedTime();
    if (pointsRef.current) {
      pointsRef.current.rotation.y = elapsedTime * 0.05;
      pointsRef.current.rotation.x = Math.sin(elapsedTime * 0.2) * 0.03;
      pointsRef.current.rotation.z = Math.cos(elapsedTime * 0.15) * 0.015;
    }
  });

  return (
    <group position={position} scale={[scale, scale, scale]} rotation={[0, 0, 0]}>
      <points ref={pointsRef} frustumCulled>
        <bufferGeometry>
          <bufferAttribute
            attach="attributes-position"
            count={numPoints}
            array={particlePositions}
            itemSize={3}
          />
          <bufferAttribute
            attach="attributes-color"
            count={numPoints}
            array={particleColors}
            itemSize={3}
          />
        </bufferGeometry>
        <pointsMaterial {...pointsMaterialProps} />
      </points>

      <mesh scale={[1.01, 1.01, 1.01]}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial color="#0a4677" transparent opacity={0.03} side={THREE.BackSide} />
      </mesh>
    </group>
  );
}


