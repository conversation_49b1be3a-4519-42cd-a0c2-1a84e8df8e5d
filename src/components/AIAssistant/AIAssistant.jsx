import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ragApiService from '@services/ragApi';
import { But<PERSON>, Card, Badge } from '../ui';
import { useLanguage } from '../../contexts/LanguageContext';
import { getTranslation } from '../../utils/translationHelper';
import {
  MessageSquare,
  Target,
  TrendingUp,
  BarChart3,
  Lightbulb,
  Sparkles,
  ArrowRight,
  Send,
  X,
  Minimize2,
  RotateCcw,
  Trash2,
  Hammer,
  MoreVertical,
  Link,
  Unlink,
  Plus,
  Globe,
  WifiOff,
} from 'lucide-react';
import './AIAssistant.css';

// 创意入口按钮组件
const CreativeAIButton = ({ onClick, showTooltip = true }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className="ai-button-container"
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="ai-button-wrapper">
        {/* 背景容器 */}
        <div className="ai-button-background" />

        {/* SVG 动画 */}
        <svg width="64" height="48" viewBox="0 0 64 48" className="ai-button-svg">
          {/* 默认状态：间歇性弹跳的虚线图案 */}
          <g className={`default-state ${isHovered ? "hidden" : "visible"}`}>
            {/* 主要的虚线路径 */}
            <path d="M8 24 L20 12 L32 24 L44 12 L56 24" className="main-dash-path" />

            {/* 辅助虚线 */}
            <path d="M12 36 L24 28 L36 36 L48 28" className="secondary-dash-path" />

            {/* 连接点 */}
            <circle cx="32" cy="24" r="2" className="connection-point" />
          </g>

          {/* 悬停状态：AI 字样绘制动画 */}
          <g className={`hover-state ${isHovered ? "visible" : "hidden"}`}>
            {/* A 字母 */}
            <path d="M12 38 L20 14 L28 38 M16 30 L24 30" className={`letter-a ${isHovered ? "animate" : ""}`} />

            {/* I 字母 */}
            <path d="M36 14 L48 14 M42 14 L42 38 M36 38 L48 38" className={`letter-i ${isHovered ? "animate" : ""}`} />

            {/* 装饰性的连接线 */}
            <path d="M30 26 L34 26" className={`connector-line ${isHovered ? "animate" : ""}`} />
          </g>
        </svg>

        {/* 悬停时显示的文本提示 - 只在showTooltip为true时显示 */}
        {showTooltip && (
          <div className={`tooltip ${isHovered ? "visible" : "hidden"}`}>
            <div className="tooltip-content">AI 智能助手</div>
            <div className="tooltip-arrow" />
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * AI 智能助手组件
 * 提供节点式可视化聊天界面，支持流式问答和现代玻璃拟态设计
 */
const AIAssistant = () => {
  const { language } = useLanguage();
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeNode, setActiveNode] = useState(null);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [status, setStatus] = useState(null);
  const [isBuilding, setIsBuilding] = useState(false);
  const [selectedNodes, setSelectedNodes] = useState([]); // 用于连接的选中节点
  const [connectionMode, setConnectionMode] = useState(false); // 连接模式
  const [nodeMenuOpen, setNodeMenuOpen] = useState(null); // 节点菜单
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 }); // 菜单位置
  const [linkMenuOpen, setLinkMenuOpen] = useState(false); // 链接按钮菜单
  const [webSearchEnabled, setWebSearchEnabled] = useState(false); // 网络搜索开关
  
  const [dragState, setDragState] = useState({
    isDragging: false,
    nodeId: null,
    startX: 0,
    startY: 0,
    offsetX: 0,
    offsetY: 0,
  });
  
  // 语言检测函数
  const detectLanguage = useCallback((text) => {
    // 简单的中文检测：如果包含中文字符，则认为是中文
    const chineseRegex = /[\u4e00-\u9fff]/;
    return chineseRegex.test(text) ? 'zh' : 'en';
  }, []);

  // 获取翻译文本的辅助函数
  const t = useCallback((key) => getTranslation(key, language), [language]);

  const [nodes, setNodes] = useState([
    {
      id: "welcome-1",
      type: "insight",
      title: getTranslation('welcome', language),
      content: getTranslation('welcomeMessage', language),
      x: 30,
      y: 25,
      connections: [],
      zIndex: 1,
    }
  ]);

  const containerRef = useRef(null);
  const nextZIndex = useRef(2);
  const cancelStreamRef = useRef(null);

  // 初始化组件
  useEffect(() => {
    loadSuggestions();
    checkStatus();
  }, []);

  // 监听语言变化，更新欢迎节点
  useEffect(() => {
    setNodes(prev => prev.map(node => 
      node.id === "welcome-1" 
        ? {
            ...node,
            title: t('welcome'),
            content: t('welcomeMessage')
          }
        : node
    ));
  }, [language, t]);

  // 添加全局事件监听
  useEffect(() => {
    if (dragState.isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [dragState.isDragging]);

  // 计算菜单位置，避免超出边界
  const calculateMenuPosition = useCallback((buttonElement) => {
    const buttonRect = buttonElement.getBoundingClientRect();
    const menuWidth = 180; // 菜单宽度
    const menuHeight = 200; // 估计菜单高度
    const padding = 8; // 边距

    let left = buttonRect.left - menuWidth - padding;
    let top = buttonRect.top;

    // 如果左侧空间不够，显示在右侧
    if (left < padding) {
      left = buttonRect.right + padding;
    }

    // 如果右侧也超出，居中显示
    if (left + menuWidth > window.innerWidth - padding) {
      left = Math.max(padding, (window.innerWidth - menuWidth) / 2);
    }

    // 垂直位置调整
    if (top + menuHeight > window.innerHeight - padding) {
      top = Math.max(padding, window.innerHeight - menuHeight - padding);
    }

    return { top, left };
  }, []);

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (e) => {
      if (!e.target.closest('.node-menu') && !e.target.closest('.node-menu-button')) {
        setNodeMenuOpen(null);
      }
      if (!e.target.closest('.link-dropdown-menu') && !e.target.closest('.control-button-wrapper')) {
        setLinkMenuOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  // 性能优化：使用 useMemo 缓存节点样式计算
  const nodeStyles = useMemo(() => {
    return nodes.reduce((acc, node) => {
      acc[node.id] = {
        left: `${node.x}%`,
        top: `${node.y}%`,
        zIndex: node.zIndex || 1,
        transform: "translate(-50%, -50%)",
      };
      return acc;
    }, {});
  }, [nodes]);

  /**
   * 加载建议问题
   */
  const loadSuggestions = async () => {
    try {
      const suggestionsData = await ragApiService.getSuggestions();
      setSuggestions(suggestionsData);
    } catch (error) {
      console.error('加载建议问题失败:', error);
    }
  };

  /**
   * 检查服务状态
   */
  const checkStatus = async () => {
    try {
      const statusData = await ragApiService.getStatus();
      setStatus(statusData);
    } catch (error) {
      console.error('检查服务状态失败:', error);
      setStatus({ initialized: false, error: error.message });
    }
  };

  /**
   * 智能布局算法：避免节点重叠
   */
  const findOptimalPosition = useCallback((existingNodes) => {
    const attempts = 50;
    const minDistance = 25; // 最小距离百分比

    for (let i = 0; i < attempts; i++) {
      const x = Math.random() * 70 + 15; // 15-85%
      const y = Math.random() * 50 + 20; // 20-70%

      const hasOverlap = existingNodes.some((node) => {
        const distance = Math.sqrt(Math.pow(node.x - x, 2) + Math.pow(node.y - y, 2));
        return distance < minDistance;
      });

      if (!hasOverlap) {
        return { x, y };
      }
    }

    // 如果找不到合适位置，使用螺旋布局
    const angle = existingNodes.length * 0.5;
    const radius = 20 + existingNodes.length * 3;
    return {
      x: 50 + Math.cos(angle) * radius,
      y: 50 + Math.sin(angle) * radius,
    };
  }, []);

  /**
   * 获取节点图标
   */
  const getNodeIcon = useCallback((type) => {
    switch (type) {
      case "insight":
        return <Lightbulb className="node-icon" />;
      case "action":
        return <Target className="node-icon" />;
      case "data":
        return <BarChart3 className="node-icon" />;
      case "suggestion":
        return <TrendingUp className="node-icon" />;
      default:
        return <MessageSquare className="node-icon" />;
    }
  }, []);

  /**
   * 获取节点类型样式类
   */
  const getNodeTypeClass = useCallback((type) => {
    return `node-${type}`;
  }, []);

  /**
   * 发送消息并创建响应节点
   */
  const sendMessage = async (question = inputValue.trim()) => {
    if (!question || isLoading) return;

    // 将中文输入翻译为英文（如果需要）
    const processedQuestion = question;

    setIsLoading(true);
    setInputValue('');

    // 检测用户输入语言，用于决定响应语言
    const userLanguage = detectLanguage(question);
    
    // 创建用户问题节点
    const userPosition = findOptimalPosition(nodes);
    const userNode = {
      id: `user-${Date.now()}`,
      type: "action",
      title: t('userInquiry'),
      content: processedQuestion.length > 50 ? `${processedQuestion.slice(0, 50)}...` : processedQuestion,
      x: userPosition.x,
      y: userPosition.y,
      zIndex: nextZIndex.current++,
      userLanguage, // 保存用户语言偏好
    };

    setNodes(prev => [...prev, userNode]);

    // 创建AI响应节点
    const aiPosition = findOptimalPosition([...nodes, userNode]);
    const aiNodeId = `ai-${Date.now()}`;
    const aiNode = {
      id: aiNodeId,
      type: "insight",
      title: t('aiAnalyzing'),
      content: "",
      isStreaming: true,
      x: aiPosition.x,
      y: aiPosition.y,
      zIndex: nextZIndex.current++,
      connections: [userNode.id],
      responseLanguage: userLanguage, // 响应语言跟随用户输入
    };

    setNodes(prev => [...prev, aiNode]);
    setActiveNode(aiNodeId);

    try {
      // 使用流式问答
      const requestOptions = {
        question: processedQuestion,
        language: userLanguage,
        responseLanguage: userLanguage,
        enableWebSearch: webSearchEnabled // 添加网络搜索开关
      };

      cancelStreamRef.current = ragApiService.askQuestionStream(
        requestOptions,
        (data) => {
          // 处理流式消息
          if (data.type === 'content') {
            setNodes(prev => prev.map(node => 
              node.id === aiNodeId 
                ? { 
                    ...node, 
                    title: t('aiAnalysisResult'),
                    content: data.content.length > 100 
                      ? `${data.content.slice(0, 100)}...` 
                      : data.content,
                    fullContent: data.content,
                    isStreaming: !data.isComplete 
                  }
                : node
            ));
          } else if (data.type === 'sources') {
            setNodes(prev => prev.map(node => 
              node.id === aiNodeId 
                ? { ...node, sources: data.data }
                : node
            ));
          }
        },
        (error) => {
          console.error('流式问答错误:', error);
          setNodes(prev => prev.map(node => 
            node.id === aiNodeId 
              ? { 
                  ...node, 
                  title: t('analysisFailed'),
                  content: t('sorryEncounteredProblem'), 
                  isStreaming: false,
                  isError: true
                }
              : node
          ));
          setIsLoading(false);
        },
        () => {
          // 完成回调
          setIsLoading(false);
        }
      );

    } catch (error) {
      console.error('发送消息失败:', error);
      setNodes(prev => prev.map(node => 
        node.id === aiNodeId 
          ? { 
              ...node, 
              title: t('sendFailed'),
              content: t('sorryEncounteredProblem'), 
              isStreaming: false,
              isError: true
            }
          : node
      ));
      setIsLoading(false);
    }
  };

  /**
   * 拖拽处理
   */
  const handleMouseDown = useCallback((e, nodeId) => {
    e.preventDefault();
    e.stopPropagation();

    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;

    const node = nodes.find((n) => n.id === nodeId);
    if (!node) return;

    // 提升被拖拽节点的层级
    setNodes((prev) => prev.map((n) => (n.id === nodeId ? { ...n, zIndex: nextZIndex.current++ } : n)));

    setDragState({
      isDragging: true,
      nodeId,
      startX: e.clientX,
      startY: e.clientY,
      offsetX: (node.x / 100) * rect.width - e.clientX + rect.left,
      offsetY: (node.y / 100) * rect.height - e.clientY + rect.top,
    });
  }, [nodes]);

  const handleMouseMove = useCallback((e) => {
    if (!dragState.isDragging || !dragState.nodeId || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = ((e.clientX - rect.left + dragState.offsetX) / rect.width) * 100;
    const y = ((e.clientY - rect.top + dragState.offsetY) / rect.height) * 100;

    // 限制在容器范围内
    const clampedX = Math.max(5, Math.min(95, x));
    const clampedY = Math.max(5, Math.min(85, y));

    setNodes((prev) =>
      prev.map((node) => (node.id === dragState.nodeId ? { ...node, x: clampedX, y: clampedY } : node)),
    );
  }, [dragState]);

  const handleMouseUp = useCallback(() => {
    setDragState({
      isDragging: false,
      nodeId: null,
      startX: 0,
      startY: 0,
      offsetX: 0,
      offsetY: 0,
    });
  }, []);

  /**
   * 键盘事件处理
   */
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  /**
   * 选择建议问题
   */
  const selectSuggestion = (question) => {
    setInputValue(question);
    sendMessage(question);
  };

  /**
   * 构建知识库
   */
  const buildKnowledgeBase = async () => {
    setIsBuilding(true);
    try {
      const result = await ragApiService.buildKnowledgeBase();
      if (result.success) {
        const position = findOptimalPosition(nodes);
        const systemNode = {
          id: `system-${Date.now()}`,
          type: "data",
          title: t('knowledgeBaseConstruction'),
          content: t('knowledgeBaseBuildSuccess'),
          x: position.x,
          y: position.y,
          zIndex: nextZIndex.current++,
        };
        setNodes(prev => [...prev, systemNode]);
        
        setTimeout(() => {
          checkStatus();
        }, 1000);
      } else {
        throw new Error(result.message || '构建失败');
      }
    } catch (error) {
      console.error('知识库构建失败:', error);
      const position = findOptimalPosition(nodes);
      const errorNode = {
        id: `error-${Date.now()}`,
        type: "suggestion",
        title: t('constructionFailed'),
        content: `${t('knowledgeBaseBuildFailed')} ${error.message}`,
        x: position.x,
        y: position.y,
        zIndex: nextZIndex.current++,
        isError: true
      };
      setNodes(prev => [...prev, errorNode]);
    } finally {
      setIsBuilding(false);
    }
  };

  /**
   * 清空所有节点
   */
  const clearAllNodes = useCallback(() => {
    setNodes([]);
    setActiveNode(null);
    if (cancelStreamRef.current) {
      cancelStreamRef.current();
    }
  }, []);

  /**
   * 自动排列节点
   */
  const autoArrange = useCallback(() => {
    const arranged = nodes.map((node, index) => {
      const angle = (index / nodes.length) * 2 * Math.PI;
      const radius = Math.min(30, 15 + nodes.length * 2);
      return {
        ...node,
        x: 50 + Math.cos(angle) * radius,
        y: 50 + Math.sin(angle) * radius,
      };
    });
    setNodes(arranged);
  }, [nodes]);

  /**
   * 展开节点详细内容
   */
  const expandNode = useCallback((nodeId) => {
    setNodes(prev => prev.map(node => 
      node.id === nodeId 
        ? { ...node, isExpanded: true, zIndex: nextZIndex.current++ }
        : { ...node, isExpanded: false } // 关闭其他节点
    ));
  }, []);

  /**
   * 收起节点详细内容
   */
  const collapseNode = useCallback((nodeId) => {
    setNodes(prev => prev.map(node => 
      node.id === nodeId 
        ? { ...node, isExpanded: false }
        : node
    ));
  }, []);

  /**
   * 删除节点
   */
  const deleteNode = useCallback((nodeId) => {
    setNodes(prev => {
      const newNodes = prev.filter(node => node.id !== nodeId);
      // 同时删除指向这个节点的连接
      return newNodes.map(node => ({
        ...node,
        connections: node.connections ? node.connections.filter(id => id !== nodeId) : []
      }));
    });
    setNodeMenuOpen(null);
    setActiveNode(null);
  }, []);

  /**
   * 切换连接模式
   */
  const toggleConnectionMode = useCallback(() => {
    setConnectionMode(prev => !prev);
    setSelectedNodes([]);
    setNodeMenuOpen(null);
  }, []);

  /**
   * 选择节点用于连接
   */
  const selectNodeForConnection = useCallback((nodeId) => {
    if (!connectionMode) return;
    
    setSelectedNodes(prev => {
      if (prev.includes(nodeId)) {
        return prev.filter(id => id !== nodeId);
      } else if (prev.length < 2) {
        const newSelected = [...prev, nodeId];
        
        // 如果选择了两个节点，创建连接
        if (newSelected.length === 2) {
          const [sourceId, targetId] = newSelected;
          setNodes(prevNodes => prevNodes.map(node => 
            node.id === sourceId 
              ? { 
                  ...node, 
                  connections: node.connections 
                    ? [...new Set([...node.connections, targetId])]
                    : [targetId]
                }
              : node
          ));
          return []; // 清空选择
        }
        
        return newSelected;
      }
      return prev;
    });
  }, [connectionMode]);

  /**
   * 删除连接
   */
  const removeConnection = useCallback((sourceId, targetId) => {
    setNodes(prev => prev.map(node => 
      node.id === sourceId 
        ? { 
            ...node, 
            connections: node.connections ? node.connections.filter(id => id !== targetId) : []
          }
        : node
    ));
  }, []);

  /**
   * 创建新的空白节点
   */
  const createBlankNode = useCallback(() => {
    const position = findOptimalPosition(nodes);
    const newNode = {
      id: `blank-${Date.now()}`,
      type: "insight",
      title: t('blankNode'),
      content: t('clickToEdit'),
      x: position.x,
      y: position.y,
      zIndex: nextZIndex.current++,
      connections: [],
      isEditable: true
    };
    setNodes(prev => [...prev, newNode]);
  }, [nodes, findOptimalPosition, t]);

  // 如果未展开，显示创意按钮（不显示tooltip）
  if (!isExpanded) {
    return <CreativeAIButton onClick={() => setIsExpanded(true)} showTooltip={false} />;
  }

  return (
    <div className="ai-assistant-overlay">
      {/* 浮动控制按钮 */}
      <div className="control-buttons">
        <Button 
          variant="ghost" 
          size="icon" 
          className="control-button" 
          onClick={createBlankNode} 
          title={t('createNode')}
        >
          <Plus className="control-icon" />
        </Button>
        <div className="control-button-wrapper">
          <Button
            variant="ghost"
            size="icon"
            className={`control-button ${connectionMode ? 'active' : ''} ${linkMenuOpen ? 'menu-open' : ''}`}
            onClick={() => setLinkMenuOpen(!linkMenuOpen)}
            title={t('connectionOptions')}
          >
            <Link className="control-icon" />
          </Button>

          {/* 链接按钮下拉菜单 */}
          {linkMenuOpen && (
            <div className="link-dropdown-menu">
              <div className="menu-header">
                <Link className="menu-header-icon" />
                <span className="menu-title">{t('connectionMode')}</span>
              </div>
              <div className="menu-content">
                <button
                  className={`menu-option ${connectionMode ? 'active' : ''}`}
                  onClick={() => {
                    toggleConnectionMode();
                    setLinkMenuOpen(false);
                  }}
                >
                  <div className="option-info">
                    <div className="option-title">
                      {connectionMode ? t('exitConnectionMode') : t('enterConnectionMode')}
                    </div>
                    <div className="option-description">
                      {connectionMode
                        ? t('exitConnectionModeDesc')
                        : t('enterConnectionModeDesc')
                      }
                    </div>
                  </div>
                  <div className="option-toggle">
                    <div className={`toggle-switch ${connectionMode ? 'on' : 'off'}`}>
                      <div className="toggle-handle"></div>
                    </div>
                  </div>
                </button>

                {connectionMode && (
                  <div className="connection-status">
                    <div className="status-info">
                      <span className="status-label">{t('selectedNodes')}:</span>
                      <span className="status-value">{selectedNodes.length}/2</span>
                    </div>
                    {selectedNodes.length === 1 && (
                      <div className="status-hint">{t('selectSecondNode')}</div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
        <Button variant="ghost" size="icon" className="control-button" onClick={autoArrange} title={t('autoArrangeNodes')}>
          <RotateCcw className="control-icon" />
        </Button>
        <Button variant="ghost" size="icon" className="control-button" onClick={clearAllNodes} title={t('clearAllNodes')}>
          <Trash2 className="control-icon" />
        </Button>
        {status?.documentCount === 0 && !isBuilding && (
          <div className="build-kb-container">
            <Button 
              variant="ghost" 
              size="icon" 
              className="control-button build-control"
              onClick={buildKnowledgeBase}
              disabled={isBuilding}
            >
              <Hammer className="control-icon" />
            </Button>
            <div className="build-kb-tooltip">
              <div className="tooltip-title">{t('buildKnowledgeBase')}</div>
              <div className="tooltip-description">{t('buildKnowledgeBaseDescription')}</div>
              <div className="tooltip-benefits">{t('buildKnowledgeBaseBenefits')}</div>
            </div>
          </div>
        )}
        {status?.documentCount > 0 && !isBuilding && (
          <div className="kb-status-indicator control-button" title={t('knowledgeBaseReady')}>
            <div className="kb-ready-icon">✓</div>
          </div>
        )}
        {isBuilding && (
          <div className="building-indicator control-button" title={t('buildingKnowledgeBase')}>
            <div className="modern-spinner">
              <div className="spinner-ring"></div>
            </div>
          </div>
        )}
        <Button variant="ghost" size="icon" className="control-button" onClick={() => setIsExpanded(false)}>
          <X className="control-icon" />
        </Button>
      </div>



      {/* 节点计数器 */}
      {nodes.length > 0 && (
        <div className="node-counter">
          <Badge className="counter-badge">{nodes.length} {t('nodeCount')}</Badge>
        </div>
      )}

      {/* 主内容区域 */}
      <div className="main-content">
        {/* 背景图案 */}
        <div className="background-pattern">
          <div className="gradient-overlay" />
          <div className="dot-pattern" />
        </div>

        {/* 交互式节点画布 */}
        <div ref={containerRef} className="node-canvas">
          {/* 连接线 */}
          <svg className="connection-svg">
            {nodes.map((node) =>
              node.connections?.map((connectionId) => {
                const targetNode = nodes.find((n) => n.id === connectionId);
                if (!targetNode) return null;

                const lineId = `${node.id}-${connectionId}`;
                const midX = (node.x + targetNode.x) / 2;
                const midY = (node.y + targetNode.y) / 2;

                return (
                  <g key={lineId}>
                    <line
                      x1={`${node.x}%`}
                      y1={`${node.y}%`}
                      x2={`${targetNode.x}%`}
                      y2={`${targetNode.y}%`}
                      className={`connection-line ${connectionMode ? 'interactive' : ''}`}
                    />
                    {/* 连接线删除按钮 (仅在连接模式下显示) */}
                    {connectionMode && (
                      <g 
                        className="connection-delete-button"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeConnection(node.id, connectionId);
                        }}
                      >
                        <circle
                          cx={`${midX}%`}
                          cy={`${midY}%`}
                          r="8"
                          className="delete-button-bg"
                        />
                        <text
                          x={`${midX}%`}
                          y={`${midY}%`}
                          className="delete-button-text"
                          textAnchor="middle"
                          dominantBaseline="central"
                        >
                          ×
                        </text>
                      </g>
                    )}
                  </g>
                );
              }),
            )}
          </svg>

          {/* AI 节点 */}
          {nodes.map((node) => (
            <Card
              key={node.id}
              className={`ai-node ${getNodeTypeClass(node.type)} ${
                activeNode === node.id ? "active" : ""
              } ${dragState.nodeId === node.id ? "dragging" : ""} ${node.isError ? "error" : ""} ${
                node.isExpanded ? "expanded" : ""
              } ${selectedNodes.includes(node.id) ? "selected-for-connection" : ""}`}
              style={nodeStyles[node.id]}
              onMouseDown={(e) => handleMouseDown(e, node.id)}
              onClick={(e) => {
                e.stopPropagation();
                if (connectionMode) {
                  selectNodeForConnection(node.id);
                } else {
                  setActiveNode(activeNode === node.id ? null : node.id);
                }
              }}
            >
              <div className="node-content">
                <div className="node-icon-wrapper">{getNodeIcon(node.type)}</div>
                <div className="node-text">
                  <div className="node-header">
                    <Badge variant="secondary" className="node-badge">
                      {node.type}
                    </Badge>
                    <div className="node-header-actions">
                      <Sparkles className="sparkle-icon" />
                      {/* 节点操作菜单按钮 */}
                  <button
                        className="node-menu-button"
                        onClick={(e) => {
                          e.stopPropagation();
                          if (nodeMenuOpen === node.id) {
                            setNodeMenuOpen(null);
                          } else {
                            const position = calculateMenuPosition(e.currentTarget);
                            setMenuPosition(position);
                            setNodeMenuOpen(node.id);
                          }
                        }}
                        title={t('nodeActions')}
                      >
                        <MoreVertical className="menu-icon" />
                  </button>
                    </div>
                  </div>
                  <h3 className="node-title">
                    {node.title}
                    {node.isStreaming && <span className="typing-indicator">▋</span>}
                  </h3>
                  <p className="node-description">{node.content}</p>
                </div>

                {/* 连接模式选择指示器 */}
                {connectionMode && (
                  <div className={`connection-indicator ${selectedNodes.includes(node.id) ? 'selected' : ''}`}>
                    {selectedNodes.includes(node.id) ? '✓' : selectedNodes.length + 1}
                  </div>
                )}

                {/* 节点操作菜单 */}
                {nodeMenuOpen === node.id && (
                  <div
                    className="node-menu"
                    style={{
                      top: `${menuPosition.top}px`,
                      left: `${menuPosition.left}px`
                    }}
                  >
                <button 
                      className="menu-item"
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteNode(node.id);
                      }}
                    >
                      <Trash2 className="menu-item-icon" />
                      {t('deleteNode')}
                </button>
                <button 
                      className="menu-item"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleConnectionMode();
                      }}
                    >
                      <Link className="menu-item-icon" />
                      {connectionMode ? t('exitConnectionMode') : t('manageConnections')}
                    </button>
                    {node.connections && node.connections.length > 0 && (
                      <>
                        <div className="menu-divider"></div>
                        <div className="menu-section-title">{t('removeConnections')}</div>
                        {node.connections.map(connectionId => {
                          const targetNode = nodes.find(n => n.id === connectionId);
                          return targetNode ? (
                            <button
                              key={connectionId}
                              className="menu-item connection-item"
                              onClick={(e) => {
                                e.stopPropagation();
                                removeConnection(node.id, connectionId);
                              }}
                            >
                              <Unlink className="menu-item-icon" />
                              {targetNode.title.slice(0, 20)}...
                </button>
                          ) : null;
                        })}
                      </>
                    )}
                  </div>
                )}
            </div>

              {activeNode === node.id && !node.isStreaming && (
                <div className="node-actions">
                  <Button 
                    size="sm" 
                    className="explore-button"
                    onClick={(e) => {
                      e.stopPropagation();
                      expandNode(node.id);
                    }}
                  >
                    <ArrowRight className="arrow-icon" />
                    {t('exploreDetails')}
                  </Button>
                </div>
              )}

              {/* 展开的详细内容 */}
              {node.isExpanded && (
                <div className="node-expanded-content">
                  <div className="expanded-text">
                    {node.fullContent || node.content}
                  </div>
                  <Button 
                    size="sm" 
                    variant="ghost" 
                    className="collapse-button"
                    onClick={(e) => {
                      e.stopPropagation();
                      collapseNode(node.id);
                    }}
                  >
                    <Minimize2 className="minimize-icon" />
                    {t('collapse')}
                  </Button>
                </div>
              )}
            </Card>
          ))}
        </div>
            </div>

            {/* 输入区域 */}
      <div className="input-area">
            <div className="input-container">
          <div className="input-row">
              <div className="input-wrapper">
              <input
                type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyDown={handleKeyDown}
                placeholder={t('inputPlaceholder')}
                className="message-input"
                  disabled={isLoading}
                />
            </div>

            {/* 网络搜索开关 */}
            <div className="web-search-toggle">
              <Button
                variant="ghost"
                size="sm"
                className={`toggle-button ${webSearchEnabled ? 'enabled' : 'disabled'}`}
                onClick={() => setWebSearchEnabled(!webSearchEnabled)}
                title={webSearchEnabled ? t('disableWebSearch') : t('enableWebSearch')}
              >
                {webSearchEnabled ? <Globe className="toggle-icon" /> : <WifiOff className="toggle-icon" />}
                <span className="toggle-text">
                  {webSearchEnabled ? t('webSearchOn') : t('webSearchOff')}
                </span>
              </Button>
            </div>

            <Button
                  onClick={() => sendMessage()}
              className="send-button"
                  disabled={!inputValue.trim() || isLoading}
            >
              <Send className="send-icon" />
            </Button>
          </div>

          {/* 快捷操作 - 工业地理开发专业提示词 */}
          <div className="quick-actions">
            {[
              'Industrial site selection',
              'Labor market analysis', 
              'Economic hotspot mapping',
              'Investment cost modeling'
            ].map((action, idx) => (
              <Button
                key={idx}
                variant="outline"
                size="sm"
                className="quick-action-button"
                onClick={() => selectSuggestion(action)}
              >
                {action}
              </Button>
            ))}
          </div>
        </div>
              </div>
            </div>
  );
};

export default AIAssistant;
