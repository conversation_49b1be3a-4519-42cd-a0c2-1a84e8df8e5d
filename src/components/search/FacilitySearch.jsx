import React, { useState, useEffect, useRef } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';
import GlassSurface from '../GlassSurface/GlassSurface';
import '../../styles/FacilitySearch.css';

// Simple inline SVG icons to match the new UI
const SearchIcon = ({ size = 18, color = '#111' }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false">
    <circle cx="11" cy="11" r="7" stroke={color} strokeWidth="2" />
    <line x1="21" y1="21" x2="16.65" y2="16.65" stroke={color} strokeWidth="2" strokeLinecap="round" />
  </svg>
);

const ChevronDownIcon = ({ size = 18, color = '#111' }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false">
    <polyline points="6 9 12 15 18 9" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

/**
 * 交互式设施搜索组件
 * 使用OpenStreetMap Overpass API搜索周边设施
 */
const FacilitySearch = ({ position, language, t }) => {
  const map = useMap();
  const [searchType, setSearchType] = useState('amenity');
  const [customSearchTerm, setCustomSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [markers, setMarkers] = useState([]);
  const [markersLayer, setMarkersLayer] = useState(null);
  const [error, setError] = useState(null);
  const [showResults, setShowResults] = useState(false);
  const [radius, setRadius] = useState(1000); // 搜索半径，默认1000米
  const [isCustomSearch, setIsCustomSearch] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false); // 控制展开/折叠状态
  
  // 添加refs
  const containerRef = useRef(null);
  const sliderRef = useRef(null);
  
  // 设施类型配置
  const facilityTypes = {
    amenity: [
      { value: "restaurant", label: t?.restaurant || "餐厅", icon: "🍽️" },
      { value: "cafe", label: t?.cafe || "咖啡厅", icon: "☕" },
      { value: "bank", label: t?.bank || "银行", icon: "🏦" },
      { value: "hospital", label: t?.hospital || "医院", icon: "🏥" },
      { value: "school", label: t?.school || "学校", icon: "🏫" },
      { value: "pharmacy", label: t?.pharmacy || "药店", icon: "💊" },
      { value: "fuel", label: t?.gasStation || "加油站", icon: "⛽" },
      { value: "police", label: t?.police || "警察局", icon: "👮" }
    ],
    shop: [
      { value: "supermarket", label: t?.supermarket || "超市", icon: "🛒" },
      { value: "convenience", label: t?.convenience || "便利店", icon: "🏪" },
      { value: "mall", label: t?.mall || "购物中心", icon: "🛍️" },
      { value: "clothes", label: t?.clothes || "服装店", icon: "👔" },
      { value: "hardware", label: t?.hardware || "五金店", icon: "🔨" }
    ],
    tourism: [
      { value: "hotel", label: t?.hotel || "酒店", icon: "🏨" },
      { value: "attraction", label: t?.attraction || "景点", icon: "🎭" },
      { value: "museum", label: t?.museum || "博物馆", icon: "🏛️" }
    ],
    leisure: [
      { value: "park", label: t?.park || "公园", icon: "🌳" },
      { value: "sports_centre", label: t?.sportsCenter || "体育中心", icon: "🏊" },
      { value: "stadium", label: t?.stadium || "体育场", icon: "🏟️" }
    ],
    public_transport: [
      { value: "station", label: t?.station || "车站", icon: "🚉" },
      { value: "bus_stop", label: t?.busStop || "公交站", icon: "🚏" }
    ]
  };

  // 初始化标记图层
  useEffect(() => {
    const layer = L.layerGroup().addTo(map);
    setMarkersLayer(layer);
    
    return () => {
      if (layer) {
        map.removeLayer(layer);
      }
    };
  }, [map]);

  // 阻止整个组件上的地图事件传播
  useEffect(() => {
    const container = containerRef.current;
    const slider = sliderRef.current;
    
    if (container) {
      L.DomEvent.disableClickPropagation(container);
      L.DomEvent.disableScrollPropagation(container);
    }
    
    if (slider) {
      // 额外为滑块添加事件阻止
      L.DomEvent.disableClickPropagation(slider);
      L.DomEvent.disableScrollPropagation(slider);
      
      // 阻止滑块上的触摸事件传播到地图
      slider.addEventListener('touchstart', (e) => {
        e.stopPropagation();
      }, { passive: false });
      
      slider.addEventListener('touchmove', (e) => {
        e.stopPropagation();
      }, { passive: false });
    }
    
    return () => {
      if (slider) {
        slider.removeEventListener('touchstart', (e) => e.stopPropagation());
        slider.removeEventListener('touchmove', (e) => e.stopPropagation());
      }
    };
  }, [containerRef, sliderRef]);

  // 清除之前的标记
  const clearMarkers = () => {
    if (markersLayer) {
      markersLayer.clearLayers();
    }
    setMarkers([]);
  };
  
  // 完全清除搜索结果和标记
  const clearSearchResults = () => {
    clearMarkers();
    setSearchResults([]);
    setShowResults(false);
    setError(null);
  };

  // 创建设施图标
  const createFacilityIcon = (category) => {
    // 根据设施类型选择不同的图标颜色
    const getColorByCategory = (cat) => {
      const categoryColors = {
        'amenity': '#4CAF50', // 绿色 - 便利设施
        'shop': '#2196F3',    // 蓝色 - 商店
        'tourism': '#FF9800', // 橙色 - 旅游景点
        'leisure': '#9C27B0', // 紫色 - 休闲场所
        'public_transport': '#F44336', // 红色 - 公共交通
        'healthcare': '#F44336', // 红色 - 医疗设施
        'education': '#795548', // 棕色 - 教育机构
      };
      return categoryColors[cat] || '#607D8B'; // 默认灰色
    };

    return L.divIcon({
      className: 'facility-marker',
      html: `<div style="background-color: ${getColorByCategory(category)}; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white;"></div>`,
      iconSize: [16, 16],
      iconAnchor: [8, 8],
    });
  };

  // 构建Overpass API查询
  const buildOverpassQuery = (lat, lon, radius, searchType, searchValue) => {
    // 如果是自定义搜索，直接使用用户输入的搜索词
    if (isCustomSearch && customSearchTerm) {
      return `
        [out:json];
        (
          node(around:${radius}, ${lat}, ${lon})["name"~"${customSearchTerm}", i];
          way(around:${radius}, ${lat}, ${lon})["name"~"${customSearchTerm}", i];
          relation(around:${radius}, ${lat}, ${lon})["name"~"${customSearchTerm}", i];
        );
        out body;
        >;
        out skel qt;
      `;
    }
    
    // 否则使用预设的分类搜索
    return `
      [out:json];
      (
        node(around:${radius}, ${lat}, ${lon})["${searchType}"="${searchValue}"];
        way(around:${radius}, ${lat}, ${lon})["${searchType}"="${searchValue}"];
        relation(around:${radius}, ${lat}, ${lon})["${searchType}"="${searchValue}"];
      );
      out body;
      >;
      out skel qt;
    `;
  };

  // 搜索周边设施
  const searchFacilities = async (facilityType) => {
    try {
      setIsSearching(true);
      setError(null);
      clearMarkers();
      
      // 获取当前地图中心点坐标 (优先使用传入的 position)
      const mapCenter = position || [map.getCenter().lat, map.getCenter().lng];
      
      // 构建Overpass API查询
      const query = buildOverpassQuery(
        mapCenter[0], 
        mapCenter[1], 
        radius,
        isCustomSearch ? "name" : searchType,
        isCustomSearch ? customSearchTerm : facilityType
      );
      
      // 发送请求到Overpass API
      const response = await fetch('https://overpass-api.de/api/interpreter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `data=${encodeURIComponent(query)}`
      });
      
      if (!response.ok) {
        throw new Error(`搜索请求失败: ${response.status}`);
      }
      
      const data = await response.json();
      
      // 处理结果（只处理节点类型的结果，简化处理）
      const results = data.elements.filter(el => el.type === 'node').map(el => {
        return {
          id: el.id,
          lat: el.lat,
          lon: el.lon,
          tags: el.tags || {},
          type: searchType,
          value: facilityType
        };
      });
      
      setSearchResults(results);
      setShowResults(true);
      
      if (results.length === 0) {
        return;
      }
      
      // 在地图上添加标记
      const newMarkers = results.map(result => {
        const marker = L.marker(
          [result.lat, result.lon],
          { icon: createFacilityIcon(searchType) }
        );
        
        // 构建弹出信息内容
        const name = result.tags.name || result.tags['name:' + language] || `${facilityType}`;
        const address = [
          result.tags['addr:housenumber'], 
          result.tags['addr:street'],
          result.tags['addr:city']
        ].filter(Boolean).join(', ');
        
        // 添加点击弹出信息
        marker.bindPopup(
          `<div class="facility-popup">
            <h3>${name}</h3>
            ${address ? `<p>${address}</p>` : ''}
            <p><strong>${t?.facilityType || '类型'}:</strong> ${facilityType}</p>
            ${result.tags.phone ? `<p><strong>${t?.phone || '电话'}:</strong> ${result.tags.phone}</p>` : ''}
            ${result.tags.website ? `<p><a href="${result.tags.website}" target="_blank">${t?.website || '网站'}</a></p>` : ''}
          </div>`
        );
        
        markersLayer.addLayer(marker);
        return marker;
      });
      
      setMarkers(newMarkers);
      
      // 如果有结果，调整地图视图以显示所有标记
      if (newMarkers.length > 0) {
        // 创建一个临时的图层组来计算边界
        const group = L.featureGroup(newMarkers);
        const bounds = group.getBounds();
        
        if (bounds.isValid()) {
          map.fitBounds(bounds.pad(0.2));
        } else if (newMarkers.length === 1) {
          // 如果只有一个标记，直接定位到该标记
          const marker = newMarkers[0];
          map.setView([marker._latlng.lat, marker._latlng.lng], 15);
        }
      }
      
      // 搜索成功后自动收起设施类型面板，保留结果显示
      setIsExpanded(false);
    } catch (err) {
      console.error('设施搜索出错:', err);
      setError(err.message);
    } finally {
      setIsSearching(false);
    }
  };

  // 处理设施类型选择
  const handleFacilityTypeSelect = (type, value) => {
    setIsCustomSearch(false);
    setSearchType(type);
    searchFacilities(value);
  };

  // 处理自定义搜索提交
  const handleCustomSearch = (e) => {
    e.preventDefault();
    if (!customSearchTerm.trim()) return;
    
    setIsCustomSearch(true);
    searchFacilities(customSearchTerm);
  };

  // 处理结果项点击
  const handleResultClick = (result) => {
    const latlng = [result.lat, result.lon];
    map.setView(latlng, 16);
    
    // 找到对应的标记并打开弹出窗口
    markers.forEach(marker => {
      if (marker._latlng.lat === result.lat && marker._latlng.lng === result.lon) {
        marker.openPopup();
      }
    });
  };

  // 关闭结果列表
  const closeResults = () => {
    setShowResults(false);
  };

  // 切换展开/折叠状态
  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  // 切换半径选择器
  const handleRadiusChange = (e) => {
    setRadius(parseInt(e.target.value));
  };

  // 格式化半径显示
  const formatRadius = (r) => {
    if (r >= 1000) {
      return `${(r / 1000).toFixed(1)} km`;
    } else {
      return `${r} m`;
    }
  };

  // 渲染设施类型分类
  const renderFacilityCategories = () => {
    return (
      <div className="facility-categories">
        {Object.entries(facilityTypes).map(([type, values]) => (
          <div key={type} className="facility-category">
            <h4>{t?.[type] || type}</h4>
            <div className="facility-buttons">
              {values.map(item => (
                <button
                  key={item.value}
                  onClick={() => handleFacilityTypeSelect(type, item.value)}
                  className="facility-type-button"
                  disabled={isSearching}
                  title={item.label}
                >
                  <span className="facility-icon">{item.icon}</span>
                  <span className="facility-label">{item.label}</span>
                </button>
              ))}
            </div>
          </div>
        ))}
      </div>
    );
  };

  // 渲染搜索结果
  const renderSearchResults = () => {
    if (!showResults) return null;
    
    return (
      <div className="facility-search-results">
        <div className="results-header">
          <h3>{t?.searchResults || "搜索结果"} ({searchResults.length})</h3>
          <button className="close-results-button" onClick={closeResults}>×</button>
        </div>
        {searchResults.length > 0 ? (
          <div className="results-container">
            <ul>
              {searchResults.map((result) => (
                <li 
                  key={result.id} 
                  onClick={() => handleResultClick(result)}
                  className="facility-result-item"
                >
                  <strong>{result.tags.name || result.tags['name:' + language] || `${result.value}`}</strong>
                  <span className="result-type">{t?.[result.value] || result.value}</span>
                  {(result.tags['addr:street'] || result.tags['addr:city']) && (
                    <small>
                      {[result.tags['addr:housenumber'], result.tags['addr:street'], result.tags['addr:city']]
                        .filter(Boolean).join(', ')}
                    </small>
                  )}
                </li>
              ))}
            </ul>
            <div className="results-footer">
              <button 
                className="clear-all-button" 
                onClick={clearSearchResults}
              >
                <span className="clear-icon">🗑️</span>
                {t?.clearResults || "清除结果"}
              </button>
            </div>
          </div>
        ) : (
          <p className="no-results">{t?.noResultsFound || "未找到结果"}</p>
        )}
      </div>
    );
  };

  return (
    <div 
      ref={containerRef}
      className={`facility-search-container ${isExpanded ? 'expanded' : 'collapsed'} ${showResults ? 'with-results' : ''}`}
    >
      <GlassSurface
        width="fit-content"
        height={56}
        borderRadius={28}
        borderWidth={0.07}
        displace={0.5}
        brightness={50}
        opacity={0.93}
        backgroundOpacity={0.12}
        saturation={2.2}
        blur={11}
        mixBlendMode="screen"
        className="facility-search-header glass-navigation"
        style={{ padding: '4px 6px' }}
      >
        <form onSubmit={handleCustomSearch} className="facility-search-form glass-bar">
          <input
            type="text"
            value={customSearchTerm}
            onChange={(e) => setCustomSearchTerm(e.target.value)}
            placeholder={t?.customSearchPlaceholder || "自定义搜索..."}
            className="facility-search-input"
            disabled={isSearching}
            onClick={() => !isExpanded && setIsExpanded(true)}
          />
          <button
            type="submit"
            className="facility-search-button"
            disabled={isSearching}
            aria-label={t?.search || '搜索'}
            title={t?.search || '搜索'}
          >
            {isSearching ? '...' : <SearchIcon color="#ffffff" size={16} />}
          </button>
        </form>
        <button
          className="facility-toggle-button"
          onClick={toggleExpand}
          aria-expanded={isExpanded}
          title={isExpanded ? t?.collapse || "收起" : t?.expand || "展开"}
          aria-label={isExpanded ? t?.collapse || "收起" : t?.expand || "展开"}
        >
          <ChevronDownIcon color="#111" size={16} />
        </button>
      </GlassSurface>

      {isExpanded && (
        <GlassSurface
          width={360}
          height="auto"
          borderRadius={20}
          borderWidth={0.07}
          displace={0.5}
          brightness={50}
          opacity={0.95}
          backgroundOpacity={0.12}
          saturation={2.2}
          blur={11}
          mixBlendMode="screen"
          className="facility-search-content glass-panel"
        >
          <div
            className="radius-selector"
            ref={sliderRef}
            onMouseDown={(e) => e.stopPropagation()}
            onTouchStart={(e) => e.stopPropagation()}
          >
            <label htmlFor="radius-slider">
              <span className="radius-icon">📏</span>
              <span>{t?.searchRadius || "搜索半径"}: {formatRadius(radius)}</span>
            </label>
            <input
              type="range"
              id="radius-slider"
              min="100"
              max="50000"
              step="100"
              value={radius}
              onChange={handleRadiusChange}
              className="radius-slider"
            />
          </div>

          {renderFacilityCategories()}

          {error && <div className="facility-search-error">{error}</div>}
        </GlassSurface>
      )}

      {showResults && (
        <GlassSurface
          width={360}
          height="auto"
          borderRadius={16}
          borderWidth={0.07}
          displace={0.5}
          brightness={50}
          opacity={0.98}
          backgroundOpacity={0.12}
          saturation={2.2}
          blur={11}
          mixBlendMode="screen"
          className="glass-panel"
        >
          {renderSearchResults()}
        </GlassSurface>
      )}
    </div>
  );
};

export default FacilitySearch;