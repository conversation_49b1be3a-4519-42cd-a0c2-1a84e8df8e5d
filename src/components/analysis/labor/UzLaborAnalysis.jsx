import React, { useEffect, useMemo, useState, useCallback } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import '../../../styles/TimeSeriesPanel.css';

// 响应式设计样式
const responsiveStyles = `
  @media (max-width: 1200px) {
    .industry-distribution-mobile {
      .industry-controls {
        flex-wrap: wrap;
        gap: 6px;
      }

      .industry-search {
        min-width: 150px;
        flex: 1;
      }
    }
  }

  @media (max-width: 768px) {
    .industry-distribution-mobile {
      .industry-controls {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
      }

      .industry-search {
        width: 100%;
        min-width: unset;
      }

      .industry-item {
        padding: 14px 12px;
      }

      .industry-item .industry-name {
        font-size: 14px;
        margin-bottom: 8px;
      }

      .industry-item .industry-code {
        width: 50px;
        font-size: 14px;
      }

      .industry-item .industry-values {
        min-width: 90px;
      }

      .industry-item .industry-value {
        font-size: 16px;
      }

      .industry-item .industry-percentage {
        font-size: 13px;
      }
    }
  }

  @media (max-width: 480px) {
    .industry-distribution-mobile {
      .industry-item {
        padding: 12px 10px;
      }

      .industry-item .industry-code {
        width: 45px;
        font-size: 13px;
      }

      .industry-item .industry-name {
        font-size: 13px;
      }

      .industry-item .industry-value {
        font-size: 15px;
      }

      .industry-item .industry-percentage {
        font-size: 12px;
      }
    }

    .analytics-container {
      padding: 8px !important;
    }
  }

  @media (min-width: 1600px) {
    .industry-distribution-mobile {
      .industry-item {
        padding: 16px 20px;
      }

      .industry-item .industry-name {
        font-size: 15px;
      }

      .industry-item .industry-code {
        width: 55px;
        font-size: 14px;
      }

      .industry-item .industry-values {
        min-width: 120px;
      }

      .industry-item .industry-value {
        font-size: 18px;
      }

      .industry-item .industry-percentage {
        font-size: 14px;
      }
    }
  }
`;

// 注入样式
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = responsiveStyles;
  document.head.appendChild(styleSheet);
}

// Register Chart.js once
try {
  ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    BarElement,
    ArcElement,
    Title,
    Tooltip,
    Legend
  );
} catch (e) {
  // ignore duplicate registration
}

const palette = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4', '#e11d48'];
const REGION_PRESETS = ['Republic of Uzbekistan','Tashkent region','Tashkent city'];

function sortYear(a, b) { return Number(a) - Number(b); }
function sortQuarter(a, b) {
  const [ay, aq] = a.split('-Q');
  const [by, bq] = b.split('-Q');
  if (ay !== by) return Number(ay) - Number(by);
  return Number(aq) - Number(bq);
}

function extractSeries(record, predicate, sorter) {
  const entries = Object.entries(record)
    .filter(([k, v]) => predicate(k) && typeof v === 'number')
    .map(([k, v]) => ({ label: k, value: v }));
  entries.sort((a, b) => sorter(a.label, b.label));
  return entries;
}

const UzLaborAnalysis = ({ defaultRegions = REGION_PRESETS, t = {}, language = 'zh' }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Raw datasets
  const [employmentRate, setEmploymentRate] = useState([]); // percent, annual
  const [avgWagesQ, setAvgWagesQ] = useState([]); // quarterly wages
  const [unemployed, setUnemployed] = useState([]); // thousand people
  const [laborResources, setLaborResources] = useState([]); // thousand people
  const [laborResourcesCity, setLaborResourcesCity] = useState([]);
  const [industryDist, setIndustryDist] = useState([]); // national by industry code
  const [schoolsCity, setSchoolsCity] = useState([]); // general education institutions
  const [collegesCity, setCollegesCity] = useState([]);
  const [graduates11, setGraduates11] = useState([]);

  const [selectedRegions, setSelectedRegions] = useState(defaultRegions);
  const [search, setSearch] = useState('');
  const [activeTab, setActiveTab] = useState('overview');
  const [industryYear, setIndustryYear] = useState('2024');
  const [chartKey, setChartKey] = useState(0); // 强制图表重新渲染

  // Industry distribution states
  const [industrySearch, setIndustrySearch] = useState('');
  const [industrySortBy, setIndustrySortBy] = useState('value'); // 'value' or 'name'
  const [industrySortOrder, setIndustrySortOrder] = useState('desc'); // 'asc' or 'desc'
  const [showAllIndustries, setShowAllIndustries] = useState(false);
  const [industryViewMode, setIndustryViewMode] = useState('chart'); // 'chart' or 'list'

  // Region selector UX
  const [selectorOpen, setSelectorOpen] = useState(false);
  const [draftSelected, setDraftSelected] = useState(defaultRegions);
  const MAX_SELECT = 4;
  const [limitMsg, setLimitMsg] = useState('');

  // Virtual list states to keep scrolling silky-smooth
  const [listScrollTop, setListScrollTop] = useState(0);
  const ROW_H = 48; // px per row

  // Viewport dimensions for responsive design
  const [viewport, setViewport] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 900
  });

  useEffect(() => {
    const onResize = () => {
      setViewport({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };
    window.addEventListener('resize', onResize);
    return () => window.removeEventListener('resize', onResize);
  }, []);

  // 响应式布局配置 - 优化图表尺寸，提升视觉效果
  const getLayoutConfig = () => {
    const { width } = viewport;
    // 减小图表高度，优化视觉比例
    if (width >= 1800) {
      return {
        gridCols: 2,
        chartHeight: 280, // 大幅减小图表高度
        containerMaxWidth: '100%',
        sidePadding: 24
      };
    } else if (width >= 1400) {
      return {
        gridCols: 2,
        chartHeight: 260,
        containerMaxWidth: '100%',
        sidePadding: 20
      };
    } else if (width >= 1200) {
      return {
        gridCols: 2,
        chartHeight: 240,
        containerMaxWidth: '100%',
        sidePadding: 18
      };
    } else if (width >= 900) {
      return {
        gridCols: 1,
        chartHeight: 220,
        containerMaxWidth: '100%',
        sidePadding: 16
      };
    } else {
      return {
        gridCols: 1,
        chartHeight: 200,
        containerMaxWidth: '100%',
        sidePadding: 14
      };
    }
  };

  const layoutConfig = getLayoutConfig();

  useEffect(() => {
    let cancelled = false;
    setLoading(true);
    (async () => {
      try {
        const paths = [
          '/data/Uzbekistan_data/Labor/Employment rate.json',

          '/data/Uzbekistan_data/Labor/Average Nominal Wages (quarterly).json',
          '/data/Uzbekistan_data/Labor/Number of unemployed.json',
          '/data/Uzbekistan_data/Labor/The number of labor resources.json',
          '/data/Uzbekistan_data/Labor/The number of labor resources (city).json',

          "/data/Uzbekistan_data/Labor/Distribution of the employed population by type of economic activity.json",
          '/data/Uzbekistan_data/Labor/Number of general education institutions (city).json',
          '/data/Uzbekistan_data/Labor/Number of colleges (city).json',

          "/data/Uzbekistan_data/Labor/Graduates of 11 classes (by regions).json",
        ];
        const resps = await Promise.all(paths.map((p) => fetch(p)));
        const jsons = await Promise.all(resps.map((r) => r.json()));
        if (cancelled) return;
        const [jEmpRate, jWagesQ, jUnemp, jLaborRes, jLaborResCity, jIndDist, jSchools, jColleges, jGrads] = jsons;
        const getM = (j) => (Array.isArray(j["Ma'lumot"]) ? j["Ma'lumot"] : []);
        setEmploymentRate(getM(jEmpRate));
        setAvgWagesQ(getM(jWagesQ));
        setUnemployed(getM(jUnemp));
        setLaborResources(getM(jLaborRes));
        setLaborResourcesCity(getM(jLaborResCity));
        setIndustryDist(getM(jIndDist));
        setSchoolsCity(getM(jSchools));
        setCollegesCity(getM(jColleges));
        setGraduates11(getM(jGrads));
        setError(null);
      } catch (e) {
        console.error('Error loading Uzbekistan labor datasets', e);
        if (!cancelled) setError('Failed to load Uzbekistan labor datasets');
      } finally {
        if (!cancelled) setLoading(false);
      }
    })();
    return () => { cancelled = true; };
  }, []);

  // Initialize draft selection when opening selector
  useEffect(() => {
    if (selectorOpen) {
      setDraftSelected(selectedRegions);
      setSearch('');
      setLimitMsg('');
    }
  }, [selectorOpen, selectedRegions]);

  const availableRegions = useMemo(() => {
    try {
      const set = new Set();
      [...employmentRate, ...laborResources, ...laborResourcesCity, ...unemployed].forEach((r) => {
        if (r?.Klassifikator_en) set.add(r.Klassifikator_en);
      });
      let arr = Array.from(set);
      console.log('Available regions in data:', arr); // Debug log
      if (search) {
        const q = search.toLowerCase();
        arr = arr.filter((n) => n.toLowerCase().includes(q));
      }
      arr.sort((a, b) => {
        const ai = REGION_PRESETS.indexOf(a);
        const bi = REGION_PRESETS.indexOf(b);
        if (ai !== -1 || bi !== -1) return (ai === -1 ? 999 : ai) - (bi === -1 ? 999 : bi);
        return a.localeCompare(b);
      });
      return arr;
    } catch (e) {
      return REGION_PRESETS;
    }
  }, [employmentRate, laborResources, laborResourcesCity, unemployed, search]);

  // Force chart re-render when selectedRegions or activeTab/viewport changes (fix blank charts after tab switch)
  useEffect(() => {
    setChartKey(prev => prev + 1);
  }, [selectedRegions, activeTab, viewport.width, viewport.height]);

  const regionColor = (region) => {
    const idx = REGION_PRESETS.includes(region)
      ? REGION_PRESETS.indexOf(region)
      : (availableRegions.indexOf(region) % palette.length);
    return palette[idx % palette.length];
  };

  // Chart builders
  const employmentRateChart = useMemo(() => {
    if (!employmentRate.length) return null;
    const years = Array.from(new Set(
      employmentRate.flatMap(r => Object.keys(r).filter(k => /^\d{4}$/.test(k)))
    )).sort(sortYear);
    const datasets = selectedRegions.map((name) => {
      const rec = employmentRate.find(r => r.Klassifikator_en === name);
      const data = years.map(y => (rec && typeof rec[y] === 'number') ? rec[y] : null);
      const hasData = data.some(val => val !== null);
      return { 
        label: hasData ? name : `${name} (无数据)`, 
        data: hasData ? data : years.map(() => null),
        borderColor: regionColor(name), 
        backgroundColor: regionColor(name), 
        tension: 0.2,
        borderDash: hasData ? [] : [5, 5], // 虚线表示无数据
        pointRadius: hasData ? 3 : 0 // 无数据时不显示点
      };
    });
    return { labels: years, datasets };
  }, [employmentRate, selectedRegions]);

  const unemployedChart = useMemo(() => {
    if (!unemployed.length) return null;
    const years = Array.from(new Set(
      unemployed.flatMap(r => Object.keys(r).filter(k => /^\d{4}$/.test(k)))
    )).sort(sortYear);
    const datasets = selectedRegions.map((name) => {
      const rec = unemployed.find(r => r.Klassifikator_en === name);
      const data = years.map(y => (rec && typeof rec[y] === 'number') ? rec[y] : null);
      const hasData = data.some(val => val !== null);
      return { 
        label: hasData ? name : `${name} (无数据)`, 
        data: hasData ? data : years.map(() => null),
        borderColor: regionColor(name), 
        backgroundColor: hasData ? regionColor(name) : `${regionColor(name)}40`, // 半透明表示无数据
        tension: 0.2 
      };
    });
    return { labels: years, datasets };
  }, [unemployed, selectedRegions]);

  const wagesQuarterlyChart = useMemo(() => {
    if (!avgWagesQ.length) return null;
    // Use national + selected regions when available; if a selected region not in dataset, skip
    const labels = extractSeries(avgWagesQ[0] || {}, (k) => /^\d{4}-Q[1-4]$/.test(k), sortQuarter).map(e => e.label);
    const datasets = selectedRegions.map((name) => {
      const rec = avgWagesQ.find(r => r.Klassifikator_en === name);
      const data = labels.map(l => rec && typeof rec[l] === 'number' ? rec[l] : null);
      const hasData = data.some(val => val !== null);
      return { 
        label: hasData ? name : `${name} (无数据)`, 
        data: hasData ? data : labels.map(() => null),
        borderColor: regionColor(name), 
        backgroundColor: regionColor(name), 
        tension: 0.2,
        borderDash: hasData ? [] : [5, 5], // 虚线表示无数据
        pointRadius: hasData ? 3 : 0 // 无数据时不显示点
      };
    });
    return { labels, datasets };
  }, [avgWagesQ, selectedRegions]);

  const laborResourcesChart = useMemo(() => {
    if (!laborResources.length) return null;
    const years = Array.from(new Set(laborResources.flatMap(r => Object.keys(r).filter(k => /^\d{4}$/.test(k))))).sort(sortYear);
    const datasets = selectedRegions.map((name) => {
      const rec = laborResources.find(r => r.Klassifikator_en === name);
      const data = years.map(y => (rec && typeof rec[y] === 'number') ? rec[y] : null);
      const hasData = data.some(val => val !== null);
      return { 
        label: hasData ? name : `${name} (无数据)`, 
        data: hasData ? data : years.map(() => null),
        borderColor: regionColor(name), 
        backgroundColor: regionColor(name), 
        tension: 0.2,
        borderDash: hasData ? [] : [5, 5], // 虚线表示无数据
        pointRadius: hasData ? 3 : 0 // 无数据时不显示点
      };
    });
    return { labels: years, datasets };
  }, [laborResources, selectedRegions]);

  const schoolsChart = useMemo(() => {
    if (!schoolsCity.length) return null;
    const years = Array.from(new Set(schoolsCity.flatMap(r => Object.keys(r).filter(k => /^\d{4}$/.test(k))))).sort(sortYear);
    const datasets = selectedRegions.map((name) => {
      const rec = schoolsCity.find(r => r.Klassifikator_en === name);
      const data = years.map(y => (rec && typeof rec[y] === 'number') ? rec[y] : null);
      const hasData = data.some(val => val !== null);
      return { 
        label: hasData ? name : `${name} (无数据)`, 
        data: hasData ? data : years.map(() => null),
        backgroundColor: hasData ? regionColor(name) : `${regionColor(name)}40`, // 半透明表示无数据
        borderColor: regionColor(name) 
      };
    });
    return { labels: years, datasets };
  }, [schoolsCity, selectedRegions]);

  const collegesChart = useMemo(() => {
    if (!collegesCity.length) return null;
    const years = Array.from(new Set(collegesCity.flatMap(r => Object.keys(r).filter(k => /^\d{4}$/.test(k))))).sort(sortYear);
    const datasets = selectedRegions.map((name) => {
      const rec = collegesCity.find(r => r.Klassifikator_en === name);
      const data = years.map(y => (rec && typeof rec[y] === 'number') ? rec[y] : null);
      const hasData = data.some(val => val !== null);
      return { 
        label: hasData ? name : `${name} (无数据)`, 
        data: hasData ? data : years.map(() => null),
        backgroundColor: hasData ? regionColor(name) : `${regionColor(name)}40`, // 半透明表示无数据
        borderColor: regionColor(name) 
      };
    });
    return { labels: years, datasets };
  }, [collegesCity, selectedRegions]);

  const gradsChart = useMemo(() => {
    if (!graduates11.length) return null;
    // Latest year available across national record
    const any = graduates11.find(r => r.Klassifikator_en === 'Republic of Uzbekistan') || graduates11[0];
    const years = Object.keys(any || {}).filter(k => /^\d{4}$/.test(k)).sort(sortYear);
    const latest = years[years.length - 1];
    // Include all selected regions, mark those without data
    const labels = selectedRegions.map(name => {
      const rec = graduates11.find(r => r.Klassifikator_en === name);
      const hasData = rec && typeof rec[latest] === 'number';
      return hasData ? name : `${name} (无数据)`;
    });
    const data = selectedRegions.map(name => {
      const rec = graduates11.find(r => r.Klassifikator_en === name);
      return rec && typeof rec[latest] === 'number' ? rec[latest] : 0;
    });
    const backgroundColors = selectedRegions.map((name, index) => {
      const rec = graduates11.find(r => r.Klassifikator_en === name);
      const hasData = rec && typeof rec[latest] === 'number';
      return hasData ? regionColor(name) : `${regionColor(name)}40`; // 半透明表示无数据
    });
    return { labels, datasets: [{ label: latest, data, backgroundColor: backgroundColors }] };
  }, [graduates11, selectedRegions]);



  // 优化的图表选项配置，提升可读性
  const lineOptions = (title = '', yLabel = '') => ({
    responsive: true,
    maintainAspectRatio: false,
    resizeDelay: 100,
    interaction: {
      intersect: false,
      mode: 'index'
    },
    plugins: { 
      legend: { 
        position: 'top',
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 13,
            weight: '500'
          }
        }
      }, 
      title: { 
        display: !!title, 
        text: title,
        font: {
          size: 16,
          weight: 'bold'
        },
        padding: 20
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleFont: {
          size: 14
        },
        bodyFont: {
          size: 13
        },
        cornerRadius: 8,
        displayColors: true
      }
    },
    scales: { 
      x: {
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.05)'
        },
        ticks: {
          font: {
            size: 12
          }
        }
      },
      y: { 
        title: { 
          display: !!yLabel, 
          text: yLabel,
          font: {
            size: 14,
            weight: '500'
          }
        },
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.05)'
        },
        ticks: {
          font: {
            size: 12
          }
        }
      } 
    }
  });
  
  const barOptions = (title = '', yLabel = '') => ({
    responsive: true,
    maintainAspectRatio: false,
    resizeDelay: 100,
    plugins: { 
      legend: { 
        position: 'top',
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 13,
            weight: '500'
          }
        }
      }, 
      title: { 
        display: !!title, 
        text: title,
        font: {
          size: 16,
          weight: 'bold'
        },
        padding: 20
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleFont: {
          size: 14
        },
        bodyFont: {
          size: 13
        },
        cornerRadius: 8,
        displayColors: true
      }
    },
    scales: { 
      x: {
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.05)'
        },
        ticks: {
          font: {
            size: 12
          }
        }
      },
      y: { 
        title: { 
          display: !!yLabel, 
          text: yLabel,
          font: {
            size: 14,
            weight: '500'
          }
        },
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.05)'
        },
        ticks: {
          font: {
            size: 12
          }
        }
      } 
    }
  });

  const headerTitle = language==='en' ? 'Labor & Employment Analysis' : '劳动力与就业分析';

  // 简约的图表容器样式 - 减少视觉干扰
  const chartWrapStyle = {
    background: '#ffffff',
    border: '1px solid #f0f0f0', // 更浅的边框
    borderRadius: 8, // 减小圆角
    padding: layoutConfig.sidePadding, // 减少内边距
    minHeight: layoutConfig.chartHeight + 60, // 优化容器高度
    boxShadow: '0 2px 8px rgba(0,0,0,0.04)', // 减弱阴影
    transition: 'all 0.3s ease',
    marginBottom: layoutConfig.sidePadding * 0.8, // 减少底部间距
    width: '100%'
  };

  // Dynamic grid style based on screen size - 优化网格间距
  const getGridStyle = (cols) => ({
    display: 'grid',
    gridTemplateColumns: `repeat(${cols}, 1fr)`,
    gap: layoutConfig.sidePadding * 1.5, // 增加网格间距
    marginBottom: layoutConfig.sidePadding,
    '@media (max-width: 768px)': {
      gridTemplateColumns: '1fr',
      gap: layoutConfig.sidePadding
    }
  });

  const chartH = layoutConfig.chartHeight;

  if (loading) return <div className="analytics-container"><p>{t.loading || 'Loading...'}</p></div>;
  if (error) return <div className="analytics-container"><p style={{ color: 'crimson' }}>{error}</p></div>;

  // Industry Distribution Component
  const IndustryDistribution = React.memo(({ data, year, language, layoutConfig = {} }) => {
    const [listScrollTop, setListScrollTop] = useState(0);
    const [hoveredIndustry, setHoveredIndustry] = useState(null);
    const ROW_HEIGHT = 52; // Increased row height for better spacing
    const MAX_VISIBLE_ROWS = layoutConfig.gridCols >= 3 ? 12 : 8; // Show more rows on wider screens

    // Calculate dynamic height based on layout
    const containerHeight = layoutConfig.gridCols >= 3 ? 500 : 400;
    const listHeight = containerHeight - 120; // Account for header and controls

    const processedData = useMemo(() => {
      if (!data?.length) return [];

      const filtered = data
        .map(item => ({
          code: item.Code,
          name: item.Klassifikator_en,
          value: item[year] || 0
        }))
        .filter(item =>
          industrySearch === '' ||
          item.name.toLowerCase().includes(industrySearch.toLowerCase()) ||
          item.code.toLowerCase().includes(industrySearch.toLowerCase())
        )
        .sort((a, b) => {
          if (industrySortBy === 'value') {
            return industrySortOrder === 'desc' ? b.value - a.value : a.value - b.value;
          } else {
            return industrySortOrder === 'desc'
              ? b.name.localeCompare(a.name)
              : a.name.localeCompare(b.name);
          }
        });

      return filtered;
    }, [data, year, industrySearch, industrySortBy, industrySortOrder]);

    const totalValue = useMemo(() => processedData.reduce((sum, item) => sum + item.value, 0), [processedData]);
    const maxValue = useMemo(() => Math.max(...processedData.map(item => item.value)), [processedData]);

    const visibleData = useMemo(() => {
      if (showAllIndustries || processedData.length <= MAX_VISIBLE_ROWS) {
        return processedData;
      }
      return processedData.slice(0, MAX_VISIBLE_ROWS);
    }, [processedData, showAllIndustries]);

    const barChartData = useMemo(() => {
      const displayData = industryViewMode === 'chart' ? processedData.slice(0, 10) : visibleData;
      return {
        labels: displayData.map(item => item.code),
        datasets: [{
          label: language === 'en' ? 'Employment (thousand)' : '就业人数（千）',
          data: displayData.map(item => item.value),
          backgroundColor: displayData.map((_, i) => palette[i % palette.length]),
          borderColor: displayData.map((_, i) => palette[i % palette.length]),
          borderWidth: 1,
        }]
      };
    }, [processedData, visibleData, industryViewMode, language]);

    const chartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      indexAxis: 'y',
      plugins: {
        legend: { display: false },
        tooltip: {
          callbacks: {
            label: (context) => {
              const item = processedData[context.dataIndex];
              const percentage = totalValue > 0 ? ((item.value / totalValue) * 100).toFixed(1) : 0;
              return `${item.name}: ${item.value.toLocaleString()} (${percentage}%)`;
            }
          }
        }
      },
      scales: {
        x: {
          beginAtZero: true,
          title: { display: true, text: language === 'en' ? 'Employment (thousand)' : '就业人数（千）' }
        },
        y: {
          ticks: {
            callback: (value, index) => {
              const item = barChartData.labels[index];
              return item || '';
            }
          }
        }
      }
    };

    return (
      <div
        className="industry-distribution-mobile"
        style={{
          background: '#ffffff',
          border: '1px solid #f0f0f0', // 更浅的边框
          borderRadius: 6, // 减小圆角
          padding: layoutConfig.sidePadding || 16,
          boxShadow: '0 1px 3px rgba(0,0,0,0.03)', // 极简阴影
          height: containerHeight,
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: 16,
          flexWrap: 'wrap',
          gap: 8
        }}>
          <h4 style={{
            margin: 0,
            color: '#333',
            fontSize: 18,
            fontWeight: 600
          }}>
            {language === 'en' ? 'Employment by industry' : '按行业就业'} ({year})
          </h4>
          <div className="industry-controls" style={{
            display: 'flex',
            gap: 8,
            alignItems: 'center',
            flexWrap: 'wrap'
          }}>
            <select
              value={year}
              onChange={(e) => setIndustryYear(e.target.value)}
              style={{
                padding: '6px 10px',
                borderRadius: 6,
                border: '1px solid #ddd',
                background: '#fff',
                fontSize: 13,
                cursor: 'pointer',
                minWidth: 80
              }}
            >
              {['2019','2020','2021','2022','2023','2024'].map(y => <option key={y} value={y}>{y}</option>)}
            </select>
            <select
              value={industryViewMode}
              onChange={(e) => setIndustryViewMode(e.target.value)}
              style={{
                padding: '6px 10px',
                borderRadius: 6,
                border: '1px solid #ddd',
                background: '#fff',
                fontSize: 13,
                cursor: 'pointer',
                minWidth: 80
              }}
            >
              <option value="chart">{language === 'en' ? 'Chart' : '图表'}</option>
              <option value="list">{language === 'en' ? 'List' : '列表'}</option>
            </select>
          </div>
        </div>

        {industryViewMode === 'chart' && (
          <div style={{ flex: 1, width: '100%' }}>
            {barChartData.labels.length > 0 ? (
              <Bar data={barChartData} options={chartOptions} />
            ) : (
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                color: '#666'
              }}>
                <div style={{ textAlign: 'center' }}>
                  <p style={{ margin: 0, fontSize: 14 }}>
                    {language === 'en' ? 'No data available' : '暂无数据'}
                  </p>
                </div>
              </div>
            )}
          </div>
        )}

        {industryViewMode === 'list' && (
          <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: 12 }}>
            <div className="industry-controls" style={{
              display: 'flex',
              gap: 8,
              alignItems: 'center',
              flexWrap: 'wrap'
            }}>
              <input
                type="text"
                className="industry-search"
                placeholder={language === 'en' ? 'Search industries...' : '搜索行业...'}
                value={industrySearch}
                onChange={(e) => setIndustrySearch(e.target.value)}
                style={{
                  flex: 1,
                  padding: '8px 12px',
                  border: '1px solid #ddd',
                  borderRadius: 6,
                  fontSize: 13,
                  minWidth: 200
                }}
              />
              <select
                value={industrySortBy}
                onChange={(e) => setIndustrySortBy(e.target.value)}
                style={{
                  padding: '8px 10px',
                  borderRadius: 6,
                  border: '1px solid #ddd',
                  background: '#fff',
                  fontSize: 13,
                  cursor: 'pointer',
                  minWidth: 120
                }}
              >
                <option value="value">{language === 'en' ? 'Sort by value' : '按数值排序'}</option>
                <option value="name">{language === 'en' ? 'Sort by name' : '按名称排序'}</option>
              </select>
              <button
                onClick={() => setIndustrySortOrder(prev => prev === 'desc' ? 'asc' : 'desc')}
                style={{
                  padding: '8px 12px',
                  border: '1px solid #ddd',
                  borderRadius: 6,
                  background: '#fff',
                  cursor: 'pointer',
                  fontSize: 13,
                  fontWeight: 500,
                  minWidth: 40
                }}
              >
                {industrySortOrder === 'desc' ? '↓' : '↑'}
              </button>
            </div>

            <div style={{
              flex: 1,
              border: '1px solid #f5f5f5', // 更浅的边框
              borderRadius: 4, // 减小圆角
              overflow: 'hidden',
              background: '#fcfcfc' // 更浅的背景
            }}>
              <div style={{
                maxHeight: listHeight,
                overflow: 'auto'
              }}>
                {visibleData.map((item, index) => {
                  const percentage = totalValue > 0 ? (item.value / totalValue * 100) : 0;
                  const barWidth = maxValue > 0 ? (item.value / maxValue * 100) : 0;

                  return (
                    <div
                      key={item.code}
                      className="industry-item"
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        padding: '12px 16px',
                        borderBottom: index < visibleData.length - 1 ? '1px solid #f0f0f0' : 'none',
                        backgroundColor: hoveredIndustry === item.code ? '#f8f9fa' : 'transparent',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease',
                        ':hover': {
                          backgroundColor: '#f8f9fa'
                        }
                      }}
                      onMouseEnter={() => setHoveredIndustry(item.code)}
                      onMouseLeave={() => setHoveredIndustry(null)}
                    >
                      <div className="industry-code" style={{
                        width: 45,
                        fontSize: 13,
                        color: '#666',
                        marginRight: 12,
                        fontWeight: 500
                      }}>
                        {item.code}
                      </div>
                      <div style={{ flex: 1, minWidth: 0 }}>
                        <div className="industry-name" style={{
                          fontSize: 14,
                          fontWeight: 500,
                          color: '#333',
                          marginBottom: 6,
                          lineHeight: 1.3
                        }}>
                          {item.name}
                        </div>
                        <div style={{
                          position: 'relative',
                          height: 10,
                          backgroundColor: '#e9ecef',
                          borderRadius: 5,
                          overflow: 'hidden'
                        }}>
                          <div
                            style={{
                              position: 'absolute',
                              left: 0,
                              top: 0,
                              height: '100%',
                              width: `${barWidth}%`,
                              backgroundColor: palette[index % palette.length],
                              borderRadius: 5,
                              transition: 'width 0.3s ease',
                              boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                            }}
                          />
                        </div>
                      </div>
                      <div className="industry-values" style={{
                        marginLeft: 16,
                        textAlign: 'right',
                        minWidth: 100,
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'flex-end'
                      }}>
                        <div className="industry-value" style={{
                          fontSize: 16,
                          fontWeight: 700,
                          color: '#333',
                          lineHeight: 1
                        }}>
                          {item.value.toLocaleString()}
                        </div>
                        <div className="industry-percentage" style={{
                          fontSize: 12,
                          color: '#666',
                          marginTop: 2,
                          fontWeight: 500
                        }}>
                          {percentage.toFixed(1)}%
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {processedData.length > MAX_VISIBLE_ROWS && !showAllIndustries && (
              <div style={{ textAlign: 'center', paddingTop: 8 }}>
                <button
                  onClick={() => setShowAllIndustries(true)}
                  style={{
                    padding: '8px 16px',
                    border: '1px solid #ddd',
                    borderRadius: 6,
                    background: '#fff',
                    cursor: 'pointer',
                    fontSize: 13,
                    fontWeight: 500,
                    transition: 'all 0.2s ease',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.05)'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.background = '#f8f9fa';
                    e.target.style.borderColor = '#bbb';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.background = '#fff';
                    e.target.style.borderColor = '#ddd';
                  }}
                >
                  {language === 'en' ? `Show all ${processedData.length} industries` : `显示全部${processedData.length}个行业`}
                </button>
              </div>
            )}
          </div>
        )}

        {processedData.length === 0 && (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: '#666'
          }}>
            <div style={{ textAlign: 'center' }}>
              <p style={{ margin: 0, fontSize: 14 }}>
                {language === 'en' ? 'No industries found' : '未找到行业'}
              </p>
            </div>
          </div>
        )}
      </div>
    );
  });

  return (
    <section
      className="analytics-container"
      style={{
        maxWidth: layoutConfig.containerMaxWidth,
        margin: '0',
        padding: `${layoutConfig.sidePadding}px`,
        transition: 'all 0.3s ease',
        width: '100%',
        minHeight: '100vh',
        background: '#fafbfc' // 更浅的背景色，减少对比度
      }}
    >
      {/* 简约工具栏样式 - 减少视觉干扰 */}
      <div
        className="analytics-toolbar analytics-header-card"
      >
        <div className="analytics-header">
          <div className="analytics-header-left">
            <h2 className="analytics-title">
              {headerTitle}
            </h2>
            <p className="analytics-subtitle">
              {language==='en' ? 'Authoritative labor statistics from Uzbekistan' : '基于乌兹别克斯坦官方统计的劳动力分析'}
            </p>
          </div>
          <div className="analytics-actions">
            <button
              onClick={() => setSelectorOpen(true)}
              className="btn-ghost"
              onMouseEnter={(e) => {
                e.target.style.background = '#f8f9fa';
                e.target.style.borderColor = '#bbb';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = '#fff';
                e.target.style.borderColor = '#ddd';
              }}
              style={{ color: '#333' }}
            >
              {language==='en' ? 'Manage Regions' : '管理地区'}
            </button>
            <select
              value={activeTab}
              onChange={(e) => setActiveTab(e.target.value)}
              className="select-ghost"
              style={{ color: '#333', WebkitTextFillColor: '#333' }}
            >
              <option value="overview">{language==='en' ? 'Overview' : '总览'}</option>
              <option value="employment">{language==='en' ? 'Employment' : '就业'}</option>
              <option value="wages">{language==='en' ? 'Wages' : '工资'}</option>
              <option value="education">{language==='en' ? 'Education' : '教育'}</option>
            </select>
          </div>
        </div>

        {/* Selected tray */}
        <div className="selected-tray">
          {selectedRegions.length === 0 && (
            <span style={{ color: '#888', fontSize: 12 }}>{language==='en' ? 'No regions selected. Click "Manage Regions".' : '未选择地区，点击“管理地区”。'}</span>
          )}
          {selectedRegions.map((r) => (
            <span key={r} style={{ display: 'inline-flex', alignItems: 'center', gap: 6, padding: '6px 12px', borderRadius: 999, border: `1px solid ${regionColor(r)}`, background: '#fff' }}>
              <span style={{ width: 8, height: 8, borderRadius: 8, background: regionColor(r) }} />
              <span style={{ fontSize: 12, color: '#333' }}>{r}</span>
              <button onClick={() => setSelectedRegions(prev => prev.filter(x => x !== r))} style={{ border: 'none', background: 'transparent', color: '#999', cursor: 'pointer', fontSize: 12 }}>×</button>
            </span>
          ))}
          {selectedRegions.length > 0 && (
            <button onClick={() => setSelectedRegions([])} className="btn-link-clear">{language==='en' ? 'Clear' : '清空'}</button>
          )}
        </div>
      </div>

      {/* Selector modal (virtualized list, no lag) */}
      {selectorOpen && (
        <div style={{ position: 'fixed', inset: 0, background: 'rgba(0,0,0,0.35)', zIndex: 50, display: 'flex', alignItems: 'center', justifyContent: 'center' }} onClick={() => setSelectorOpen(false)}>
          <div style={{ width: 'min(720px, 92vw)', maxHeight: '86vh', background: '#fff', borderRadius: 12, border: '1px solid #e5e7eb', boxShadow: '0 12px 30px rgba(0,0,0,0.15)', overflow: 'hidden', display: 'flex', flexDirection: 'column' }} onClick={(e)=>e.stopPropagation()}>
            <div style={{ padding: 10, borderBottom: '1px solid #eee', display: 'flex', gap: 8, alignItems: 'center', justifyContent: 'space-between' }}>
              <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
                <strong>{language==='en' ? 'Select regions' : '选择地区'}</strong>
                <span style={{ color: '#888', fontSize: 12 }}>({language==='en' ? 'Up to' : '最多'} {MAX_SELECT})</span>
              </div>
              <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
                <input placeholder={language==='en' ? 'Search region...' : '搜索地区...'} value={search} onChange={(e)=>setSearch(e.target.value)} style={{ padding: '6px 10px', border: '1px solid #ddd', borderRadius: 8, width: 240, color: '#333', backgroundColor: '#fff' }} />
                <button onClick={() => { setDraftSelected(REGION_PRESETS); }} style={{ padding: '6px 10px', borderRadius: 8, border: '1px solid #ddd', background: '#fff', cursor: 'pointer' }}>{language==='en' ? 'Presets' : '预设'}</button>
              </div>
            </div>
            {limitMsg && <div style={{ padding: '6px 12px', color: '#b91c1c', background: '#fef2f2', borderBottom: '1px solid #fee2e2', fontSize: 12 }}>{limitMsg}</div>}
            <div style={{ padding: 10, overflow: 'auto' }} onScroll={(e)=>setListScrollTop(e.currentTarget.scrollTop)}>
              {/* Virtual rows: compute visible window */}
              {(() => {
                const total = availableRegions.length;
                const viewportH = 420;
                const startIndex = Math.max(0, Math.floor(listScrollTop / ROW_H) - 5);
                const endIndex = Math.min(total, Math.ceil((listScrollTop + viewportH) / ROW_H) + 5);
                const topSpacer = startIndex * ROW_H;
                const bottomSpacer = (total - endIndex) * ROW_H;
                const slice = availableRegions.slice(startIndex, endIndex);
                return (
                  <div style={{ height: viewportH }}>
                    <div style={{ height: topSpacer }} />
                    {slice.map((r) => {
                      const checked = draftSelected.includes(r);
                      return (
                        <div key={r} style={{ display: 'flex', alignItems: 'center', gap: 10, padding: '10px 12px', border: '1px solid #f0f0f0', borderRadius: 10, marginBottom: 8, cursor: 'pointer', background: checked ? 'rgba(59,130,246,0.06)' : '#fff' }}
                          onClick={() => {
                            setLimitMsg('');
                            if (!checked) {
                              if (draftSelected.length >= MAX_SELECT) { setLimitMsg(language==='en' ? `You can select up to ${MAX_SELECT} regions` : `最多选择 ${MAX_SELECT} 个地区`); return; }
                              setDraftSelected(prev => [...prev, r]);
                            } else {
                              setDraftSelected(prev => prev.filter(x => x !== r));
                            }
                          }}
                        >
                          <span style={{ width: 10, height: 10, borderRadius: 10, background: regionColor(r) }} />
                          <span style={{ fontSize: 13, color: '#333', flex: 1 }}>{r}</span>
                          <span style={{ fontSize: 12, color: checked ? '#2563eb' : '#aaa' }}>{checked ? (language==='en'?'Selected':'已选') : ''}</span>
                        </div>
                      );
                    })}
                    <div style={{ height: bottomSpacer }} />
                  </div>
                );
              })()}
            </div>
            <div style={{ padding: 10, borderTop: '1px solid #eee', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ fontSize: 12, color: '#666' }}>{language==='en' ? 'Selected' : '已选择'}: {draftSelected.length}</div>
              <div style={{ display: 'flex', gap: 8 }}>
                <button onClick={()=>setSelectorOpen(false)} style={{ padding: '8px 12px', borderRadius: 8, border: '1px solid #ddd', background: '#fff', cursor: 'pointer' }}>{language==='en' ? 'Cancel' : '取消'}</button>
                <button onClick={()=>{ setSelectedRegions(draftSelected); setSelectorOpen(false); }} style={{ padding: '8px 12px', borderRadius: 8, border: '1px solid #2563eb', background: '#2563eb', color: '#fff', cursor: 'pointer' }}>{language==='en' ? 'Apply' : '应用'}</button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Content Area */}
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        gap: layoutConfig.sidePadding,
        paddingBottom: layoutConfig.sidePadding
      }}>
        {activeTab === 'overview' && (
          <>
            {/* 主要图表 - 优化为单列布局以提升可读性 */}
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: layoutConfig.sidePadding * 1.5
            }}>
              {/* Employment Rate Chart - 独占一行，提供更好的可读性 */}
              <div style={{
                ...chartWrapStyle,
                minHeight: layoutConfig.chartHeight + 40 // 优化高度
              }}>
                <h4 style={{
                  margin: '0 0 16px 0',
                  color: '#1f2937',
                  fontSize: 18,
                  fontWeight: 700,
                  borderBottom: '2px solid #e5e7eb',
                  paddingBottom: 8
                }}>
                  {language==='en' ? 'Employment rate (annual)' : '就业率（年度）'}
                </h4>
                <div style={{ height: chartH, width: '100%' }}>
                  {employmentRateChart ? (
                    <Line 
                      key={`employment-rate-overview-${chartKey}`}
                      datasetIdKey="label" 
                      data={employmentRateChart} 
                      options={lineOptions('', '%')} 
                      redraw={true}
                    />
                  ) : (
                    <p style={{ textAlign: 'center', color: '#666', marginTop: 60 }}>
                      {t.noData || '暂无数据'}
                    </p>
                  )}
                </div>
              </div>

              {/* Average Wages Chart - 独占一行，提供更好的可读性 */}
              <div style={{
                ...chartWrapStyle,
                minHeight: layoutConfig.chartHeight + 40 // 优化高度
              }}>
                <h4 style={{
                  margin: '0 0 16px 0',
                  color: '#1f2937',
                  fontSize: 18,
                  fontWeight: 700,
                  borderBottom: '2px solid #e5e7eb',
                  paddingBottom: 8
                }}>
                  {language==='en' ? 'Average nominal wages (quarterly)' : '名义平均工资（季度）'}
                </h4>
                <div style={{ height: chartH, width: '100%' }}>
                  {wagesQuarterlyChart ? (
                    <Line 
                      key={`wages-quarterly-${chartKey}`}
                      datasetIdKey="label" 
                      data={wagesQuarterlyChart} 
                      options={lineOptions('', language==='en'?'soums':'苏姆')}
                      redraw={true}
                    />
                  ) : (
                    <p style={{ textAlign: 'center', color: '#666', marginTop: 60 }}>
                      {t.noData || '暂无数据'}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* 辅助图表 - 使用网格布局但增强样式 */}
            <div style={getGridStyle(Math.max(1, layoutConfig.gridCols - 1))}>
              <div style={chartWrapStyle}>
                <h4 style={{
                  margin: '0 0 16px 0',
                  color: '#1f2937',
                  fontSize: 17,
                  fontWeight: 600,
                  borderBottom: '1px solid #e5e7eb',
                  paddingBottom: 8
                }}>
                  {language==='en' ? 'Number of unemployed' : '失业人数'}
                </h4>
                <div style={{ height: chartH, width: '100%' }}>
                  {unemployedChart ? (
                    <Bar key={`chart-${chartKey}`} datasetIdKey="label" data={unemployedChart} options={barOptions('', language==='en'?'thousand people':'千人')} />
                  ) : (
                    <p style={{ textAlign: 'center', color: '#666', marginTop: 40 }}>
                      {t.noData || '暂无数据'}
                    </p>
                  )}
                </div>
              </div>
              <div style={chartWrapStyle}>
                <h4 style={{
                  margin: '0 0 16px 0',
                  color: '#1f2937',
                  fontSize: 17,
                  fontWeight: 600,
                  borderBottom: '1px solid #e5e7eb',
                  paddingBottom: 8
                }}>
                  {language==='en' ? 'Labor resources (annual)' : '劳动力资源（年度）'}
                </h4>
                <div style={{ height: chartH, width: '100%' }}>
                  {laborResourcesChart ? (
                    <Line key={`chart-${chartKey}`} datasetIdKey="label" data={laborResourcesChart} options={lineOptions('', language==='en'?'thousand people':'千人')} />
                  ) : (
                    <p style={{ textAlign: 'center', color: '#666', marginTop: 40 }}>
                      {t.noData || '暂无数据'}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Full Width Industry Distribution */}
            <div style={{ width: '100%' }}>
              <IndustryDistribution
                data={industryDist}
                year={industryYear}
                language={language}
                layoutConfig={layoutConfig}
              />
            </div>
          </>
        )}

        {activeTab === 'employment' && (
          <>
            <div style={getGridStyle(layoutConfig.gridCols)}>
              <div style={chartWrapStyle}>
                <h4 style={{
                  margin: '0 0 12px 0',
                  color: '#333',
                  fontSize: 16,
                  fontWeight: 600
                }}>
                  {language==='en' ? 'Employment rate (annual)' : '就业率（年度）'}
                </h4>
                <div style={{ height: chartH, width: '100%' }}>
                  {employmentRateChart ? (
                    <Line key={`chart-${chartKey}`} datasetIdKey="label" data={employmentRateChart} options={lineOptions('', '%')} />
                  ) : (
                    <p style={{ textAlign: 'center', color: '#666', marginTop: 40 }}>
                      {t.noData || '暂无数据'}
                    </p>
                  )}
                </div>
              </div>
              <div style={chartWrapStyle}>
                <h4 style={{
                  margin: '0 0 12px 0',
                  color: '#333',
                  fontSize: 16,
                  fontWeight: 600
                }}>
                  {language==='en' ? 'Number of unemployed' : '失业人数'}
                </h4>
                <div style={{ height: chartH, width: '100%' }}>
                  {unemployedChart ? (
                    <Bar key={`chart-${chartKey}`} datasetIdKey="label" data={unemployedChart} options={barOptions('', language==='en'?'thousand people':'千人')} />
                  ) : (
                    <p style={{ textAlign: 'center', color: '#666', marginTop: 40 }}>
                      {t.noData || '暂无数据'}
                    </p>
                  )}
                </div>
              </div>
              {layoutConfig.gridCols >= 3 && (
                <div style={chartWrapStyle}>
                  <h4 style={{
                    margin: '0 0 12px 0',
                    color: '#333',
                    fontSize: 16,
                    fontWeight: 600
                  }}>
                    {language==='en' ? 'Employment trends analysis' : '就业趋势分析'}
                  </h4>
                  <div style={{ height: chartH, width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <div style={{ textAlign: 'center', color: '#666' }}>
                      <p style={{ margin: 0, fontSize: 14 }}>高级分析功能</p>
                      <p style={{ margin: '8px 0 0 0', fontSize: 12, opacity: 0.8 }}>
                        更多就业数据洞察
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Full width industry distribution for employment tab */}
            <div style={{ width: '100%' }}>
              <IndustryDistribution
                data={industryDist}
                year={industryYear}
                language={language}
                layoutConfig={layoutConfig}
              />
            </div>
          </>
        )}

        {activeTab === 'wages' && (
          <div style={{
            ...chartWrapStyle,
            maxWidth: '1200px',
            margin: '0 auto'
          }}>
            <h4 style={{
              margin: '0 0 12px 0',
              color: '#333',
              fontSize: 18,
              fontWeight: 600
            }}>
              {language==='en' ? 'Average nominal wages (quarterly)' : '名义平均工资（季度）'}
            </h4>
            <div style={{ height: layoutConfig.chartHeight * 1.2, width: '100%' }}>
              {wagesQuarterlyChart ? (
                <Line key={`chart-${chartKey}`} datasetIdKey="label" data={wagesQuarterlyChart} options={lineOptions('', language==='en'?'soums':'苏姆')}/>
              ) : (
                <p style={{ textAlign: 'center', color: '#666', marginTop: 60 }}>
                  {t.noData || '暂无数据'}
                </p>
              )}
            </div>
          </div>
        )}

        {activeTab === 'education' && (
          <>
            <div style={getGridStyle(layoutConfig.gridCols)}>
              <div style={chartWrapStyle}>
                <h4 style={{
                  margin: '0 0 12px 0',
                  color: '#333',
                  fontSize: 16,
                  fontWeight: 600
                }}>
                  {language==='en' ? 'General education institutions (city)' : '普通教育机构（城市）'}
                </h4>
                <div style={{ height: chartH, width: '100%' }}>
                  {schoolsChart ? (
                    <Bar key={`chart-${chartKey}`} datasetIdKey="label" data={schoolsChart} options={barOptions('', language==='en'?'units':'所/个')} />
                  ) : (
                    <p style={{ textAlign: 'center', color: '#666', marginTop: 40 }}>
                      {t.noData || '暂无数据'}
                    </p>
                  )}
                </div>
              </div>
              <div style={chartWrapStyle}>
                <h4 style={{
                  margin: '0 0 12px 0',
                  color: '#333',
                  fontSize: 16,
                  fontWeight: 600
                }}>
                  {language==='en' ? 'Number of colleges (city)' : '学院（城市）数量'}
                </h4>
                <div style={{ height: chartH, width: '100%' }}>
                  {collegesChart ? (
                    <Bar key={`chart-${chartKey}`} datasetIdKey="label" data={collegesChart} options={barOptions('', language==='en'?'units':'所/个')} />
                  ) : (
                    <p style={{ textAlign: 'center', color: '#666', marginTop: 40 }}>
                      {t.noData || '暂无数据'}
                    </p>
                  )}
                </div>
              </div>
              {layoutConfig.gridCols >= 3 && (
                <div style={chartWrapStyle}>
                  <h4 style={{
                    margin: '0 0 12px 0',
                    color: '#333',
                    fontSize: 16,
                    fontWeight: 600
                  }}>
                    {language==='en' ? 'Education investment trends' : '教育投资趋势'}
                  </h4>
                  <div style={{ height: chartH, width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <div style={{ textAlign: 'center', color: '#666' }}>
                      <p style={{ margin: 0, fontSize: 14 }}>教育发展分析</p>
                      <p style={{ margin: '8px 0 0 0', fontSize: 12, opacity: 0.8 }}>
                        更多教育数据洞察
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div style={chartWrapStyle}>
              <h4 style={{
                margin: '0 0 12px 0',
                color: '#333',
                fontSize: 16,
                fontWeight: 600
              }}>
                {language==='en' ? 'Graduates of 11 classes (latest)' : '11年级毕业生（最新）'}
              </h4>
              <div style={{ height: chartH, width: '100%' }}>
                {gradsChart ? (
                  <Bar key={`chart-${chartKey}`} datasetIdKey="label" data={gradsChart} options={barOptions('', language==='en'?'persons':'人')} />
                ) : (
                  <p style={{ textAlign: 'center', color: '#666', marginTop: 40 }}>
                    {t.noData || '暂无数据'}
                  </p>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </section>
  );
};

export default UzLaborAnalysis;

