import React, { useEffect, useMemo, useState } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import '../../../styles/TimeSeriesPanel.css';

// 注册Chart.js组件
try {
  ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    BarElement,
    ArcElement,
    Title,
    Tooltip,
    Legend
  );
  console.log('Chart.js registered successfully');
} catch (error) {
  console.error('Error registering Chart.js:', error);
}

// Helper: extract series from a record by keys matcher and sorted labels
function extractSeries(record, keyMatcher, labelSortFn) {
  const entries = Object.entries(record)
    .filter(([k, v]) => keyMatcher(k) && (typeof v === 'number'))
    .map(([k, v]) => ({ label: k, value: v }));
  entries.sort((a, b) => labelSortFn(a.label, b.label));
  return entries;
}

function sortQuarterLabels(a, b) {
  // a: '2024-Q1'
  const [ay, aq] = a.split('-Q');
  const [by, bq] = b.split('-Q');
  if (ay !== by) return Number(ay) - Number(by);
  return Number(aq) - Number(bq);
}

function sortYearLabels(a, b) {
  return Number(a) - Number(b);
}

const palette = [
  '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4', '#e11d48'
];

const REGION_PRESETS = [
  'Republic of Uzbekistan',
  'Tashkent region',
  'Tashkent city',
];

const UzTransportAnalysis = ({ defaultRegions = ['Republic of Uzbekistan','Tashkent region', 'Tashkent city'], t = {}, language = 'zh' }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [quarterlyTurnover, setQuarterlyTurnover] = useState([]); // Freight turnover of road transport (quarterly)
  const [annualTurnoverRoad, setAnnualTurnoverRoad] = useState([]); // Freight turnover volume of road transport (annual)
  const [annualCargoVolumeRoad, setAnnualCargoVolumeRoad] = useState([]); // Volume of cargo transportation by road (annual)
  const [totalQuarterly, setTotalQuarterly] = useState([]); // Freight turnover, total (quarterly)
  const [meta, setMeta] = useState({});
  const [selectedRegions, setSelectedRegions] = useState(defaultRegions);
  const [activeTab, setActiveTab] = useState('overview');
  const [chartType, setChartType] = useState('line');
  const [search, setSearch] = useState('');

  useEffect(() => {
    let cancelled = false;
    setLoading(true);
    (async () => {
      try {
        const [qRes, aTurnRes, aVolRes, totalQRes] = await Promise.all([
          fetch('/data/Uzbekistan_data/Transportation/Freight turnover of road transport (quarterly).json'),
          fetch('/data/Uzbekistan_data/Transportation/Freight turnover volume of road transport (annual).json'),
          fetch('/data/Uzbekistan_data/Transportation/Volume of cargo transportation by road (annual).json'),
          fetch('/data/Uzbekistan_data/Transportation/Freight turnover, total (quarterly).json'),
        ]);
        const [qJson, aTurnJson, aVolJson, totalQJson] = await Promise.all([
          qRes.json(), aTurnRes.json(), aVolRes.json(), totalQRes.json()
        ]);
        if (cancelled) return;
        setQuarterlyTurnover(Array.isArray(qJson["Ma'lumot"]) ? qJson["Ma'lumot"] : []);
        setAnnualTurnoverRoad(Array.isArray(aTurnJson["Ma'lumot"]) ? aTurnJson["Ma'lumot"] : []);
        setAnnualCargoVolumeRoad(Array.isArray(aVolJson["Ma'lumot"]) ? aVolJson["Ma'lumot"] : []);
        setTotalQuarterly(Array.isArray(totalQJson["Ma'lumot"]) ? totalQJson["Ma'lumot"] : []);
        setMeta({
          quarterly: qJson["Metama'lumot"] || [],
          annualTurnover: aTurnJson["Metama'lumot"] || [],
          annualCargo: aVolJson["Metama'lumot"] || []
        });
        setLoading(false);
      } catch (e) {
        console.error('Error loading transport data', e);
        if (!cancelled) {
          setError('Failed to load Uzbekistan transport data');
          setLoading(false);
        }
      }
    })();
    return () => { cancelled = true; };
  }, []);

  const availableRegions = useMemo(() => {
    try {
    const set = new Set();
    [...quarterlyTurnover, ...annualTurnoverRoad, ...annualCargoVolumeRoad].forEach(r => {
      if (r?.Klassifikator_en) set.add(r.Klassifikator_en);
    });
    let arr = Array.from(set);
    if (search) {
      const q = search.toLowerCase();
      arr = arr.filter(n => n.toLowerCase().includes(q));
    }
    // Keep common presets on top
    arr.sort((a, b) => {
      const ai = REGION_PRESETS.indexOf(a);
      const bi = REGION_PRESETS.indexOf(b);
      if (ai !== -1 || bi !== -1) return (ai === -1 ? 999 : ai) - (bi === -1 ? 999 : bi);
      return a.localeCompare(b);
    });
    return arr;
    } catch (error) {
      console.error('Error processing available regions:', error);
      return ['Republic of Uzbekistan', 'Tashkent region', 'Tashkent city'];
    }
  }, [quarterlyTurnover, annualTurnoverRoad, annualCargoVolumeRoad, totalQuarterly, search]);

  const toggleRegion = (region) => {
    setSelectedRegions(prev => prev.includes(region) ? prev.filter(r => r !== region) : [...prev, region]);
  };

  const regionColor = (region) => {
    const idx = REGION_PRESETS.includes(region)
      ? REGION_PRESETS.indexOf(region)
      : (availableRegions.indexOf(region) % palette.length);
    return palette[idx % palette.length];
  };

  const buildDataset = (records, keyMatcher, sorter) => {
    try {
    const labelsSet = new Set();
    const seriesByRegion = {};

    selectedRegions.forEach(region => {
      const rec = records.find(r => r.Klassifikator_en === region);
      if (!rec) return;
      const points = extractSeries(rec, keyMatcher, sorter);
      points.forEach(p => labelsSet.add(p.label));
      seriesByRegion[region] = points.reduce((acc, p) => ({ ...acc, [p.label]: p.value }), {});
    });

    const labels = Array.from(labelsSet);
    labels.sort(sorter);

    const datasets = selectedRegions
      .map(region => {
        const color = regionColor(region);
        return {
          label: region,
          data: labels.map(l => seriesByRegion[region]?.[l] ?? null),
          borderColor: color,
          backgroundColor: color + '55',
          tension: 0.25,
          spanGaps: true,
        };
      });

    return { labels, datasets };
    } catch (error) {
      console.error('Error building dataset:', error);
      return { labels: [], datasets: [] };
    }
  };

  const lineOptions = (title, yLabel) => ({
    responsive: true,
    maintainAspectRatio: false,
    interaction: { mode: 'index', intersect: false },
    plugins: {
      legend: { position: 'bottom' },
      title: { display: true, text: title },
      tooltip: {
        callbacks: {
          label: (ctx) => {
            const v = ctx.parsed.y;
            const num = (v ?? 0).toLocaleString();
            return `${ctx.dataset.label}: ${num}${yLabel ? ' ' + yLabel : ''}`;
          }
        }
      }
    },
    scales: {
      x: { ticks: { maxRotation: 0, autoSkip: true } },
      y: { beginAtZero: true }
    }
  });

  const barOptions = (title, yLabel) => ({
    responsive: true,
    maintainAspectRatio: false,
    interaction: { mode: 'index', intersect: false },
    plugins: {
      legend: { position: 'bottom' },
      title: { display: true, text: title },
      tooltip: {
        callbacks: {
          label: (ctx) => {
            const v = ctx.parsed.y;
            const num = (v ?? 0).toLocaleString();
            return `${ctx.dataset.label}: ${num}${yLabel ? ' ' + yLabel : ''}`;
          }
        }
      }
    },
    scales: {
      y: { beginAtZero: true }
    }
  });

  // 计算关键指标
  const keyMetrics = useMemo(() => {
    if (quarterlyTurnover.length === 0) return null;

    const latestQuarter = quarterlyTurnover.find(r => r.Klassifikator_en === 'Republic of Uzbekistan');
    if (!latestQuarter) return null;

    const quarters = Object.keys(latestQuarter).filter(k => /^\d{4}-Q[1-4]$/.test(k));
    const latestQ = quarters.sort((a, b) => sortQuarterLabels(a, b)).pop();

    const currentValue = latestQuarter[latestQ];
    const prevQuarter = quarters[quarters.length - 2];
    const prevValue = prevQuarter ? latestQuarter[prevQuarter] : currentValue;

    const growth = prevValue ? ((currentValue - prevValue) / prevValue * 100) : 0;

    return {
      latestQuarter: latestQ,
      currentValue: currentValue?.toLocaleString(),
      growth: growth.toFixed(1),
      trend: growth > 0 ? 'up' : 'down'
    };
  }, [quarterlyTurnover]);

  // 区域对比数据
  const regionalComparison = useMemo(() => {
    if (quarterlyTurnover.length === 0) return null;

    const latestData = {};
    quarterlyTurnover.forEach(region => {
      const quarters = Object.keys(region).filter(k => /^\d{4}-Q[1-4]$/.test(k));
      const latestQ = quarters.sort((a, b) => sortQuarterLabels(a, b)).pop();
      if (latestQ) {
        latestData[region.Klassifikator_en] = region[latestQ] || 0;
      }
    });

    return Object.entries(latestData)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10);
  }, [quarterlyTurnover]);

  // 导航按钮样式
  const navButtonStyle = {
    padding: '6px 8px',
    border: '1px solid #667eea',
    background: 'white',
    color: '#667eea',
    borderRadius: '3px',
    cursor: 'pointer',
    fontSize: '11px',
    fontWeight: '500',
    transition: 'all 0.2s ease'
  };

  if (loading) {
    return (
      <div className="analytics-container">
        <h2>{language==='en'?'Logistics & Transport Analysis':'🚚 交通运输分析系统'}</h2>
        <div style={{ textAlign: 'center', padding: 40 }}>🔄 {language==='en'?'Loading...':'加载中...'}</div>
      </div>
    );
  }
  if (error) {
    return (
      <div className="analytics-container" style={{ background: '#ffebee', border: '1px solid #f44336' }}>
        <h2 style={{ color: '#d32f2f' }}>{language==='en'?'Logistics & Transport Analysis':'🚚 交通运输分析系统'}</h2>
        <div style={{ textAlign: 'center', padding: 40 }}>❌ {language==='en'?'Failed to load Uzbekistan transport data':'数据加载失败'}</div>
      </div>
    );
  }

  // Debug info
  console.log('Transport data loaded:', {
    quarterlyTurnover: quarterlyTurnover.length,
    annualTurnoverRoad: annualTurnoverRoad.length,
    annualCargoVolumeRoad: annualCargoVolumeRoad.length,
    totalQuarterly: totalQuarterly.length,
    availableRegions: availableRegions.length
  });

  // Safety check: ensure we have at least some data to display
  if (quarterlyTurnover.length === 0 && annualTurnoverRoad.length === 0 &&
      annualCargoVolumeRoad.length === 0 && totalQuarterly.length === 0) {
    return (
      <div className="time-series-panel show" style={{ padding: 16, backgroundColor: '#fff3cd', minHeight: '200px', border: '1px solid #ffeaa7', position: 'relative', transform: 'none' }}>
        <h2 style={{ color: '#856404' }}>{t.transportConnections || 'Transport Analysis'}</h2>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <div style={{ color: '#856404', fontSize: '16px' }}>⚠️ {language==='en'?'No data':'暂无数据'}</div>
          <div style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>
            {language==='en'?'No Uzbekistan transport data found. Please check data files.':'未找到乌兹别克斯坦交通数据，请检查数据文件。'}
          </div>
        </div>
      </div>
    );
  }

  const quarterlyData = buildDataset(
    quarterlyTurnover,
    (k) => /^\d{4}-Q[1-4]$/.test(k),
    sortQuarterLabels
  );

  const annualTurnoverData = buildDataset(
    annualTurnoverRoad,
    (k) => /^\d{4}$/.test(k),
    sortYearLabels
  );

  const annualCargoData = buildDataset(
    annualCargoVolumeRoad,
    (k) => /^\d{4}$/.test(k),
    sortYearLabels
  );

  const unitQuarterly = "million. ton-km"; // from metadata
  const unitAnnualTurnover = "million t-km";
  const unitAnnualCargo = "mln. Tons";

  // Total quarterly line (single series)
  const totalQuarterlySeries = (() => {
    const rec = totalQuarterly[0];
    if (!rec) return { labels: [], datasets: [] };
    const points = extractSeries(rec, (k) => /^\d{4}-Q[1-4]$/.test(k), sortQuarterLabels);
    const labels = points.map(p => p.label);
    const datasets = [{
      label: 'Freight turnover, total (quarterly) — Uzbekistan',
      data: points.map(p => p.value),
      borderColor: '#111827',
      backgroundColor: '#11182755',
      tension: 0.25,
    }];
    return { labels, datasets };
  })();

  return (
    <section className="analytics-container">
      <div className="analytics-toolbar">
        <h2 style={{ margin: '0 0 4px 0', color: '#333', fontSize: '20px', fontWeight: '700', display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
          {language==='en'?'Logistics & Transport Analysis':'🚚 交通运输分析系统'}
        </h2>
        <p style={{ margin: 0, color: '#666', fontSize: '12px' }}>
          {language === 'en' ? 'Logistics and transport analytics based on Uzbekistan statistics' : '基于乌兹别克斯坦统计数据提供专业的物流运输分析'}
        </p>
      </div>

      {/* 关键指标卡片 */}
      {keyMetrics && (
        <div style={{ marginBottom: '12px' }}>
          <div style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            padding: '12px 16px',
            borderRadius: '6px',
            color: 'white'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <div style={{ fontSize: '12px', opacity: 0.9 }}>
                  最新季度货物周转量 ({keyMetrics.latestQuarter})
                </div>
                <div style={{ fontSize: '20px', fontWeight: 'bold', margin: '2px 0' }}>
                  {keyMetrics.currentValue} 万吨公里
                </div>
              </div>
              <div style={{ textAlign: 'right' }}>
                <div style={{ fontSize: '12px', opacity: 0.9 }}>环比增长</div>
                <div style={{
                  fontSize: '16px',
                  fontWeight: 'bold',
                  color: keyMetrics.trend === 'up' ? '#4ade80' : '#f87171'
                }}>
                  {keyMetrics.trend === 'up' ? '↗' : '↘'} {keyMetrics.growth}%
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 标签页导航 */}
      <div style={{ marginBottom: '12px' }}>
        <div style={{ display: 'flex', gap: '2px', borderBottom: '1px solid #e0e0e0' }}>
          {[
            { id: 'overview', label: '总览' },
            { id: 'quarterly', label: '季度' },
            { id: 'annual', label: '年度' },
            { id: 'regional', label: '区域' }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              style={{
                padding: '8px 12px',
                border: 'none',
                background: activeTab === tab.id ? '#667eea' : 'transparent',
                color: activeTab === tab.id ? 'white' : '#666',
                borderRadius: '4px 4px 0 0',
                fontSize: '13px',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* 控制面板 */}
      <div className="analytics-controls" style={{ marginBottom: '12px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          <span style={{ fontSize: '11px', color: '#666' }}>{language==='en'?'Chart:':'图表:'}</span>
          <div style={{ display:'inline-flex', gap:4, background:'#f3f4f6', padding:2, borderRadius:6, border:'1px solid #e5e7eb' }}>
            {[{id:'line', label: language==='en'?'Line':'折线'}, {id:'bar', label: language==='en'?'Bar':'柱状'}].map(opt => (
              <button key={opt.id} onClick={()=>setChartType(opt.id)} className="analytics-chip" style={{
                padding:'4px 10px', borderRadius:6, border:'none',
                background: chartType===opt.id? '#667eea' : 'transparent',
                color: chartType===opt.id? '#fff' : '#374151', fontSize:11, fontWeight:600
              }}>{opt.label}</button>
            ))}
          </div>
        </div>

        {/* 地区选择器 */}
        <div style={{ display: 'flex', gap: '4px', flexWrap: 'wrap', alignItems: 'center' }}>
          <input className="analytics-search" placeholder={t.searchRegions || (language==='en'?'Search regions':'搜索地区')} onChange={(e)=>setSearch(e.target.value)} />
          <button onClick={()=>setSelectedRegions(['Republic of Uzbekistan'])} style={navButtonStyle}>{t.onlyNational || (language==='en'?'Only national':'仅全国')}</button>
          <button onClick={()=>setSelectedRegions([])} style={navButtonStyle}>{language==='en'?'Clear':'清空'}</button>
          <button onClick={()=>setSelectedRegions(availableRegions)} style={navButtonStyle}>{language==='en'?'Select all':'全选'}</button>
          {availableRegions.map(region => (
            <button
              key={region}
              onClick={() => toggleRegion(region)}
              style={{
                padding: '4px 8px',
                border: `1px solid ${regionColor(region)}`,
                background: selectedRegions.includes(region) ? regionColor(region) : 'white',
                color: selectedRegions.includes(region) ? 'white' : regionColor(region),
                borderRadius: '12px',
                fontSize: '10px',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
            >
              {region}
            </button>
          ))}
        </div>
      </div>

            {/* 标签页内容 */}
      <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {activeTab === 'overview' && (
          <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {/* 上半部分 - 图表和排行榜并排 */}
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px', flex: 1 }}>
              {/* 季度总览图表 */}
              <div style={{ background: 'white', padding: '12px', borderRadius: '6px', border: '1px solid #e0e0e0', display: 'flex', flexDirection: 'column' }}>
                <h4 style={{ margin: '0 0 8px 0', fontSize: '14px', color: '#333' }}>季度总货物周转量</h4>
                <div style={{ flex: 1, minHeight: '300px' }}>
                  {totalQuarterlySeries.labels.length > 0 ? (
                    chartType === 'line' ? (
                      <Line data={totalQuarterlySeries} options={lineOptions('', 'bln. ton-km')} />
                    ) : (
                      <Bar data={totalQuarterlySeries} options={barOptions('', 'bln. ton-km')} />
                    )
                  ) : (
                    <div style={{ textAlign: 'center', padding: '60px', color: '#999' }}>
                      {language==='en'?'No data':'暂无数据'}
                    </div>
                  )}
                </div>
              </div>

              {/* 区域排行榜 */}
              <div style={{ background: 'white', padding: '12px', borderRadius: '6px', border: '1px solid #e0e0e0', display: 'flex', flexDirection: 'column' }}>
                <h4 style={{ margin: '0 0 8px 0', fontSize: '14px', color: '#333' }}>区域贡献排行</h4>
                <div style={{ flex: 1, overflowY: 'auto' }}>
                  {regionalComparison ? (
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '6px' }}>
                      {regionalComparison.map(([region, value], index) => (
                        <div key={region} style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          padding: '8px 10px',
                          background: index < 3 ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : '#f9f9f9',
                          color: index < 3 ? 'white' : '#333',
                          borderRadius: '4px',
                          borderLeft: `4px solid ${regionColor(region)}`
                        }}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <span style={{
                              background: index < 3 ? 'rgba(255,255,255,0.3)' : regionColor(region),
                              color: index < 3 ? 'white' : 'white',
                              padding: '2px 6px',
                              borderRadius: '8px',
                              fontSize: '10px',
                              fontWeight: 'bold',
                              minWidth: '24px',
                              textAlign: 'center'
                            }}>
                              #{index + 1}
                            </span>
                            <span style={{ fontWeight: '500', fontSize: '12px' }}>{region}</span>
                          </div>
                          <div style={{ textAlign: 'right' }}>
                            <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                              {value.toLocaleString()}
                            </div>
                            <div style={{ fontSize: '10px', opacity: 0.8 }}>
                              万吨公里
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div style={{ textAlign: 'center', padding: '60px', color: '#999' }}>
                      {language==='en'?'No data':'暂无数据'}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 下半部分 - 信息卡片 */}
            <div style={{ display: 'grid', gridTemplateColumns: '2fr 1fr', gap: '12px' }}>
              {/* 数据概览 */}
              <div style={{ background: 'white', padding: '12px', borderRadius: '6px', border: '1px solid #e0e0e0' }}>
                <h4 style={{ margin: '0 0 10px 0', fontSize: '14px', color: '#333' }}>数据概览</h4>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
                  <div style={{ textAlign: 'center', padding: '8px', background: '#f8f9fa', borderRadius: '4px' }}>
                    <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#667eea' }}>
                      {quarterlyTurnover.length + annualTurnoverRoad.length + annualCargoVolumeRoad.length + totalQuarterly.length}
                    </div>
                    <div style={{ fontSize: '10px', color: '#666' }}>总记录数</div>
                  </div>
                  <div style={{ textAlign: 'center', padding: '8px', background: '#f8f9fa', borderRadius: '4px' }}>
                    <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#667eea' }}>
                      {availableRegions.length}
                    </div>
                    <div style={{ fontSize: '10px', color: '#666' }}>区域数量</div>
                  </div>
                  <div style={{ textAlign: 'center', padding: '8px', background: '#f8f9fa', borderRadius: '4px' }}>
                    <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#667eea' }}>
                      2024-2025
                    </div>
                    <div style={{ fontSize: '10px', color: '#666' }}>时间跨度</div>
                  </div>
                  <div style={{ textAlign: 'center', padding: '8px', background: '#f8f9fa', borderRadius: '4px' }}>
                    <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#667eea' }}>
                      实时
                    </div>
                    <div style={{ fontSize: '10px', color: '#666' }}>数据状态</div>
                  </div>
                </div>
              </div>

              {/* 快速导航 */}
              <div style={{ background: 'white', padding: '12px', borderRadius: '6px', border: '1px solid #e0e0e0' }}>
                <h4 style={{ margin: '0 0 10px 0', fontSize: '14px', color: '#333' }}>快速导航</h4>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                  <button onClick={() => setActiveTab('quarterly')} style={navButtonStyle}>季度趋势</button>
                  <button onClick={() => setActiveTab('annual')} style={navButtonStyle}>年度对比</button>
                  <button onClick={() => setActiveTab('regional')} style={navButtonStyle}>区域分析</button>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'quarterly' && (
          <div style={{ flex: 1, display: 'flex', flexDirection: 'column', background: 'white', padding: '12px', borderRadius: '6px', border: '1px solid #e0e0e0' }}>
            <h4 style={{ margin: '0 0 8px 0', fontSize: '14px', color: '#333' }}>季度货物周转量对比</h4>
            <div style={{ flex: 1, minHeight: '400px' }}>
              {quarterlyData.labels.length > 0 ? (
                chartType === 'line' ? (
                  <Line data={quarterlyData} options={lineOptions('', unitQuarterly)} />
                ) : (
                  <Bar data={quarterlyData} options={barOptions('', unitQuarterly)} />
                )
              ) : (
                <div style={{ textAlign: 'center', padding: '80px', color: '#666' }}>
                  {language==='en'?'No quarterly data':'暂无季度数据'}
                </div>
              )}
            </div>
          </div>
        )}

                {activeTab === 'annual' && (
          <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {/* 年度对比图表 */}
            <div style={{ flex: 1, display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
              <div style={{ background: 'white', padding: '12px', borderRadius: '6px', border: '1px solid #e0e0e0', display: 'flex', flexDirection: 'column' }}>
                <h4 style={{ margin: '0 0 8px 0', fontSize: '14px', color: '#333' }}>货物周转量</h4>
                <div style={{ flex: 1, minHeight: '300px' }}>
                  {annualTurnoverData.labels.length > 0 ? (
                    chartType === 'line' ? (
                      <Line data={annualTurnoverData} options={lineOptions('', unitAnnualTurnover)} />
                    ) : (
                      <Bar data={annualTurnoverData} options={barOptions('', unitAnnualTurnover)} />
                    )
                  ) : (
                    <div style={{ textAlign: 'center', padding: '60px', color: '#666' }}>
                      {language==='en'?'No data':'暂无数据'}
                    </div>
                  )}
                </div>
              </div>

              <div style={{ background: 'white', padding: '12px', borderRadius: '6px', border: '1px solid #e0e0e0', display: 'flex', flexDirection: 'column' }}>
                <h4 style={{ margin: '0 0 8px 0', fontSize: '14px', color: '#333' }}>货物运输量</h4>
                <div style={{ flex: 1, minHeight: '300px' }}>
                  {annualCargoData.labels.length > 0 ? (
                    chartType === 'line' ? (
                      <Line data={annualCargoData} options={lineOptions('', unitAnnualCargo)} />
                    ) : (
                      <Bar data={annualCargoData} options={barOptions('', unitAnnualCargo)} />
                    )
                  ) : (
                    <div style={{ textAlign: 'center', padding: '60px', color: '#666' }}>
                      {language==='en'?'No data':'暂无数据'}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 数据洞察 */}
            <div style={{ background: 'white', padding: '12px', borderRadius: '6px', border: '1px solid #e0e0e0' }}>
              <h4 style={{ margin: '0 0 10px 0', fontSize: '14px', color: '#333' }}>数据洞察</h4>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '10px' }}>
                <div style={{ padding: '10px', background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)', borderRadius: '4px' }}>
                  <div style={{ fontSize: '12px', fontWeight: '600', color: '#1976d2', marginBottom: '4px' }}>📈 增长趋势</div>
                  <div style={{ fontSize: '11px', color: '#424242', lineHeight: '1.4' }}>
                    货物运输量整体呈增长趋势，反映了物流基础设施的改善和经济发展的积极影响。
                  </div>
      </div>

                <div style={{ padding: '10px', background: 'linear-gradient(135deg, #f3e5f5 0%, #ce93d8 100%)', borderRadius: '4px' }}>
                  <div style={{ fontSize: '12px', fontWeight: '600', color: '#7b1fa2', marginBottom: '4px' }}>🎯 区域差异</div>
                  <div style={{ fontSize: '11px', color: '#424242', lineHeight: '1.4' }}>
                    区域间存在显著差异，塔什干地区凭借其枢纽地位保持领先，其他区域潜力巨大。
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

                {activeTab === 'regional' && (
          <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {/* 区域对比图表 */}
            <div style={{ flex: 1, background: 'white', padding: '12px', borderRadius: '6px', border: '1px solid #e0e0e0', display: 'flex', flexDirection: 'column' }}>
              <h4 style={{ margin: '0 0 8px 0', fontSize: '14px', color: '#333' }}>区域详细对比</h4>
              <div style={{ flex: 1, minHeight: '400px' }}>
                {quarterlyData.labels.length > 0 ? (
                  chartType === 'line' ? (
                    <Line data={quarterlyData} options={lineOptions('', unitQuarterly)} />
                  ) : (
                    <Bar data={quarterlyData} options={barOptions('', unitQuarterly)} />
                  )
                ) : (
                  <div style={{ textAlign: 'center', padding: '80px', color: '#666' }}>
                    {language==='en'?'No regional data':'暂无区域数据'}
                  </div>
                )}
              </div>
      </div>

            {/* 区域分析洞察 */}
            <div style={{ background: 'white', padding: '12px', borderRadius: '6px', border: '1px solid #e0e0e0' }}>
              <h4 style={{ margin: '0 0 10px 0', fontSize: '14px', color: '#333' }}>区域分析洞察</h4>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '10px' }}>
                <div style={{ padding: '10px', background: 'linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%)', borderRadius: '4px' }}>
                  <div style={{ fontSize: '12px', fontWeight: '600', color: '#2e7d32', marginBottom: '4px' }}>🏆 领跑区域</div>
                  <div style={{ fontSize: '11px', color: '#424242', lineHeight: '1.4' }}>
                    塔什干地区凭借其首都地位和发达的交通网络，一直保持着最高的货物周转量。
                  </div>
      </div>

                <div style={{ padding: '10px', background: 'linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%)', borderRadius: '4px' }}>
                  <div style={{ fontSize: '12px', fontWeight: '600', color: '#f57c00', marginBottom: '4px' }}>📈 增长潜力</div>
                  <div style={{ fontSize: '11px', color: '#424242', lineHeight: '1.4' }}>
                    其他区域随着基础设施完善和经济发展，运输量有望快速提升。
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 数据来源信息 */}
      <div style={{
        marginTop: '8px',
        padding: '8px 12px',
        background: 'linear-gradient(135deg, #f5f5f5 0%, #eeeeee 100%)',
        borderRadius: '4px',
        fontSize: '11px',
        color: '#666',
        border: '1px solid #e0e0e0'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>📄 {(t.dataSource || (language==='en' ? 'Data Source' : '数据来源'))}: {language==='en'?'Statistics Agency of Uzbekistan':'乌兹别克斯坦统计局'} | {quarterlyTurnover.length + annualTurnoverRoad.length + annualCargoVolumeRoad.length + totalQuarterly.length}</span>
          <span style={{ background: '#667eea', color: 'white', padding: '2px 6px', borderRadius: '8px', fontSize: '9px' }}>
            {language==='en'?'Live update':'实时更新'}
          </span>
        </div>
        {meta?.quarterly?.length > 0 && (
          <div className="analytics-footer">
            <span>{(t.dataSource || (language==='en' ? 'Data Source' : '数据来源'))}: {language==='en'?'Statistics Agency of Uzbekistan':'乌兹别克斯坦统计局'}</span>
            {(() => {
              const unit = meta.quarterly.find(m=>m.name_en==='Unit of measurement')?.value_en;
              const last = meta.quarterly.find(m=>m.name_en==='Last modified date')?.value_en;
              return (
                <span style={{ marginLeft: 8 }}>
                  {unit ? `${(t.unit|| (language==='en'?'Unit':'单位'))}: ${unit}` : ''}
                  {last ? ` · ${(t.updated || (language==='en'?'Updated':'更新'))}: ${last}` : ''}
                </span>
              );
            })()}
          </div>
        )}
      </div>
    </section>
  );
};

export default UzTransportAnalysis;

