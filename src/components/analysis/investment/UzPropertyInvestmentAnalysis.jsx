import React, { useEffect, useMemo, useState } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import '../../../styles/TimeSeriesPanel.css';

// 注册Chart.js组件（保持与 UzTransportAnalysis 一致）
try {
  ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    BarElement,
    ArcElement,
    Title,
    Tooltip,
    Legend
  );
} catch (e) {
  // 忽略重复注册错误
}

// 工具函数
function extractSeries(record, keyMatcher, sorter) {
  const entries = Object.entries(record)
    .filter(([k, v]) => keyMatcher(k) && typeof v === 'number')
    .map(([k, v]) => ({ label: k, value: v }));
  entries.sort((a, b) => sorter(a.label, b.label));
  return entries;
}
const sortYear = (a, b) => Number(a) - Number(b);
function sortQuarter(a, b) {
  const [ay, aq] = a.split('-Q');
  const [by, bq] = b.split('-Q');
  if (ay !== by) return Number(ay) - Number(by);
  return Number(aq) - Number(bq);
}

const palette = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4', '#e11d48'];
const REGION_PRESETS = ['Republic of Uzbekistan', 'Tashkent region', 'Tashkent city'];


const TAB_STATE_KEY = 'UzPropertyInvestmentAnalysis.activeTab';

const UzPropertyInvestmentAnalysis = ({ defaultRegions = ['Republic of Uzbekistan','Tashkent region', 'Tashkent city'], t = {}, language = 'zh' }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 数据集
  const [shareAttracted, setShareAttracted] = useState([]);
  const [shareBankLoans, setShareBankLoans] = useState([]);
  const [shareForeign, setShareForeign] = useState([]);
  const [shareOwnFunds, setShareOwnFunds] = useState([]);
  const [shareStateBudget, setShareStateBudget] = useState([]);
  const [shareGdp, setShareGdp] = useState([]);
  const [perCapitaQ, setPerCapitaQ] = useState([]);
  const [assetTypes, setAssetTypes] = useState([]); // 仅国家层级，分类维度

  const [selectedRegions, setSelectedRegions] = useState(defaultRegions);
  const [chartType, setChartType] = useState('line');
  const [activeTab, setActiveTab] = useState('overview');
  const [searchQuery, setSearchQuery] = useState('');


  // 持久化当前标签页到 sessionStorage，刷新后保持当前位置
  useEffect(() => {
    try {
      const saved = sessionStorage.getItem(TAB_STATE_KEY) || localStorage.getItem(TAB_STATE_KEY);
      if (saved) setActiveTab(saved);
    } catch {}
  }, []);
  useEffect(() => {
    try {
      sessionStorage.setItem(TAB_STATE_KEY, activeTab);
    } catch {}
  }, [activeTab]);

  const [meta, setMeta] = useState({});
  useEffect(() => {
    let cancelled = false;
    setLoading(true);
    (async () => {
      try {
        const paths = [
          '/data/Uzbekistan_data/Property_Investment/Share of attracted funds in investments in fixed assets.json',
          '/data/Uzbekistan_data/Property_Investment/Share of bank loans and other borrowed funds in investments in fixed assets.json',
          '/data/Uzbekistan_data/Property_Investment/Share of foreign investments and loans in investments in fixed assets.json',
          '/data/Uzbekistan_data/Property_Investment/Share of own funds of enterprises and individuals in investments in fixed assets.json',
          '/data/Uzbekistan_data/Property_Investment/Share of the state budget of investments in fixed assets.json',
          '/data/Uzbekistan_data/Property_Investment/Share of investment in fixed assets in GDP.json',

          '/data/Uzbekistan_data/Property_Investment/The volume of investments in fixed assets per capita (quarterly).json',
          '/data/Uzbekistan_data/Property_Investment/Structure of investments in fixed assets by types of fixed assets.json'
        ];
        const resps = await Promise.all(paths.map(p => fetch(p)));
        const jsons = await Promise.all(resps.map(r => r.json()));
        if (cancelled) return;
        const [jAttracted, jBank, jForeign, jOwn, jState, jGdp, jPerCapitaQ, jTypes] = jsons;
        setShareAttracted(Array.isArray(jAttracted["Ma'lumot"]) ? jAttracted["Ma'lumot"] : []);
        setShareBankLoans(Array.isArray(jBank["Ma'lumot"]) ? jBank["Ma'lumot"] : []);
        setShareForeign(Array.isArray(jForeign["Ma'lumot"]) ? jForeign["Ma'lumot"] : []);
        setShareOwnFunds(Array.isArray(jOwn["Ma'lumot"]) ? jOwn["Ma'lumot"] : []);
        setShareStateBudget(Array.isArray(jState["Ma'lumot"]) ? jState["Ma'lumot"] : []);
        setShareGdp(Array.isArray(jGdp["Ma'lumot"]) ? jGdp["Ma'lumot"] : []);
        setPerCapitaQ(Array.isArray(jPerCapitaQ["Ma'lumot"]) ? jPerCapitaQ["Ma'lumot"] : []);
        const metaExtract = (j) => Array.isArray(j["Metama'lumot"]) ? j["Metama'lumot"] : [];
        const metaMap = {
          attracted: metaExtract(jAttracted),
          bank: metaExtract(jBank),
          foreign: metaExtract(jForeign),
          own: metaExtract(jOwn),
          state: metaExtract(jState),
          gdp: metaExtract(jGdp),
          perCapitaQ: metaExtract(jPerCapitaQ),
          types: metaExtract(jTypes),
        };
        setMeta(metaMap);

        setAssetTypes(Array.isArray(jTypes["Ma'lumot"]) ? jTypes["Ma'lumot"] : []);
        setError(null);
      } catch (e) {
        console.error('Error loading property investment data', e);
        if (!cancelled) setError('Failed to load property investment data');
      } finally {
        if (!cancelled) setLoading(false);
      }
    })();
    return () => { cancelled = true; };
  }, []);

  const availableRegions = useMemo(() => {
    try {
      const set = new Set();
      [shareAttracted, shareBankLoans, shareForeign, shareOwnFunds, shareStateBudget, shareGdp, perCapitaQ].forEach(arr => {
        arr.forEach(r => r?.Klassifikator_en && set.add(r.Klassifikator_en));
      });
      let arr = Array.from(set);
      if (searchQuery) {
        const q = searchQuery.toLowerCase();
        arr = arr.filter(name => name.toLowerCase().includes(q));
      }
      arr.sort((a, b) => {
        const ai = REGION_PRESETS.indexOf(a);
        const bi = REGION_PRESETS.indexOf(b);
        if (ai !== -1 || bi !== -1) return (ai === -1 ? 999 : ai) - (bi === -1 ? 999 : bi);
        return a.localeCompare(b);
      });
      return arr;
    } catch {
      return ['Republic of Uzbekistan', 'Tashkent region', 'Tashkent city'];
    }
  }, [shareAttracted, shareBankLoans, shareForeign, shareOwnFunds, shareStateBudget, shareGdp, perCapitaQ, searchQuery]);

  const toggleRegion = (region) => {
    setSelectedRegions(prev => prev.includes(region) ? prev.filter(r => r !== region) : [...prev, region]);
  };
  const regionColor = (region) => {
    const idx = REGION_PRESETS.includes(region) ? REGION_PRESETS.indexOf(region) : (availableRegions.indexOf(region) % palette.length);
    return palette[idx % palette.length];
  };

  // 通用dataset构建器（区域+年份）
  const buildDataset = (records, keyMatcher, sorter) => {
    const labelsSet = new Set();
    const byRegion = {};
    selectedRegions.forEach(r => {
      const rec = records.find(x => x.Klassifikator_en === r);
      if (!rec) return;
      const pts = extractSeries(rec, keyMatcher, sorter);
      pts.forEach(p => labelsSet.add(p.label));
      byRegion[r] = pts.reduce((acc, p) => ({ ...acc, [p.label]: p.value }), {});
    });
    const labels = Array.from(labelsSet);
    labels.sort(sorter);
    const datasets = selectedRegions.map(region => {
      const color = regionColor(region);
      return {
        label: region,
        data: labels.map(l => byRegion[region]?.[l] ?? null),
        borderColor: color,
        backgroundColor: color + '55',
        tension: 0.25,
        spanGaps: true,
      };
    });
    return { labels, datasets };
  };

  const lineOptions = (title, unit) => ({
    responsive: true,
    maintainAspectRatio: false,
    interaction: { mode: 'index', intersect: false },
    plugins: {
      legend: { position: 'bottom' },
      title: { display: !!title, text: title },
      tooltip: { callbacks: { label: (ctx) => `${ctx.dataset.label}: ${(ctx.parsed.y ?? 0).toLocaleString()}${unit ? ' ' + unit : ''}` } }
    },
    scales: { y: { beginAtZero: true } }
  });
  const barOptions = (title, unit) => ({
    responsive: true,
    maintainAspectRatio: false,
    interaction: { mode: 'index', intersect: false },
    plugins: {
      legend: { position: 'bottom' },
      title: { display: !!title, text: title },
      tooltip: { callbacks: { label: (ctx) => `${ctx.dataset.label}: ${(ctx.parsed.y ?? 0).toLocaleString()}${unit ? ' ' + unit : ''}` } }
    },
    scales: { y: { beginAtZero: true } }
  });

  // 派生数据
  const perCapitaQuarterly = useMemo(() => buildDataset(perCapitaQ, (k) => /^\d{4}-Q[1-4]$/.test(k), sortQuarter), [perCapitaQ, selectedRegions]);
  const gdpShareSeries = useMemo(() => buildDataset(shareGdp, (k) => /^\d{4}$/.test(k), sortYear), [shareGdp, selectedRegions]);

  // 资金来源（同年结构）— 取最新年份，计算全国（Republic of Uzbekistan）的结构
  const fundingShareLatest = useMemo(() => {
    try {
      const usz = 'Republic of Uzbekistan';
      const rAttracted = shareAttracted.find(r => r.Klassifikator_en === usz) || shareAttracted[0];
      const rOwn = shareOwnFunds.find(r => r.Klassifikator_en === usz) || shareOwnFunds[0];
      const rBank = shareBankLoans.find(r => r.Klassifikator_en === usz) || shareBankLoans[0];
      const rForeign = shareForeign.find(r => r.Klassifikator_en === usz) || shareForeign[0];
      const rState = shareStateBudget.find(r => r.Klassifikator_en === usz) || shareStateBudget[0];
      if (!rAttracted || !rOwn || !rBank || !rForeign || !rState) return { labels: [], data: [] };

      const years = [rAttracted, rOwn, rBank, rForeign, rState]
        .flatMap(rec => Object.keys(rec).filter(k => /^\d{4}$/.test(k)))
        .reduce((set, y) => set.add(y), new Set());
      const latest = Array.from(years).sort(sortYear).pop();
      if (!latest) return { labels: [], data: [] };

      const own = rOwn[latest] ?? 0;
      const bank = rBank[latest] ?? 0;
      const foreign = rForeign[latest] ?? 0;
      const state = rState[latest] ?? 0;
      const attracted = rAttracted[latest] ?? 0;
      const other = Math.max(0, attracted - (bank + foreign + state));

      const labels = ['Own funds', 'Bank loans', 'Foreign investments', 'State budget', 'Other attracted'];
      const data = [own, bank, foreign, state, other];
      return { labels, data };
    } catch (e) {
      return { labels: [], data: [] };
    }
  }, [shareAttracted, shareOwnFunds, shareBankLoans, shareForeign, shareStateBudget]);

  // 资产类型结构：选最新年份，展示分类分布（单位：billion soums）
  const assetTypeLatest = useMemo(() => {
    if (assetTypes.length === 0) return { labels: [], data: [] };
    // assetTypes 是分类数组，每条有多个年份
    const yearsSet = new Set();
    assetTypes.forEach(c => Object.keys(c).forEach(k => { if (/^\d{4}$/.test(k)) yearsSet.add(k); }));
    const years = Array.from(yearsSet).sort(sortYear);
    const latest = years[years.length - 1];
    const labels = assetTypes.map(c => c.Klassifikator_en);
    const data = assetTypes.map(c => (latest ? (c[latest] || 0) : 0));
    return { latestYear: latest, labels, data };
  }, [assetTypes]);

  // 关键指标卡片：人均固定资产投资（全国最新季度）
  const keyMetrics = useMemo(() => {
    try {
      const rec = perCapitaQ.find(r => r.Klassifikator_en === 'Republic of Uzbekistan');
      if (!rec) return null;
      const qs = Object.keys(rec).filter(k => /^\d{4}-Q[1-4]$/.test(k)).sort(sortQuarter);
      const last = qs[qs.length - 1];
      const prev = qs[qs.length - 2];
      const cur = last ? rec[last] : null;
      const pre = prev ? rec[prev] : null;
      const growth = pre ? (((cur - pre) / pre) * 100).toFixed(1) : '0.0';
      return { latestQuarter: last, currentValue: cur, growth, trend: pre && cur >= pre ? 'up' : 'down' };
    } catch {
      return null;
    }
  }, [perCapitaQ]);

  if (loading) {
    return (
      <div className="analytics-container">
        <h2>{language==='en'?'Investment & Fixed Assets Analysis':'🏗️ 投资与固定资产分析'}</h2>
        <div style={{ textAlign: 'center', padding: 40 }}>🔄 {language==='en'?'Loading...':'加载中...'}</div>
      </div>
    );
  }
  if (error) {
    return (
      <div className="analytics-container" style={{ background: '#ffebee', border: '1px solid #f44336' }}>
        <h2 style={{ color: '#d32f2f' }}>{language==='en'?'Investment & Fixed Assets Analysis':'🏗️ 投资与固定资产分析'}</h2>
        <div style={{ textAlign: 'center', padding: 40 }}>❌ {error}</div>
      </div>
    );
  }

  return (
    <section className="analytics-container">
      <div style={{ marginBottom: 12, textAlign: 'center' }}>
        <h2 style={{ margin: 0, color: '#333', fontSize: 20, fontWeight: 700, display: 'flex', gap: 8, justifyContent: 'center' }}>
          {language==='en'?'Investment & Fixed Assets Analysis':'💹 投资与固定资产分析'}
        </h2>
        <p style={{ margin: 0, color: '#666', fontSize: 12 }}>{language==='en'?'Investment structure and trends based on Uzbekistan statistics':'基于乌兹别克斯坦统计数据的投资结构与趋势'}</p>
      </div>

      {/* 关键指标卡片 */}
      {keyMetrics && (
        <div style={{ marginBottom: 12 }}>
          <div style={{ background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)', padding: '12px 16px', borderRadius: 6, color: 'white' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div>
                <div style={{ fontSize: 12, opacity: 0.9 }}>全国人均固定资产投资 ({keyMetrics.latestQuarter})</div>
                <div style={{ fontSize: 20, fontWeight: 'bold' }}>{(keyMetrics.currentValue ?? 0).toLocaleString()} 千苏姆</div>
              </div>
              <div style={{ textAlign: 'right' }}>
                <div style={{ fontSize: 12, opacity: 0.9 }}>环比</div>
                <div style={{ fontSize: 16, fontWeight: 'bold', color: keyMetrics.trend === 'up' ? '#bbf7d0' : '#fecaca' }}>
                  {keyMetrics.trend === 'up' ? '↗' : '↘'} {keyMetrics.growth}%
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 控制面板 */}
      <div className="analytics-controls" style={{ marginBottom: 12 }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <span style={{ fontSize: 11, color: '#666' }}>{language==='en'?'Chart:':'图表:'}</span>
          <div style={{ display: 'inline-flex', gap: 4, background: '#f3f4f6', padding: 2, borderRadius: 6, border: '1px solid #e5e7eb' }}>
            {[
              {id:'line', label: language==='en'?'Line':'折线'},
              {id:'bar', label: language==='en'?'Bar':'柱状'}
            ].map(opt => (
              <button key={opt.id} onClick={()=>setChartType(opt.id)}
                className="analytics-chip"
                style={{
                  padding: '4px 10px',
                  borderRadius: 6,
                  border: 'none',
                  background: chartType===opt.id ? '#10b981' : 'transparent',
                  color: chartType===opt.id ? '#fff' : '#374151',
                  fontSize: 11,
                  fontWeight: 600
                }}
              >{opt.label}</button>
            ))}
          </div>
        </div>
        <div style={{ display: 'flex', gap: 4, flexWrap: 'wrap', alignItems: 'center' }}>
          <input className="analytics-search" placeholder={t.searchRegions || (language==='en'?'Search regions':'搜索地区')} onChange={(e)=>setSearchQuery(e.target.value)} />
          <button className="analytics-chip" onClick={()=>setSelectedRegions(['Republic of Uzbekistan'])}>{t.onlyNational || (language==='en'?'Only national':'仅全国')}</button>
          <button className="analytics-chip" onClick={()=>setSelectedRegions([])}>{language==='en'?'Clear':'清空'}</button>
          <button className="analytics-chip" onClick={()=>setSelectedRegions(availableRegions)}>{language==='en'?'Select all':'全选'}</button>
          {availableRegions.map(region => (
            <button key={region} onClick={() => toggleRegion(region)}
              style={{ padding: '4px 8px', border: `1px solid ${regionColor(region)}`, background: selectedRegions.includes(region) ? regionColor(region) : 'white', color: selectedRegions.includes(region) ? 'white' : regionColor(region), borderRadius: 12, fontSize: 10, cursor: 'pointer' }}>
              {region}
            </button>
          ))}
        </div>
      </div>

      {/* 标签页 */}
      <div style={{ marginBottom: 12, display: 'flex', gap: 2, borderBottom: '1px solid #e0e0e0' }}>
        {[
          { id: 'overview', label: language==='en'?'Overview':'总览' },
          { id: 'perCapita', label: language==='en'?'Per capita (quarterly)':'人均(季度)' },
          { id: 'funding', label: language==='en'?'Funding structure':'资金来源结构' },
          { id: 'gdpShare', label: language==='en'?'Investment/GDP (annual)':'投资/GDP(年度)' },
          { id: 'assetTypes', label: language==='en'?'Fixed asset types':'固定资产类型' },
        ].map(tab => (
          <button key={tab.id} onClick={() => setActiveTab(tab.id)}
            style={{ padding: '8px 12px', border: 'none', background: activeTab === tab.id ? '#10b981' : 'transparent', color: activeTab === tab.id ? 'white' : '#666', borderRadius: '4px 4px 0 0', fontSize: 13, fontWeight: 500, cursor: 'pointer' }}>
            {tab.label}
          </button>
        ))}
      </div>

      {/* 内容 */}
      <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
        {activeTab === 'overview' && (
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 12 }}>
            {/* 人均季度趋势（全国+默认地区） */}
            <div style={{ background: 'white', padding: 12, borderRadius: 6, border: '1px solid #e0e0e0' }}>
              <h4 style={{ margin: '0 0 8px 0', fontSize: 14, color: '#333' }}>人均固定资产投资（季度）</h4>
              <div style={{ minHeight: 280 }}>
                {perCapitaQuarterly.labels.length > 0 ? (
                  chartType === 'line' ? (
                    <Line data={perCapitaQuarterly} options={{...lineOptions('', 'thousand soums'), interaction: { mode: 'index', intersect: false }}} />
                  ) : (
                    <Bar data={perCapitaQuarterly} options={{...barOptions('', 'thousand soums'), interaction: { mode: 'index', intersect: false }}} />
                  )
                ) : (
                  <div style={{ textAlign: 'center', padding: 60, color: '#999' }}>{language==='en'?'No data':'暂无数据'}</div>
                )}
              </div>
            </div>

            {/* 资金来源结构（全国最新年份） */}
            <div style={{ background: 'white', padding: 12, borderRadius: 6, border: '1px solid #e0e0e0' }}>
              <h4 style={{ margin: '0 0 8px 0', fontSize: 14, color: '#333' }}>资金来源结构（全国，最新年度）</h4>
              <div style={{ height: 280, position: 'relative' }}>
                {fundingShareLatest.labels.length > 0 ? (
                  <Doughnut data={{ labels: fundingShareLatest.labels, datasets: [{ label: 'Share (%)', data: fundingShareLatest.data, backgroundColor: ['#3b82f6','#10b981','#f59e0b','#8b5cf6','#ef4444'] }] }} options={{ responsive: true, maintainAspectRatio: false, plugins: { legend: { position: 'bottom' } } }} />
                ) : (
                  <div style={{ textAlign: 'center', padding: 60, color: '#999' }}>{language==='en'?'No data':'暂无数据'}</div>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'perCapita' && (
          <div style={{ background: 'white', padding: 12, borderRadius: 6, border: '1px solid #e0e0e0' }}>
            <h4 style={{ margin: '0 0 8px 0', fontSize: 14, color: '#333' }}>人均固定资产投资（季度）</h4>
            <div style={{ minHeight: 380 }}>
              {perCapitaQuarterly.labels.length > 0 ? (
                chartType === 'line' ? (
                  <Line data={perCapitaQuarterly} options={lineOptions('', 'thousand soums')} />
                ) : (
                  <Bar data={perCapitaQuarterly} options={barOptions('', 'thousand soums')} />
                )
              ) : (
                <div style={{ textAlign: 'center', padding: 80, color: '#666' }}>{language==='en'?'No quarterly data':'暂无季度数据'}</div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'gdpShare' && (
          <div style={{ background: 'white', padding: 12, borderRadius: 6, border: '1px solid #e0e0e0' }}>
            <h4 style={{ margin: '0 0 8px 0', fontSize: 14, color: '#333' }}>固定资产投资/GDP 占比（年度）</h4>
            <div style={{ minHeight: 380 }}>
              {gdpShareSeries.labels.length > 0 ? (
                chartType === 'line' ? (
                  <Line data={gdpShareSeries} options={{...lineOptions('', 'percent'), interaction: { mode: 'index', intersect: false }}} />
                ) : (
                  <Bar data={gdpShareSeries} options={{...barOptions('', 'percent'), interaction: { mode: 'index', intersect: false }}} />
                )
              ) : (
                <div style={{ textAlign: 'center', padding: 80, color: '#666' }}>{language==='en'?'No annual data':'暂无年度数据'}</div>
              )}
            </div>
          </div>
        )}


        {activeTab === 'funding' && (
          <div style={{ background: 'white', padding: 12, borderRadius: 6, border: '1px solid #e0e0e0' }}>
            <h4 style={{ margin: '0 0 8px 0', fontSize: 14, color: '#333' }}>资金来源结构（全国，最新年度）</h4>
            <div style={{ height: 360, position: 'relative' }}>
              {fundingShareLatest.labels.length > 0 ? (
                <Doughnut data={{ labels: fundingShareLatest.labels, datasets: [{ label: 'Share (%)', data: fundingShareLatest.data, backgroundColor: ['#3b82f6','#10b981','#f59e0b','#8b5cf6','#ef4444'] }] }} options={{ responsive: true, maintainAspectRatio: false, plugins: { legend: { position: 'bottom' }, title: { display: false } }} } />
              ) : (
                <div style={{ textAlign: 'center', padding: 80, color: '#666' }}>{language==='en'?'No data':'暂无数据'}</div>
              )}
      {/* 数据来源与单位（若可用） */}
      {meta?.gdp?.length > 0 && (
        <div className="analytics-footer">
          <span>{language==='en'?'Source: Statistics Agency of Uzbekistan':'数据来源：乌兹别克斯坦统计局'}</span>
          {(() => {
            const unit = meta.gdp.find(m=>m.name_en==='Unit of measurement')?.value_en;
            const last = meta.gdp.find(m=>m.name_en==='Last modified date')?.value_en;
            return (
              <span style={{ marginLeft: 8 }}>
                {unit ? (language==='en'?`Unit: ${unit}`:`单位：${unit}`) : ''}
                {last ? (language==='en'?` · Updated: ${last}`:` · 更新：${last}`) : ''}
              </span>
            );
          })()}
        </div>
      )}

            </div>
          </div>
        )}

        {activeTab === 'assetTypes' && (
          <div style={{ background: 'white', padding: 12, borderRadius: 6, border: '1px solid #e0e0e0' }}>
            <h4 style={{ margin: '0 0 8px 0', fontSize: 14, color: '#333' }}>固定资产类型结构（全国，{assetTypeLatest.latestYear || '-'}）</h4>
            <div style={{ minHeight: 380 }}>
              {assetTypeLatest.labels.length > 0 ? (
                <Bar data={{ labels: assetTypeLatest.labels, datasets: [{ label: 'billion soums', data: assetTypeLatest.data, backgroundColor: '#3b82f6' }] }} options={barOptions('', 'billion soums')} />
              ) : (
                <div style={{ textAlign: 'center', padding: 80, color: '#666' }}>{language==='en'?'No data':'暂无数据'}</div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* 数据来源 */}
      <div style={{ marginTop: 8, padding: '8px 12px', background: 'linear-gradient(135deg, #f5f5f5 0%, #eeeeee 100%)', borderRadius: 4, fontSize: 11, color: '#666', border: '1px solid #e0e0e0' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>📄 数据来源: 乌兹别克斯坦统计局</span>
          <span style={{ background: '#10b981', color: 'white', padding: '2px 6px', borderRadius: 8, fontSize: 9 }}>实时更新</span>
        </div>
      </div>
    </section>
  );
};

export default UzPropertyInvestmentAnalysis;

