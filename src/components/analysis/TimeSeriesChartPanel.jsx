import React, { useState, useEffect, useRef } from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend } from 'chart.js';
import { Line, Bar } from 'react-chartjs-2';
import '../../styles/TimeSeriesChartPanel.css';

// 注册 Chart.js 组件
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend);

const TimeSeriesChartPanel = ({ show, onClose, data, t = {} }) => {
  // 图表类型（折线图/柱状图）
  const [chartType, setChartType] = useState('line');
  // 当前时间点
  const [currentTime, setCurrentTime] = useState(0);
  // 选中的数据类别
  const [selectedCategories, setSelectedCategories] = useState([]);
  // 滑块拖动状态
  const [isDragging, setIsDragging] = useState(false);
  // 滑块引用
  const sliderRef = useRef(null);

  // 预定义的数据类别
  const categories = [
    { id: 'education', label: t.education || '教育水平', icon: '🎓' },
    { id: 'employment', label: t.employment || '就业状态', icon: '👥' },
    { id: 'occupation', label: t.occupation || '职业分布', icon: '👷' },
    { id: 'industry', label: t.industry || '行业分布', icon: '🏭' },
    { id: 'workingHours', label: t.workingHours || '工作时间', icon: '⏰' }
  ];

  // 处理滑块拖动开始
  const handleDragStart = (e) => {
    setIsDragging(true);
    document.addEventListener('mousemove', handleDragMove);
    document.addEventListener('mouseup', handleDragEnd);
    document.addEventListener('touchmove', handleDragMove);
    document.addEventListener('touchend', handleDragEnd);
  };

  // 处理滑块拖动
  const handleDragMove = (e) => {
    if (!isDragging || !sliderRef.current) return;

    const rect = sliderRef.current.getBoundingClientRect();
    const x = (e.touches ? e.touches[0].clientX : e.clientX) - rect.left;
    const width = rect.width;
    
    // 计算百分比位置（0-1）
    let percent = Math.max(0, Math.min(1, x / width));
    
    // 假设数据有 5 个时间点（0-4）
    const timePoint = Math.round(percent * 4);
    setCurrentTime(timePoint);
  };

  // 处理滑块拖动结束
  const handleDragEnd = () => {
    setIsDragging(false);
    document.removeEventListener('mousemove', handleDragMove);
    document.removeEventListener('mouseup', handleDragEnd);
    document.removeEventListener('touchmove', handleDragMove);
    document.removeEventListener('touchend', handleDragEnd);
  };

  // 切换数据类别
  const toggleCategory = (categoryId) => {
    setSelectedCategories(prev => {
      if (prev.includes(categoryId)) {
        return prev.filter(id => id !== categoryId);
      } else {
        return [...prev, categoryId];
      }
    });
  };

  // 生成图表数据
  const chartData = {
    labels: ['2020', '2021', '2022', '2023', '2024'],
    datasets: selectedCategories.map((categoryId, index) => ({
      label: categories.find(c => c.id === categoryId)?.label || categoryId,
      data: Array(5).fill(0).map(() => Math.floor(Math.random() * 1000)),
      backgroundColor: [
        'rgba(75, 192, 192, 0.6)',
        'rgba(255, 99, 132, 0.6)',
        'rgba(54, 162, 235, 0.6)',
        'rgba(153, 102, 255, 0.6)',
        'rgba(255, 159, 64, 0.6)',
      ][index % 5],
      borderColor: [
        'rgba(75, 192, 192, 1)',
        'rgba(255, 99, 132, 1)',
        'rgba(54, 162, 235, 1)',
        'rgba(153, 102, 255, 1)',
        'rgba(255, 159, 64, 1)',
      ][index % 5],
      borderWidth: 1,
    }))
  };

  // 图表配置
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { position: 'top' },
      title: {
        display: true,
        text: t.laborTrendAnalysis || '劳动力趋势分析',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              label += new Intl.NumberFormat('zh-CN').format(context.parsed.y);
            }
            return label;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return new Intl.NumberFormat('zh-CN').format(value);
          }
        }
      }
    }
  };

  return (
    <div className={`time-series-chart-panel ${show ? 'show' : ''}`}>
      <div className="panel-header">
        <h2>{t.laborTrendAnalysis || '劳动力趋势分析'}</h2>
        <button className="close-button" onClick={onClose}>×</button>
      </div>

      <div className="panel-content">
        {/* 时间轴滑块 */}
        <div className="time-slider">
          <div className="slider-container">
            <div className="slider-track" ref={sliderRef}>
              <div
                className="slider-handle"
                style={{ left: `${(currentTime / 4) * 100}%` }}
                onMouseDown={handleDragStart}
                onTouchStart={handleDragStart}
              >
                <div className="slider-tooltip">
                  {2020 + currentTime}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 图表类型选择 */}
        <div className="chart-type-selector">
          <button
            className={`type-button ${chartType === 'line' ? 'active' : ''}`}
            onClick={() => setChartType('line')}
          >
            <span>📈</span>
            {t.lineChart || '折线图'}
          </button>
          <button
            className={`type-button ${chartType === 'bar' ? 'active' : ''}`}
            onClick={() => setChartType('bar')}
          >
            <span>📊</span>
            {t.barChart || '柱状图'}
          </button>
        </div>

        {/* 数据类别选择 */}
        <div className="category-selector">
          {categories.map(category => (
            <button
              key={category.id}
              className={`category-button ${selectedCategories.includes(category.id) ? 'active' : ''}`}
              onClick={() => toggleCategory(category.id)}
            >
              <span>{category.icon}</span> {category.label}
            </button>
          ))}
        </div>

        {/* 图表展示区 */}
        <div className="chart-container">
          {selectedCategories.length > 0 ? (
            chartType === 'line' ? (
              <Line data={chartData} options={chartOptions} />
            ) : (
              <Bar data={chartData} options={chartOptions} />
            )
          ) : (
            <div style={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#666',
              gap: '12px'
            }}>
              <div style={{ fontSize: '48px', opacity: 0.2 }}>
                {chartType === 'line' ? '📈' : '📊'}
              </div>
              <div>{t.pleaseSelectMetrics || '请选择至少一个指标以显示图表'}</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TimeSeriesChartPanel; 