# Parcel Prospector Module Specification

## Overview
The Parcel Prospector module analyzes individual parcels within identified clusters or user-defined areas, computing detailed dollar-based feasibility metrics. It enriches Reonomy-style parcel data with archetype-driven cost modeling and regional rate analysis.

## Dependencies

### Required Packages
- `pandas`, `numpy`: Core calculations
- `requests`: External API calls (EIA, BLS, Census)
- `geopandas`: Spatial filtering (optional)
- `pydantic`: Override validation
- `cachetools`: Rate lookup memoization

## Configuration
```python
parcel_prospector_cfg = {
    "archetype": {
        "default": "General Manufacturing",
        "mapping_rules": {
            "warehouse": {
                "min_sqft": 50000,
                "max_sqft": 1000000,
                "preferred_zones": ["M1", "M2", "IL"]
            },
            "manufacturing": {
                "min_sqft": 20000,
                "max_sqft": 500000,
                "preferred_zones": ["M2", "M3", "IG"]
            }
        }
    },
    
    "multipliers": {
        "age_bands": {
            "pre_1980": 1.20,
            "1980_2000": 1.10,
            "post_2000": 1.00
        },
        "building_class": {
            "A": 1.15,
            "B": 1.00,
            "C": 0.90
        }
    },
    
    "cost_factors": {
        "maintenance_pct": 0.02,        # of replacement cost per year
        "replacement_cost_psf": 120.0,  # baseline hard cost
        "labor_burden_pct": 0.25,       # benefits + payroll taxes
        "scenario_max": 5               # max concurrent what-if scenarios
    }
}
```

## Input Schema
```python
ParcelInput = {
    # Required Fields
    "parcel_id": str,                    # Unique identifier
    "latitude": float,                   # Location for rate lookups
    "longitude": float,
    "building_area": float,              # Main scale driver
    "lot_size_acres": float,             # Site OpEx calculations
    "year_built": int,                   # Age factor
    "year_renovated": Optional[int],     # Renovation adjustments
    "building_class": str,               # Quality multiplier
    "std_land_use_code_description": str,# Archetype selection
    
    # Tax Information
    "taxes": {
        "annual_tax_amount": float
    },
    "assessment": {
        "total_value": float            # Fallback for tax calculation
    },
    
    # Optional Overrides
    "overrides": {
        "labor_rate": Optional[float],
        "energy_rate": Optional[float],
        "maintenance_pct": Optional[float],
        "capex_per_sf": Optional[float]
    }
}
```

## Output Schema
```python
ParcelAnalysis = {
    "parcel_id": str,                   # Echo input
    "archetype": str,                   # Assigned industry template
    
    # Capital Expenditure
    "capex_estimate": float,            # Total projected upfront capital
    
    # Operating Expenses
    "opex": {
        "energy": float,                # Annual energy costs
        "labor": float,                 # Annual labor costs
        "utilities_other": float,       # Water, gas, waste
        "maintenance": float,           # R&M / preventive maintenance
        "property_tax": float,          # Annual property tax
        "total": float                  # Sum of all OpEx
    },
    
    "year1_total_cost": float,          # capex_estimate + opex.total
    
    # Analysis Metadata
    "assumptions": {
        "rates": {
            "electricity": float,        # $/kWh
            "labor": float,             # $/hour
            "water": float              # $/gallon
        },
        "multipliers": {
            "age": float,
            "class": float,
            "location": float
        },
        "archetype_metrics": {
            "kwh_per_sf": float,
            "headcount_per_sf": float,
            "maintenance_factor": float
        }
    },
    
    "overrides_applied": List[str]      # Active user overrides
}
```

## Processing Steps

1. **Parcel Selection**
   - Filter by cluster IDs or map extent
   - Apply user constraints (zoning, size, use type)

2. **Archetype Assignment**
   - Map parcels to industry archetypes based on:
     - `std_land_use_code_description`
     - Zoning
     - Size thresholds
   - Apply archetype-specific baseline metrics:
     - kWh per square foot
     - Water consumption
     - Labor headcount density
     - Maintenance percentages
     - TI (tenant improvement) multipliers

3. **Regional Rate Analysis**
   - EIA utility zone → electricity & gas rates
   - BLS metro/county → wage rates
   - Census county → economic indicators
   - Cache results by geography

4. **Cost Calculations**
   ```python
   # Example calculations
   CapEx = base_ti_cost * building_area * (age_multiplier * class_multiplier)
   OpEx_energy = kwh_per_sf * building_area * local_rate
   OpEx_labor = headcount_per_sf * building_area * avg_wage * (1 + burden_pct)
   OpEx_maintenance = maintenance_pct * replacement_cost_psf * building_area
   ```

## Performance Optimization

### Caching Strategy
- Cache key format: `{eia_zone}:{bls_area_code}`
- Cache duration: 24 hours for rate data
- Batch API requests where possible
- Stream processing for large datasets

### Error Handling
- Retry logic for API failures
- Fallback values for missing data
- Validation for user overrides

## Testing Requirements
- Unit tests for cost calculations
- Integration tests for API interactions
- Validation tests for archetype mapping
- Performance tests for large datasets


