import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import requests
from cachetools import TTLCache
from pydantic import BaseModel, Field
import json
import sys
import os

# Add the API Manager directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'API Manager'))

from eia_client import EIAClient
from bls_client import BLSClient, fetch_fully_loaded_hourly
import config  # Import the config module directly

# Pydantic models for validation
# Remove old models that we don't need anymore
class ParcelOverrides(BaseModel):
    labor_rate: Optional[float] = None
    energy_rate: Optional[float] = None
    maintenance_pct: Optional[float] = None
    capex_per_sf: Optional[float] = None

class ParcelInput(BaseModel):
    """Input data model matching CSV structure"""
    apn: str
    latitude: float
    longitude: float
    fips: str
    asset_category: str
    building_area: float
    lot_area_ft: float
    building_class: str
    noi: float
    annual_taxes: float
    latest_sale_date: Optional[str]
    latest_sale_price: Optional[float]
    loan_balance: Optional[float]
    loan_due_date: Optional[str]
    loan_rate: Optional[float]
    owner_name: Optional[str]
    owner_phone: Optional[str]
    owner_email: Optional[str]
    overrides: Optional[ParcelOverrides] = None  # Keep overrides for flexibility

class ParcelDataLoader:
    """Loads and manages parcel data from CSV file."""
    
    def __init__(self, csv_path: str = "C:/Users/<USER>/OneDrive/桌面/雾谷科技/industrial location Algorithms/Modules/1 Mock Data/generated_data/california_parcels.csv"):
        """Initialize with path to CSV file."""
        self.csv_path = csv_path
        print(f"Loading data from: {csv_path}")
        self.df = pd.read_csv(csv_path, dtype={'apn': str})
        print(f"Loaded {len(self.df)} parcels from database")
    
    def get_parcel_by_apn(self, apn: str) -> Optional[Dict[str, Any]]:
        """Fetch parcel data by APN."""
        try:
            # Find the parcel row
            parcel = self.df[self.df['apn'] == apn]
            
            if len(parcel) == 0:
                print(f"Debug: No match found for APN '{apn}'")
                print(f"Debug: Available APNs (first 5): {', '.join(self.df['apn'].head().tolist())}")
                return None
                
            # Convert row to dictionary
            return parcel.iloc[0].to_dict()
        except Exception as e:
            print(f"Error fetching parcel data: {str(e)}")
            return None

class Rates(BaseModel):
    electricity: float
    labor: float

class Multipliers(BaseModel):
    age: float
    building_class: float
    location: float

class ArchetypeMetrics(BaseModel):
    kwh_per_sf: float
    headcount_per_sf: float
    maintenance_factor: float

class OpExBreakdown(BaseModel):
    energy: float
    labor: float
    utilities_other: float
    maintenance: float
    property_tax: float
    total: float

class Assumptions(BaseModel):
    rates: Rates
    multipliers: Multipliers
    archetype_metrics: ArchetypeMetrics

class ParcelAnalysis(BaseModel):
    parcel_id: str
    archetype: str
    capex_estimate: float
    opex: OpExBreakdown
    year1_total_cost: float
    assumptions: Assumptions
    overrides_applied: List[str]

class APIManager:
    """Manages API connections and data retrieval for the Parcel Prospector."""
    
    def __init__(self):
        """Initialize API clients and load configuration."""
        # Use config values directly
        self.eia_client = EIAClient()
        self.bls_client = BLSClient()
        self.rate_cache = {}
        
        # Load API configuration
        self.cache_expiry = config.CACHE_EXPIRY_HOURS
        self.max_requests = config.MAX_REQUESTS_PER_MINUTE
        self.timeout = config.REQUEST_TIMEOUT_SECONDS
        self.labor_multiplier = config.FULLY_LOADED_LABOR_MULTIPLIER
        self.work_hours = config.DEFAULT_WORK_HOURS_PER_YEAR
    
    def get_utility_rates(self, latitude: float, longitude: float) -> Dict[str, float]:
        """Get utility rates from EIA based on location."""
        cache_key = f"{round(latitude, 2)}:{round(longitude, 2)}"
        
        if cache_key in self.rate_cache:
            return self.rate_cache[cache_key]
        
        try:
            # Get electricity rates from EIA
            # Convert to $/kWh from cents/kWh
            electricity_rate = self.eia_client.get_latest_price_cents_per_kwh(
                state_id="CA",  # TODO: Convert lat/lon to state
                sector_id="IND"  # Industrial sector
            ) / 100.0  # Convert cents to dollars
            
            # For now, use default gas rate since it's not in the current API
            gas_rate = self.eia_client.get_default_rate("natural_gas", 8.50)  # $/MCF
            
            rates = {
                "electricity": electricity_rate if electricity_rate else self.eia_client.get_default_rate("electricity", 0.12),
                "natural_gas": gas_rate
            }
            
            self.rate_cache[cache_key] = rates
            return rates
            
        except Exception as e:
            print(f"Error fetching utility rates: {str(e)}")
            # Return default rates if API fails
            return {
                "electricity": self.eia_client.get_default_rate("electricity", 0.12),  # $/kWh
                "natural_gas": self.eia_client.get_default_rate("natural_gas", 8.50)  # $/MCF
            }
    
    def get_labor_rates(self, county_fips: str) -> Dict[str, float]:
        """Get labor rates from BLS based on county FIPS code."""
        try:
            # Use the fetch_fully_loaded_hourly function directly
            total_comp_series = "CMU2010000000000D"  # National private industry total comp
            wages_series = "CMU2020000000000D"      # National private industry wages
            
            hourly_rate = fetch_fully_loaded_hourly(
                total_comp_series=total_comp_series,
                wages_only_series=wages_series
            )
            
            if hourly_rate is None:
                hourly_rate = 25.00  # Default rate
                
            # Use the labor multiplier from config for benefits
            benefits_pct = self.labor_multiplier - 1.0
            
            return {
                "hourly_wage": hourly_rate,
                "benefits_pct": benefits_pct,
                "labor": hourly_rate  # Add this for compatibility
            }
            
        except Exception as e:
            print(f"Error fetching labor rates: {str(e)}")
            # Return default rates if API fails
            return {
                "hourly_wage": 25.00,
                "benefits_pct": 0.30,
                "labor": 25.00
            }

class ParcelProspector:
    """Analyzes individual parcels with detailed cost modeling."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize with optional custom configuration."""
        # Load default configuration
        self.config = {
            "archetype": {
                "default": "General Manufacturing",
                "mapping_rules": {
                    "warehouse": {
                        "min_sqft": 50000,
                        "max_sqft": 1000000,
                        "preferred_zones": ["M1", "M2", "IL"]
                    },
                    "manufacturing": {
                        "min_sqft": 20000,
                        "max_sqft": 500000,
                        "preferred_zones": ["M2", "M3", "IG"]
                    }
                }
            },
            "multipliers": {
                "building_class": {
                    "A": 1.15,
                    "B": 1.00,
                    "C": 0.90
                }
            },
            "cost_factors": {
                "maintenance_pct": 0.02,
                "replacement_cost_psf": 120.0,
                "labor_burden_pct": 0.25,
                "scenario_max": 5
            }
        }
        
        # Override with custom config if provided
        if config_path:
            with open(config_path, 'r') as f:
                custom_config = json.load(f)
                self.config.update(custom_config)
        
        # Initialize API manager
        self.api_manager = APIManager()
        
        # Define archetype baseline metrics
        self.archetype_metrics = {
            "General Manufacturing": {
                "kwh_per_sf": 15.0,
                "headcount_per_sf": 0.001,  # 1 person per 1000 sf
                "maintenance_factor": 1.0
            },
            "Warehouse/Distribution": {
                "kwh_per_sf": 8.0,
                "headcount_per_sf": 0.0005,  # 1 person per 2000 sf
                "maintenance_factor": 0.8
            },
            "R&D/Flex": {
                "kwh_per_sf": 20.0,
                "headcount_per_sf": 0.002,  # 1 person per 500 sf
                "maintenance_factor": 1.2
            }
        }
    
    def _determine_archetype(self, parcel: ParcelInput) -> str:
        """Map parcel to appropriate industry archetype."""
        # Start with default
        archetype = self.config["archetype"]["default"]
        
        # Check size-based rules
        area = parcel.building_area
        
        if (area >= self.config["archetype"]["mapping_rules"]["warehouse"]["min_sqft"] and 
            area <= self.config["archetype"]["mapping_rules"]["warehouse"]["max_sqft"]):
            if "warehouse" in parcel.asset_category.lower():
                archetype = "Warehouse/Distribution"
                
        elif (area >= self.config["archetype"]["mapping_rules"]["manufacturing"]["min_sqft"] and 
              area <= self.config["archetype"]["mapping_rules"]["manufacturing"]["max_sqft"]):
            if "manufacturing" in parcel.asset_category.lower():
                archetype = "General Manufacturing"
                
        # R&D/Flex is typically determined more by use type than size
        if any(term in parcel.asset_category.lower() 
               for term in ["r&d", "flex", "research", "office"]):
            archetype = "R&D/Flex"
            
        return archetype
    
    def _get_class_multiplier(self, building_class: str) -> float:
        """Get building class multiplier."""
        return self.config["multipliers"]["building_class"].get(
            building_class, 
            self.config["multipliers"]["building_class"]["B"]  # Default to Class B if unknown
        )
    
    def analyze_parcel(self, parcel: ParcelInput) -> ParcelAnalysis:
        """Perform comprehensive parcel analysis."""
        # Track any overrides used
        overrides_applied = []
        
        # 1. Determine archetype
        archetype = self._determine_archetype(parcel)
        archetype_data = self.archetype_metrics[archetype]
        
        # 2. Get multipliers
        class_multiplier = self._get_class_multiplier(parcel.building_class)
        
        # 3. Calculate CapEx first
        base_ti_cost = self.config["cost_factors"]["replacement_cost_psf"]
        capex_estimate = base_ti_cost * parcel.building_area * class_multiplier
        
        if parcel.overrides and parcel.overrides.capex_per_sf:
            capex_estimate = parcel.building_area * parcel.overrides.capex_per_sf
            overrides_applied.append("capex_per_sf")
        
        # 4. Get local rates using APIs
        rates = self.api_manager._get_rates(
            latitude=parcel.latitude,
            longitude=parcel.longitude,
            county_fips=parcel.fips
        )
        
        # Update energy calculations to include natural gas
        energy_rate = rates["electricity"]
        gas_rate = rates["natural_gas"]
        if parcel.overrides and parcel.overrides.energy_rate:
            energy_rate = parcel.overrides.energy_rate
            overrides_applied.append("energy_rate")
        
        # Calculate energy costs including both electricity and natural gas
        electricity_cost = (archetype_data["kwh_per_sf"] * 
                          parcel.building_area * 
                          energy_rate)
        
        gas_cost = (archetype_data.get("gas_usage_factor", 0.5) * 
                   parcel.building_area * 
                   gas_rate)
        
        total_energy_cost = electricity_cost + gas_cost
        
        # Update labor calculations with benefits
        labor_rate = rates["labor"]
        benefits_pct = rates["benefits_pct"]
        if parcel.overrides and parcel.overrides.labor_rate:
            labor_rate = parcel.overrides.labor_rate
            overrides_applied.append("labor_rate")
        
        headcount = archetype_data["headcount_per_sf"] * parcel.building_area
        labor_cost = (headcount * 
                     labor_rate * 
                     2080 * 
                     (1 + benefits_pct))
        
        # Maintenance
        maint_pct = self.config["cost_factors"]["maintenance_pct"]
        if parcel.overrides and parcel.overrides.maintenance_pct:
            maint_pct = parcel.overrides.maintenance_pct
            overrides_applied.append("maintenance_pct")
        maintenance_cost = maint_pct * self.config["cost_factors"]["replacement_cost_psf"] * parcel.building_area
        
        # Property tax (use actual if available, otherwise estimate)
        property_tax = (parcel.annual_taxes if parcel.annual_taxes > 0 
                       else parcel.latest_sale_price * 0.01 if pd.notnull(parcel.latest_sale_price)
                       else capex_estimate * 0.01)  # Fallback to 1% of CapEx
        
        # Other utilities (simplified estimate)
        utilities_other = parcel.building_area * 0.3  # $0.30/sf for gas and waste
        
        # Total OpEx
        total_opex = sum([
            total_energy_cost,
            labor_cost,
            maintenance_cost,
            property_tax,
            utilities_other
        ])
        
        # Create analysis output
        return ParcelAnalysis(
            parcel_id=parcel.apn,
            archetype=archetype,
            capex_estimate=capex_estimate,
            opex=OpExBreakdown(
                energy=total_energy_cost,
                labor=labor_cost,
                utilities_other=utilities_other,
                maintenance=maintenance_cost,
                property_tax=property_tax,
                total=total_opex
            ),
            year1_total_cost=capex_estimate + total_opex,
            assumptions=Assumptions(
                rates=Rates(
                    electricity=energy_rate,
                    labor=labor_rate
                ),
                multipliers=Multipliers(
                    age=1.0,  # No age multiplier since we don't have year_built
                    building_class=class_multiplier,
                    location=1.0
                ),
                archetype_metrics=ArchetypeMetrics(
                    kwh_per_sf=archetype_data["kwh_per_sf"],
                    headcount_per_sf=archetype_data["headcount_per_sf"],
                    maintenance_factor=archetype_data["maintenance_factor"]
                )
            ),
            overrides_applied=overrides_applied
        )

def main():
    """Interactive command-line interface for Parcel Prospector."""
    print("\n🏢 Welcome to Parcel Prospector 🏢")
    print("=" * 50)
    
    # Initialize prospector and data loader
    prospector = ParcelProspector()
    data_loader = ParcelDataLoader()
    
    while True:
        print("\nPlease enter the APN number of the parcel you want to analyze")
        print("(or type 'exit' to quit)")
        apn = input("APN > ").strip()
        
        if apn.lower() == 'exit':
            print("\nThank you for using Parcel Prospector!")
            break
            
        try:
            # Fetch parcel data from CSV
            parcel_data = data_loader.get_parcel_by_apn(apn)
            
            if not parcel_data:
                print(f"\n❌ Error: Could not find parcel with APN: {apn}")
                continue
            
            # Create ParcelInput instance with proper validation
            sample_parcel = ParcelInput(**parcel_data)
            
            print(f"\nFound parcel: {apn}")
            print(f"Building Type: {sample_parcel.asset_category}")
            print(f"Building Area: {sample_parcel.building_area:,.0f} sq ft")
            if pd.notnull(sample_parcel.building_class):
                print(f"Building Class: {sample_parcel.building_class}")
            print(f"Lot Area: {sample_parcel.lot_area_ft:,.0f} sq ft")
            print(f"County FIPS: {sample_parcel.fips}")
            
            while True:
                calculate = input("\nWould you like to calculate CapEx and OpEx? (yes/no) > ").strip().lower()
                if calculate in ['yes', 'no']:
                    break
                print("Please enter 'yes' or 'no'")
            
            if calculate == 'yes':
                print("\nCalculating costs...")
                print("(This may take a moment as we fetch current market rates)")
                
                # Perform analysis
                analysis = prospector.analyze_parcel(sample_parcel)
                
                # Display results
                print("\n📊 Analysis Results 📊")
                print("=" * 50)
                print(f"\n🏗️ Capital Expenditure (CapEx)")
                print(f"Total CapEx: ${analysis.capex_estimate:,.2f}")
                print(f"CapEx per SF: ${analysis.capex_estimate/sample_parcel.building_area:.2f}")
                
                print(f"\n💰 Operating Expenses (Annual)")
                print(f"Energy: ${analysis.opex.energy:,.2f}")
                print(f"Labor: ${analysis.opex.labor:,.2f}")
                print(f"Maintenance: ${analysis.opex.maintenance:,.2f}")
                print(f"Property Tax: ${analysis.opex.property_tax:,.2f}")
                print(f"Other Utilities: ${analysis.opex.utilities_other:,.2f}")
                print(f"Total OpEx: ${analysis.opex.total:,.2f}")
                print(f"OpEx per SF: ${analysis.opex.total/sample_parcel.building_area:.2f}")
                
                print(f"\n💵 First Year Total Cost")
                print(f"Total: ${analysis.year1_total_cost:,.2f}")
                print(f"Per SF: ${analysis.year1_total_cost/sample_parcel.building_area:.2f}")
                
                # Display assumptions used
                print("\n📋 Key Assumptions Used")
                print(f"Electricity Rate: ${analysis.assumptions.rates.electricity:.4f}/kWh")
                print(f"Labor Rate: ${analysis.assumptions.rates.labor:.2f}/hour")
                print(f"Building Class Multiplier: {analysis.assumptions.multipliers.building_class:.2f}")
                
                # Ask if user wants to save results
                save = input("\nWould you like to save these results? (yes/no) > ").strip().lower()
                if save == 'yes':
                    filename = f"parcel_{apn}_analysis.txt"
                    with open(filename, 'w') as f:
                        f.write(f"Analysis Results for Parcel {apn}\n")
                        f.write("=" * 50 + "\n")
                        f.write(f"\nCapEx: ${analysis.capex_estimate:,.2f}")
                        f.write(f"\nAnnual OpEx: ${analysis.opex.total:,.2f}")
                        f.write(f"\nFirst Year Total: ${analysis.year1_total_cost:,.2f}")
                    print(f"\nResults saved to {filename}")
            
            # Ask if user wants to analyze another parcel
            another = input("\nWould you like to analyze another parcel? (yes/no) > ").strip().lower()
            if another != 'yes':
                print("\nThank you for using Parcel Prospector!")
                break
                
        except Exception as e:
            print(f"\n❌ Error: Could not analyze parcel {apn}")
            print(f"Details: {str(e)}")
            print("Please try again with a different Parcel ID")

if __name__ == "__main__":
    main() 