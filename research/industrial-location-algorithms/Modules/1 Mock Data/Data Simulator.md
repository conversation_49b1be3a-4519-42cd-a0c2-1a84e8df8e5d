# Data Simulator Module Specification

## Overview
The Data Simulator module generates synthetic parcel-level real estate data for testing and local development, avoiding Reonomy API quota usage. It creates realistic mock data with regional variations focused on California properties.

## Module Information
- **Filename**: `data_simulator.py`
- **Purpose**: Generate synthetic real estate data that mimics Re<PERSON>'s data structure
- **Scope**: California-based property data simulation

## Dependencies

### Required Packages
- `pandas`
- `numpy`
- `faker`
- `random`
- `uuid`

### System Requirements
- Python 3.8+
- Minimum 1GB RAM (for 10,000+ records)
- No external services required (fully offline simulation)

## Configuration Schema
# High‑level knobs for the parcel generator.
# Developers are free to interpret these values—
# e.g., choose any sampling distribution, any chunk size,
# or swap Faker for another synthetic‑data lib.

Schema should solve:
    # How many synthetic parcels to create.  Use a big number (≈1 M)
    # so the dataset covers all California counties, but let the
    # code decide whether to stream‑write in chunks or buffer in memory.
    "num_parcels": 1_000_000,

    # Geographic scope.  “CA” signals statewide generation.
    "state": "CA",

    # If provided, a list of counties to emphasize.  If omitted or "ALL_CA",
    # the generator should auto‑load the full list of California counties.
    "county_pool": "ALL_CA",

    # Vintage range for building attributes.  Specific sampling strategy
    # (uniform, skewed to recent years, etc.) is up to the implementation.
    "year_range": (1960, 2023),

    # Optional: toggle for a quick dev sample (e.g., 10 k rows)
    # Implementation may expose this as a CLI flag instead.
    "sample_rows": 10_000



Sample Output:
{
  "parcel_id": "ca-********",
  "latitude": 34.0517,
  "longitude": -117.3892,
  "county": "San Bernardino",
  "zoning": "M1",
  "building_area": 83500,
  "lot_size_acres": 4.12,
  "year_built": 1998,
  "year_renovated": null,
  "building_class": "B",
  "std_land_use_code_description": "Warehouse/Distribution Center",
  "assessment.total_value": 6275000,
  "taxes.annual_tax_amount": 62800,
  "sales": "[{\"sale_date\":\"2018-11-02\",\"sale_price\":5700000}]",
  "mortgages": "[{\"lender_name\":\"First Pacific Bank\",\"original_loan_amount\":3420000,\"maturity_date\":\"2028-11-01\",\"interest_rate\":4.1}]",
  "true_owner_name": "Inland Logistics LLC",
  "owner_contact_phone": "(909) 555‑0133",
  "owner_contact_email": "<EMAIL>"
}

### Real world trend:
One‑page Industry Snapshot – CA Industrial RE (2025)
Inland Empire (San Bernardino & Riverside):
30 % of state industrial inventory. Typical modern warehouses: 200 k–600 k sf; land zoned M1/M2. 2024 asking rents $1.45 – $1.65 psf/mo; cap rates 4.5 – 5 %. Recent deal sizes $30–80 M.

Los Angeles County infill:
Older Class B/C buildings, 20 k–150 k sf. Zoning M1 (light) and CM (commercial‑manufacturing). High land cost ⇒ smaller lots (≤3 ac). 2024 vacancies sub‑2 %.

Bay Area (Alameda, Santa Clara, Solano):
Mix of advanced‑manufacturing shell space and last‑mile warehouses. Zoning I‑1/I‑2. Clean‑room retrofits push CapEx higher. 2024 sale comps $250–$350 psf for existing buildings.

San Diego–Tijuana cross‑border corridor:
Heavy tilt toward defense/aerospace suppliers and maquila logistics. Typical parcels 5–15 ac; building 50 k–250 k sf. Zoning IL‑2‑1, IH. Labor rates lower but power costs higher.

Central Valley (Fresno, Kern, Stanislaus):
Food‑grade cold storage and agricultural processing. Parcels 10–40 ac; buildings 100 k–400 k sf. Zoning M1 or county‑specific Ag/Industrial overlays. Cap rates 5.5 – 6 %.




### Batch Processing
- Optimal batch size: 500-1,000 records
- Memory optimization through batch generation

### Memory Management
- Scales to 100,000 parcels on standard laptops (8GB RAM)
- Garbage collection between batches
- Stream-based file writing for large datasets
