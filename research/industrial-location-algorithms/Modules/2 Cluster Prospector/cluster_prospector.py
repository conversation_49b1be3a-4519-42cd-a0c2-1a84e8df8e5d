import pandas as pd
import numpy as np
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
from typing import Dict, List, Tuple, Optional
from collections import Counter

class ClusterProspector:
    """Analyzes spatial clusters of industrial properties."""
    
    def __init__(self, eps: float = 0.01, min_samples: int = 5):
        """
        Initialize the cluster prospector.
        
        Args:
            eps: Maximum distance between samples (in degrees, ~1km)
            min_samples: Minimum samples per cluster
        """
        self.eps = eps
        self.min_samples = min_samples
        self.data = None
        self.clusters = None
        self.cluster_summary = None
        
    def load_data(self, filepath: str) -> None:
        """Load and preprocess the parcel data."""
        # Read data
        self.data = pd.read_csv(filepath)
        
        # Verify expected columns
        expected_columns = [
            'apn', 'latitude', 'longitude', 'fips', 'asset_category',
            'building_area', 'lot_area_ft', 'building_class', 'noi',
            'opex', 'annual_taxes', 'latest_sale_date', 'latest_sale_price',
            'loan_balance', 'loan_due_date', 'loan_rate',
            'owner_name', 'owner_phone', 'owner_email'
        ]
        
        # Check for missing columns
        missing_cols = [col for col in expected_columns if col not in self.data.columns]
        if missing_cols:
            print(f"Warning: Missing expected columns: {missing_cols}")
            print(f"Available columns: {self.data.columns.tolist()}")
        
        # Convert date columns
        date_cols = ['latest_sale_date', 'loan_due_date']
        for col in date_cols:
            if col in self.data.columns:
                self.data[col] = pd.to_datetime(self.data[col], errors='coerce')
            
        print(f"Loaded {len(self.data)} parcels")
        print(f"Columns found: {', '.join(self.data.columns)}")
        
        # Handle any missing values
        numeric_cols = ['latitude', 'longitude', 'building_area', 'lot_area_ft', 
                       'noi', 'opex', 'annual_tax', 'latest_sale_price', 
                       'loan_balance', 'loan_rate']
        for col in numeric_cols:
            if col in self.data.columns:
                self.data[col] = pd.to_numeric(self.data[col], errors='coerce')
        
    def identify_clusters(self) -> None:
        """Use DBSCAN to identify spatial clusters."""
        # Extract coordinates
        X = self.data[['latitude', 'longitude']].values
        
        # Scale coordinates for DBSCAN
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Apply DBSCAN
        dbscan = DBSCAN(eps=self.eps, min_samples=self.min_samples)
        self.data['cluster_id'] = dbscan.fit_predict(X_scaled)
        
        # Count clusters (excluding noise)
        n_clusters = len(set(self.data['cluster_id'])) - (1 if -1 in self.data['cluster_id'] else 0)
        print(f"Identified {n_clusters} clusters")
        
    def calculate_cluster_metrics(self) -> pd.DataFrame:
        """Calculate metrics for each cluster."""
        clusters = []
        
        # Process each cluster
        for cluster_id in sorted(self.data['cluster_id'].unique()):
            if cluster_id == -1:  # Skip noise
                continue
                
            cluster_data = self.data[self.data['cluster_id'] == cluster_id].copy()
            
            try:
                # Basic Size & Density Metrics
                metrics = {
                    'cluster_id': cluster_id,
                    'num_parcels': len(cluster_data),
                    'total_lot_area_ft': cluster_data['lot_area_ft'].fillna(0).sum(),
                    'total_building_area': cluster_data['building_area'].fillna(0).sum(),
                    'avg_lot_area_ft': cluster_data['lot_area_ft'].mean(),
                    'avg_building_area': cluster_data['building_area'].mean()
                }
                
                # Avoid division by zero
                if metrics['total_lot_area_ft'] > 0:
                    metrics['building_density_ratio'] = metrics['total_building_area'] / metrics['total_lot_area_ft']
                else:
                    metrics['building_density_ratio'] = 0
                
                # Financial Performance - handle null values
                total_noi = cluster_data['noi'].fillna(0).sum()
                total_taxes = cluster_data['annual_taxes'].fillna(0).sum()
                
                metrics.update({
                    'total_noi': total_noi,
                    'avg_noi_per_parcel': total_noi / metrics['num_parcels'] if metrics['num_parcels'] > 0 else 0,
                    'noisf': total_noi / metrics['total_building_area'] if metrics['total_building_area'] > 0 else 0,
                    'total_annual_taxes': total_taxes,
                    'taxes_per_sf': total_taxes / metrics['total_building_area'] if metrics['total_building_area'] > 0 else 0
                })
                
                # Sales Activity
                three_years_ago = pd.Timestamp.now() - pd.DateOffset(years=3)
                recent_sales = cluster_data[cluster_data['latest_sale_date'] >= three_years_ago]
                valid_sales = cluster_data['latest_sale_price'].notna()
                
                metrics.update({
                    'num_recent_sales': len(recent_sales),
                    'pct_recent_sales': len(recent_sales) / metrics['num_parcels'] if metrics['num_parcels'] > 0 else 0,
                    'avg_sale_price': cluster_data.loc[valid_sales, 'latest_sale_price'].mean(),
                    'median_sale_price': cluster_data.loc[valid_sales, 'latest_sale_price'].median(),
                    'price_per_sf': (cluster_data.loc[valid_sales, 'latest_sale_price'].mean() / 
                                   metrics['avg_building_area'] if metrics['avg_building_area'] > 0 else 0),
                    'total_transaction_volume': cluster_data['latest_sale_price'].fillna(0).sum()
                })
                
                # Loan Metrics
                has_loans = cluster_data['loan_balance'].notna()
                valid_loans = cluster_data.loc[has_loans]
                
                metrics.update({
                    'num_with_loans': has_loans.sum(),
                    'pct_with_loans': has_loans.mean(),
                    'total_loan_balance': cluster_data['loan_balance'].fillna(0).sum(),
                    'avg_loan_balance': valid_loans['loan_balance'].mean(),
                    'avg_loan_rate': valid_loans['loan_rate'].mean(),
                    'avg_loan_due_year': valid_loans['loan_due_date'].dt.year.mean()
                })
                
                # Calculate loan-to-sale ratio only for parcels with both values
                valid_ratios = cluster_data[cluster_data['loan_balance'].notna() & 
                                         cluster_data['latest_sale_price'].notna()]
                if len(valid_ratios) > 0:
                    loan_to_sale = valid_ratios['loan_balance'] / valid_ratios['latest_sale_price']
                    metrics['avg_loan_to_sale_ratio'] = loan_to_sale.mean()
                else:
                    metrics['avg_loan_to_sale_ratio'] = 0
                
                # Ownership Structure
                owner_counts = cluster_data['owner_name'].value_counts()
                metrics.update({
                    'num_unique_owners': len(owner_counts),
                    'ownership_concentration': owner_counts.iloc[0] / metrics['num_parcels'] if len(owner_counts) > 0 else 0,
                    'top_owner_name': owner_counts.index[0] if len(owner_counts) > 0 else 'Unknown'
                })
                
                # Categorical Composition
                building_class_dist = cluster_data['building_class'].value_counts()
                asset_category_dist = cluster_data['asset_category'].value_counts()
                
                metrics.update({
                    'dominant_building_class': building_class_dist.index[0] if len(building_class_dist) > 0 else 'Unknown',
                    'dominant_asset_category': asset_category_dist.index[0] if len(asset_category_dist) > 0 else 'Unknown',
                    'building_class_distribution': dict(building_class_dist),
                    'asset_category_distribution': dict(asset_category_dist)
                })
                
                clusters.append(metrics)
                
            except Exception as e:
                print(f"Error processing cluster {cluster_id}: {str(e)}")
                continue
        
        # Create summary DataFrame
        self.cluster_summary = pd.DataFrame(clusters)
        return self.cluster_summary
    
    def save_cluster_details(self, output_dir: str) -> None:
        """Save detailed data for each cluster."""
        os.makedirs(output_dir, exist_ok=True)
        
        for cluster_id in sorted(self.data['cluster_id'].unique()):
            if cluster_id == -1:  # Skip noise
                continue
                
            cluster_data = self.data[self.data['cluster_id'] == cluster_id]
            output_file = os.path.join(output_dir, f'cluster_{cluster_id}_parcels.csv')
            cluster_data.to_csv(output_file, index=False)
            
        # Save summary
        if self.cluster_summary is not None:
            summary_file = os.path.join(output_dir, 'cluster_summary.csv')
            self.cluster_summary.to_csv(summary_file, index=False)
    
    def plot_clusters(self, save_path: Optional[str] = None) -> None:
        """Generate a scatter plot of parcels colored by cluster."""
        plt.figure(figsize=(12, 8))
        
        # Plot noise points in gray
        noise = self.data[self.data['cluster_id'] == -1]
        plt.scatter(noise['longitude'], noise['latitude'], 
                   c='gray', alpha=0.5, s=10, label='Noise')
        
        # Plot clusters with different colors
        clusters = self.data[self.data['cluster_id'] != -1]
        scatter = plt.scatter(clusters['longitude'], clusters['latitude'],
                            c=clusters['cluster_id'], cmap='tab20',
                            s=30, alpha=0.6)
        
        plt.colorbar(scatter, label='Cluster ID')
        plt.xlabel('Longitude')
        plt.ylabel('Latitude')
        plt.title('Industrial Parcel Clusters')
        
        if save_path:
            plt.savefig(save_path)
        plt.show()

    def print_cluster_report(self) -> None:
        """Print a detailed report for each cluster with emoji indicators."""
        if self.cluster_summary is None:
            print("No cluster summary available. Run calculate_cluster_metrics() first.")
            return
            
        def format_number(num: float) -> str:
            """Format numbers with thousands separator and 2 decimal places."""
            if abs(num) >= 1_000_000:
                return f"{num/1_000_000:,.2f}M"
            return f"{num:,.2f}"
        
        print("\n" + "="*80)
        print("📊 CLUSTER ANALYSIS REPORT 📊")
        print("="*80)
        
        for _, cluster in self.cluster_summary.iterrows():
            print(f"\n{'🧱'*3} CLUSTER {int(cluster['cluster_id'])} {'🧱'*3}")
            print(f"{'='*40}")
            
            # Size metrics
            print(f"📦 Parcels: {int(cluster['num_parcels']):,}")
            print(f"🏭 Total Building Area: {format_number(cluster['total_building_area'])} ft²")
            print(f"🌐 Total Lot Area: {format_number(cluster['total_lot_area_ft'])} ft²")
            print(f"🏗️ Building Density Ratio: {cluster['building_density_ratio']:.2%}")
            
            # Financial metrics
            print(f"\n💰 Financial Performance:")
            print(f"💵 Total NOI: ${format_number(cluster['total_noi'])}")
            print(f"📊 NOI per SF: ${cluster['noisf']:.2f}")
            print(f"💸 Annual Taxes per SF: ${cluster['taxes_per_sf']:.2f}")
            
            # Sales metrics
            print(f"\n🏷️ Sales Activity:")
            print(f"💰 Median Sale Price: ${format_number(cluster['median_sale_price'])}")
            print(f"📈 Recent Sales: {cluster['pct_recent_sales']:.1%}")
            print(f"🏦 Avg Loan-to-Sale Ratio: {cluster['avg_loan_to_sale_ratio']:.2%}")
            
            # Ownership metrics
            print(f"\n👥 Ownership Structure:")
            print(f"📋 Unique Owners: {int(cluster['num_unique_owners']):,}")
            print(f"🏆 Top Owner: {cluster['top_owner_name']}")
            print(f"📊 Ownership Concentration: {cluster['ownership_concentration']:.1%}")
            
            # Property characteristics
            print(f"\n🏢 Property Characteristics:")
            print(f"📚 Dominant Building Class: {cluster['dominant_building_class']}")
            print(f"🏢 Dominant Asset Category: {cluster['dominant_asset_category']}")
            
            print("\n" + "-"*80)

def main():
    # Initialize prospector
    prospector = ClusterProspector(eps=0.01, min_samples=5)
    
    # Load and process data
    input_path = r"C:\Users\<USER>\OneDrive\桌面\雾谷科技\industrial location Algorithms\Modules\1 Mock Data\generated_data\california_parcels.csv"
    output_dir = r"C:\Users\<USER>\OneDrive\桌面\雾谷科技\industrial location Algorithms\Modules\2 Cluster Prospector\cluster_results"
    
    print(f"Loading data from: {input_path}")
    prospector.load_data(input_path)
    
    # Identify clusters
    print("\nIdentifying spatial clusters...")
    prospector.identify_clusters()
    
    # Calculate metrics
    print("\nCalculating cluster metrics...")
    cluster_summary = prospector.calculate_cluster_metrics()
    
    # Print detailed cluster report
    prospector.print_cluster_report()
    
    # Save results
    print(f"\nSaving results to: {output_dir}")
    prospector.save_cluster_details(output_dir)
    
    # Generate plot
    plot_path = os.path.join(output_dir, "cluster_map.png")
    print(f"\nGenerating cluster visualization: {plot_path}")
    prospector.plot_clusters(save_path=plot_path)
    
    print("\nAnalysis complete!")

if __name__ == "__main__":
    main() 