import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
from pydantic import BaseModel, Field

class UnderwritingInput(BaseModel):
    """Input data model for underwriting analysis"""
    parcel_id: str
    active_loan_amount: float
    current_property_value: float
    net_operating_income: float
    annual_debt_service: float
    interest_rate: float
    maturity_date: datetime

class ScenarioResult(BaseModel):
    """Results for a single scenario"""
    name: str
    ltv: float
    dscr: float

class UnderwritingOutput(BaseModel):
    """Output data model for underwriting analysis"""
    parcel_id: str
    current_ltv: float
    current_dscr: float
    debt_yield: float
    stress_dscr: Dict[str, float]
    implied_cap_rate_value: float
    risk_flags: List[str]

class ParcelUnderwriter:
    """Analyzes parcels for underwriting metrics and risk assessment."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize with optional configuration."""
        self.config = {
            "stress_test": {
                "interest_rate_shocks": [200, 500],  # basis points
            },
            "thresholds": {
                "min_dscr": 1.25,
                "max_ltv": 0.75,
                "min_debt_yield": 0.085,  # 8.5%
                "maturity_warning_days": 90
            },
            "valuation": {
                "cap_rate": 0.07,  # 7% default cap rate
            }
        }
        
        # Override defaults with provided config
        if config:
            self.config.update(config)
    
    def calculate_ltv(self, loan_amount: float, property_value: float) -> float:
        """Calculate Loan-to-Value ratio."""
        return loan_amount / property_value if property_value > 0 else float('inf')
    
    def calculate_dscr(self, noi: float, debt_service: float) -> float:
        """Calculate Debt Service Coverage Ratio."""
        return noi / debt_service if debt_service > 0 else float('inf')
    
    def calculate_debt_yield(self, noi: float, loan_amount: float) -> float:
        """Calculate Debt Yield."""
        return noi / loan_amount if loan_amount > 0 else float('inf')
    
    def calculate_implied_value(self, noi: float, cap_rate: Optional[float] = None) -> float:
        """Calculate implied property value using cap rate."""
        cap_rate = cap_rate or self.config["valuation"]["cap_rate"]
        return noi / cap_rate if cap_rate > 0 else 0
    
    def run_stress_test(self, input_data: UnderwritingInput) -> Dict[str, float]:
        """Run stress tests for DSCR under different scenarios."""
        stress_results = {}
        
        # Interest rate stress tests
        for bps in self.config["stress_test"]["interest_rate_shocks"]:
            rate_shock = bps / 10000  # Convert bps to decimal
            new_rate = input_data.interest_rate + rate_shock
            # Recalculate annual debt service with new rate
            # Using simplified calculation - in practice would use proper amortization
            shocked_debt_service = input_data.active_loan_amount * new_rate
            shocked_dscr = self.calculate_dscr(input_data.net_operating_income, shocked_debt_service)
            stress_results[f"rate_shock_{bps}bp"] = shocked_dscr
            
        return stress_results
    
    def generate_risk_flags(self, input_data: UnderwritingInput, metrics: Dict[str, float]) -> List[str]:
        """Generate risk flags based on metrics and thresholds."""
        flags = []
        
        # Check DSCR
        if metrics["current_dscr"] < self.config["thresholds"]["min_dscr"]:
            flags.append(f"low_DSCR_{metrics['current_dscr']:.2f}")
        
        # Check LTV
        if metrics["current_ltv"] > self.config["thresholds"]["max_ltv"]:
            flags.append(f"high_LTV_{metrics['current_ltv']:.2f}")
        
        # Check Debt Yield
        if metrics["debt_yield"] < self.config["thresholds"]["min_debt_yield"]:
            flags.append(f"low_debt_yield_{metrics['debt_yield']:.2f}")
        
        # Check maturity
        days_to_maturity = (input_data.maturity_date - datetime.now()).days
        if days_to_maturity <= self.config["thresholds"]["maturity_warning_days"]:
            flags.append(f"maturity_{days_to_maturity}d")
            
        return flags
    
    def run_scenarios(self, input_data: UnderwritingInput) -> List[ScenarioResult]:
        """Run base, upside, and downside scenarios."""
        scenarios = []
        
        # Base case - current metrics
        scenarios.append(ScenarioResult(
            name="base",
            ltv=self.calculate_ltv(input_data.active_loan_amount, input_data.current_property_value),
            dscr=self.calculate_dscr(input_data.net_operating_income, input_data.annual_debt_service)
        ))
        
        # Upside case - higher NOI from rent growth, lower vacancy
        upside_noi = input_data.net_operating_income * 1.1  # 10% NOI improvement
        upside_value = input_data.current_property_value * 1.05  # 5% value appreciation
        scenarios.append(ScenarioResult(
            name="upside",
            ltv=self.calculate_ltv(input_data.active_loan_amount, upside_value),
            dscr=self.calculate_dscr(upside_noi, input_data.annual_debt_service)
        ))
        
        # Downside case - lower NOI from higher vacancy, higher debt service from rate increase
        downside_noi = input_data.net_operating_income * 0.9  # 10% NOI decrease
        downside_debt_service = input_data.annual_debt_service * 1.1  # 10% debt service increase
        scenarios.append(ScenarioResult(
            name="downside",
            ltv=self.calculate_ltv(input_data.active_loan_amount, input_data.current_property_value * 0.95),
            dscr=self.calculate_dscr(downside_noi, downside_debt_service)
        ))
        
        return scenarios
    
    def analyze_parcel(self, input_data: UnderwritingInput) -> UnderwritingOutput:
        """Perform comprehensive underwriting analysis on a parcel."""
        
        # Calculate base metrics
        current_ltv = self.calculate_ltv(
            input_data.active_loan_amount,
            input_data.current_property_value
        )
        
        current_dscr = self.calculate_dscr(
            input_data.net_operating_income,
            input_data.annual_debt_service
        )
        
        debt_yield = self.calculate_debt_yield(
            input_data.net_operating_income,
            input_data.active_loan_amount
        )
        
        # Calculate implied value
        implied_value = self.calculate_implied_value(input_data.net_operating_income)
        
        # Run stress tests
        stress_results = self.run_stress_test(input_data)
        
        # Generate risk flags
        metrics = {
            "current_dscr": current_dscr,
            "current_ltv": current_ltv,
            "debt_yield": debt_yield
        }
        risk_flags = self.generate_risk_flags(input_data, metrics)
        
        # Create output
        return UnderwritingOutput(
            parcel_id=input_data.parcel_id,
            current_ltv=current_ltv,
            current_dscr=current_dscr,
            debt_yield=debt_yield,
            stress_dscr=stress_results,
            implied_cap_rate_value=implied_value,
            risk_flags=risk_flags
        )

class ParcelDataLoader:
    """Loads and manages parcel data from CSV file."""
    
    def __init__(self, csv_path: str = "C:/Users/<USER>/OneDrive/桌面/雾谷科技/industrial location Algorithms/Modules/1 Mock Data/generated_data/california_parcels.csv"):
        """Initialize with path to CSV file."""
        self.csv_path = csv_path
        print(f"Loading data from: {csv_path}")
        # Read CSV with APN as string type
        self.df = pd.read_csv(csv_path, dtype={'apn': str})
        print(f"Loaded {len(self.df)} parcels from database")
    
    def calculate_annual_debt_service(self, loan_balance: float, loan_rate: float) -> float:
        """Calculate annual debt service using simple interest calculation."""
        # Simple annual debt service calculation (principal + interest)
        # In reality, this would use a more sophisticated amortization schedule
        return loan_balance * (loan_rate + 0.1)  # Assuming 10% of principal for amortization
    
    def get_parcel_by_apn(self, apn: str) -> Optional[Dict[str, Any]]:
        """Fetch parcel data by APN."""
        try:
            # Find the parcel row
            parcel = self.df[self.df['apn'] == apn]
            
            if len(parcel) == 0:
                print(f"Debug: No match found for APN '{apn}'")
                print(f"Debug: Available APNs (first 5): {', '.join(self.df['apn'].head().tolist())}")
                return None
                
            # Convert row to dictionary
            parcel_data = parcel.iloc[0].to_dict()
            
            # Calculate annual debt service
            annual_debt_service = self.calculate_annual_debt_service(
                loan_balance=parcel_data['loan_balance'],
                loan_rate=parcel_data['loan_rate']
            )
            
            # Use latest sale price as current property value, if available
            current_value = (parcel_data['latest_sale_price'] 
                           if pd.notnull(parcel_data['latest_sale_price']) 
                           else parcel_data['loan_balance'] * 1.25)  # Assume LTV of 80% if no sale price
            
            # Create UnderwritingInput from parcel data
            return {
                "parcel_id": parcel_data['apn'],
                "active_loan_amount": parcel_data['loan_balance'],
                "current_property_value": current_value,
                "net_operating_income": parcel_data['noi'],
                "annual_debt_service": annual_debt_service,
                "interest_rate": parcel_data['loan_rate'],
                "maturity_date": pd.to_datetime(parcel_data['loan_due_date']),
                "building_type": parcel_data['asset_category'],
                "building_area": parcel_data['building_area'],
                "building_class": parcel_data['building_class'],
                "lot_area": parcel_data['lot_area_ft'],
                "fips": parcel_data['fips'],
                "latest_sale_price": parcel_data['latest_sale_price'],
                "latest_sale_date": parcel_data['latest_sale_date']
            }
        except Exception as e:
            print(f"Error fetching parcel data: {str(e)}")
            return None

def main():
    """Interactive command-line interface for Parcel Underwriter."""
    print("\n📊 Welcome to Parcel Underwriter 📊")
    print("=" * 50)
    
    # Initialize underwriter and data loader
    underwriter = ParcelUnderwriter()
    data_loader = ParcelDataLoader()
    
    while True:
        print("\nPlease enter the APN number of the parcel you want to analyze")
        print("(or type 'exit' to quit)")
        apn = input("APN > ").strip()
        
        if apn.lower() == 'exit':
            print("\nThank you for using Parcel Underwriter!")
            break
            
        try:
            # Fetch parcel data from CSV
            parcel_data = data_loader.get_parcel_by_apn(apn)
            
            if not parcel_data:
                print(f"\n❌ Error: Could not find parcel with APN: {apn}")
                continue
            
            # Display parcel info (matching Parcel Prospector's format)
            print(f"\nFound parcel: {apn}")
            print(f"Building Type: {parcel_data['building_type']}")
            print(f"Building Area: {parcel_data['building_area']:,.0f} sq ft")
            if pd.notnull(parcel_data['building_class']):
                print(f"Building Class: {parcel_data['building_class']}")
            print(f"Lot Area: {parcel_data['lot_area']:,.0f} sq ft")
            print(f"County FIPS: {parcel_data['fips']}")
            
            while True:
                calculate = input("\nWould you like to run underwriting analysis? (yes/no) > ").strip().lower()
                if calculate in ['yes', 'no']:
                    break
                print("Please enter 'yes' or 'no'")
            
            if calculate == 'yes':
                print("\nRunning underwriting analysis...")
                
                # Create input data from parcel data
                sample_input = UnderwritingInput(
                    parcel_id=parcel_data['parcel_id'],
                    active_loan_amount=parcel_data['active_loan_amount'],
                    current_property_value=parcel_data['current_property_value'],
                    net_operating_income=parcel_data['net_operating_income'],
                    annual_debt_service=parcel_data['annual_debt_service'],
                    interest_rate=parcel_data['interest_rate'],
                    maturity_date=parcel_data['maturity_date']
                )
                
                # Perform analysis
                analysis = underwriter.analyze_parcel(sample_input)
                
                # Display results in a format similar to Parcel Prospector
                print("\n📈 Underwriting Analysis 📈")
                print("=" * 50)
                
                print(f"\n💰 Loan Metrics")
                print(f"Active Loan Balance: ${sample_input.active_loan_amount:,.2f}")
                print(f"Property Value: ${sample_input.current_property_value:,.2f}")
                if pd.notnull(parcel_data['latest_sale_price']):
                    print(f"Latest Sale Price: ${parcel_data['latest_sale_price']:,.2f}")
                    print(f"Latest Sale Date: {parcel_data['latest_sale_date']}")
                print(f"Net Operating Income: ${sample_input.net_operating_income:,.2f}")
                print(f"Annual Debt Service: ${sample_input.annual_debt_service:,.2f}")
                print(f"Interest Rate: {sample_input.interest_rate:.2%}")
                print(f"Loan Maturity: {sample_input.maturity_date.strftime('%Y-%m-%d')}")
                
                print(f"\n📊 Key Ratios")
                print(f"LTV: {analysis.current_ltv:.1%}")
                print(f"DSCR: {analysis.current_dscr:.2f}x")
                print(f"Debt Yield: {analysis.debt_yield:.1%}")
                
                print(f"\n⚠️ Risk Assessment")
                if analysis.risk_flags:
                    for flag in analysis.risk_flags:
                        print(f"- {flag}")
                else:
                    print("No risk flags identified")
                
                print(f"\n🔄 Interest Rate Stress Tests")
                for scenario, dscr in analysis.stress_dscr.items():
                    print(f"{scenario}: DSCR = {dscr:.2f}x")
                
                print(f"\n💎 Valuation Analysis")
                print(f"Implied Value (Cap Rate Method): ${analysis.implied_cap_rate_value:,.2f}")
                print(f"Current Value: ${sample_input.current_property_value:,.2f}")
                value_diff = analysis.implied_cap_rate_value - sample_input.current_property_value
                value_diff_pct = value_diff / sample_input.current_property_value
                print(f"Value Difference: ${value_diff:,.2f} ({value_diff_pct:+.1%})")
                
                # Ask if user wants to save results
                save = input("\nWould you like to save these results? (yes/no) > ").strip().lower()
                if save == 'yes':
                    filename = f"parcel_{apn}_underwriting.json"
                    with open(filename, 'w') as f:
                        json.dump(analysis.dict(), f, indent=2, default=str)
                    print(f"\nResults saved to {filename}")
            
            # Ask if user wants to analyze another parcel
            another = input("\nWould you like to analyze another parcel? (yes/no) > ").strip().lower()
            if another != 'yes':
                print("\nThank you for using Parcel Underwriter!")
                break
                
        except Exception as e:
            print(f"\n❌ Error: Could not analyze parcel {apn}")
            print(f"Details: {str(e)}")
            print("Please try again with a different Parcel ID")

if __name__ == "__main__":
    main() 