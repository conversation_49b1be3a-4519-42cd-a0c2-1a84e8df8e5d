"""
Incentives model for tax and regulatory benefits
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field


class IncentiveData(BaseModel):
    """Tax incentives and regulatory benefits for a location"""
    
    # Tax rates
    property_tax_rate: float = Field(..., description="Property tax rate as decimal")
    sales_tax_rate: Optional[float] = Field(None, description="Sales tax rate as decimal")
    corporate_tax_rate: Optional[float] = Field(None, description="Corporate income tax rate")
    
    # Tax abatements
    property_tax_abatement: Optional[bool] = Field(None, description="Property tax abatement available")
    abatement_years: Optional[int] = Field(None, description="Number of abatement years")
    abatement_percentage: Optional[float] = Field(None, description="Percentage of taxes abated")
    
    # Economic development incentives
    tax_increment_financing: Optional[bool] = Field(None, description="TIF available")
    enterprise_zone: Optional[bool] = Field(None, description="Located in enterprise zone")
    opportunity_zone: Optional[bool] = Field(None, description="Located in opportunity zone")
    
    # Job creation incentives
    job_creation_credits: Optional[bool] = Field(None, description="Job creation tax credits available")
    jobs_required: Optional[int] = Field(None, description="Minimum jobs required for incentives")
    wage_requirements: Optional[float] = Field(None, description="Minimum wage requirements")
    
    # Investment incentives
    investment_tax_credits: Optional[bool] = Field(None, description="Investment tax credits available")
    minimum_investment: Optional[float] = Field(None, description="Minimum investment required")
    equipment_exemptions: Optional[bool] = Field(None, description="Equipment tax exemptions")
    
    # Utility incentives
    utility_rebates: Optional[bool] = Field(None, description="Utility rebates available")
    energy_efficiency_incentives: Optional[bool] = Field(None, description="Energy efficiency incentives")
    renewable_energy_credits: Optional[bool] = Field(None, description="Renewable energy credits")
    
    # Workforce development
    training_grants: Optional[bool] = Field(None, description="Workforce training grants available")
    training_amount: Optional[float] = Field(None, description="Training grant amount")
    
    # Financing assistance
    low_interest_loans: Optional[bool] = Field(None, description="Low-interest loans available")
    loan_guarantees: Optional[bool] = Field(None, description="Loan guarantees available")
    industrial_revenue_bonds: Optional[bool] = Field(None, description="Industrial revenue bonds available")
    
    # Infrastructure support
    infrastructure_grants: Optional[bool] = Field(None, description="Infrastructure grants available")
    road_improvements: Optional[bool] = Field(None, description="Road improvement support")
    utility_extensions: Optional[bool] = Field(None, description="Utility extension assistance")
    
    # Available programs
    programs: List[str] = Field(default_factory=list, description="List of available incentive programs")
    program_details: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="Detailed program information")
    
    # Geographic context
    jurisdiction: str = Field(..., description="Governing jurisdiction")
    state: Optional[str] = Field(None, description="State")
    county: Optional[str] = Field(None, description="County")
    city: Optional[str] = Field(None, description="City")
    
    # Application process
    application_required: Optional[bool] = Field(None, description="Formal application required")
    approval_timeline: Optional[str] = Field(None, description="Typical approval timeline")
    contact_info: Optional[Dict[str, str]] = Field(None, description="Contact information for applications")
    
    # Data metadata
    data_source: Optional[str] = Field(None, description="Source of incentive data")
    last_updated: Optional[str] = Field(None, description="Last update date")
    
    # Additional data
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional incentive data")
    
    def to_dict(self) -> dict:
        """Convert to dictionary format"""
        return self.model_dump()
    
    @classmethod
    def from_dict(cls, data: dict) -> "IncentiveData":
        """Create from dictionary format"""
        return cls(**data)
    
    def calculate_tax_savings(self, assessed_value: float, years: int = 10) -> Dict[str, float]:
        """Calculate potential tax savings over time"""
        savings = {
            "annual_property_tax": assessed_value * self.property_tax_rate,
            "total_savings": 0.0,
            "abatement_savings": 0.0
        }
        
        # Calculate property tax abatement savings
        if self.property_tax_abatement and self.abatement_years and self.abatement_percentage:
            abatement_years = min(self.abatement_years, years)
            annual_tax = assessed_value * self.property_tax_rate
            annual_abatement = annual_tax * self.abatement_percentage
            savings["abatement_savings"] = annual_abatement * abatement_years
            savings["total_savings"] += savings["abatement_savings"]
        
        return savings
    
    def incentive_value_score(self) -> float:
        """Calculate overall incentive value score (0-100)"""
        score = 0.0
        
        # Property tax rate (lower is better)
        if self.property_tax_rate is not None:
            # Score based on tax rate (assume 2% is average)
            tax_score = max(0, 100 - (self.property_tax_rate * 5000))  # Scale appropriately
            score += tax_score * 0.3
        
        # Abatement availability
        if self.property_tax_abatement:
            score += 25
            if self.abatement_percentage:
                score += self.abatement_percentage * 25  # Up to 25 more points
        
        # Special zones
        if self.enterprise_zone:
            score += 10
        if self.opportunity_zone:
            score += 10
        
        # Job and investment incentives
        if self.job_creation_credits:
            score += 10
        if self.investment_tax_credits:
            score += 10
        
        return min(100, score)
    
    def get_program_summary(self) -> List[str]:
        """Get a summary of available programs"""
        summary = []
        
        if self.property_tax_abatement:
            summary.append(f"Property tax abatement: {self.abatement_percentage:.1%} for {self.abatement_years} years")
        
        if self.enterprise_zone:
            summary.append("Enterprise Zone benefits")
        
        if self.opportunity_zone:
            summary.append("Opportunity Zone designation")
        
        if self.job_creation_credits:
            summary.append("Job creation tax credits")
        
        if self.investment_tax_credits:
            summary.append("Investment tax credits")
        
        for program in self.programs:
            summary.append(program)
        
        return summary 