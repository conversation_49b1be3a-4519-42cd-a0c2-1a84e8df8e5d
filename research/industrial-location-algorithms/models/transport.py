"""
Transportation metrics model for logistics analysis
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field


class TransportMetrics(BaseModel):
    """Transportation access and cost metrics for a location"""
    
    # Distance metrics (in miles)
    distance_to_highway: float = Field(..., description="Distance to major highway in miles")
    distance_to_port: Optional[float] = Field(None, description="Distance to nearest port in miles")
    distance_to_airport: Optional[float] = Field(None, description="Distance to airport in miles")
    distance_to_rail: Optional[float] = Field(None, description="Distance to rail terminal in miles")
    
    # Travel time metrics (in minutes)
    drive_time_to_highway: Optional[float] = Field(None, description="Drive time to highway in minutes")
    drive_time_to_port: Optional[float] = Field(None, description="Drive time to port in minutes")
    drive_time_to_airport: Optional[float] = Field(None, description="Drive time to airport in minutes")
    drive_time_to_rail: Optional[float] = Field(None, description="Drive time to rail in minutes")
    
    # Highway access
    highway_name: Optional[str] = Field(None, description="Name of nearest major highway")
    highway_type: Optional[str] = Field(None, description="Type of highway (interstate, state, etc.)")
    traffic_volume: Optional[int] = Field(None, description="Average daily traffic volume")
    
    # Port access
    port_name: Optional[str] = Field(None, description="Name of nearest port")
    port_type: Optional[str] = Field(None, description="Port type (deep water, inland, etc.)")
    port_capacity: Optional[str] = Field(None, description="Port capacity classification")
    
    # Airport access
    airport_name: Optional[str] = Field(None, description="Name of nearest airport")
    airport_type: Optional[str] = Field(None, description="Airport type (international, regional, etc.)")
    cargo_capacity: Optional[bool] = Field(None, description="Airport has cargo facilities")
    
    # Rail access
    rail_name: Optional[str] = Field(None, description="Name of rail line/terminal")
    rail_type: Optional[str] = Field(None, description="Rail type (freight, passenger, both)")
    rail_operator: Optional[str] = Field(None, description="Rail operator/company")
    
    # Transportation costs
    trucking_cost_per_mile: Optional[float] = Field(None, description="Regional trucking cost per mile")
    rail_cost_per_mile: Optional[float] = Field(None, description="Rail shipping cost per mile")
    port_handling_cost: Optional[float] = Field(None, description="Port handling cost estimate")
    
    # Traffic and congestion
    congestion_index: Optional[float] = Field(None, description="Traffic congestion index (0-1)")
    peak_hour_delay: Optional[float] = Field(None, description="Average peak hour delay in minutes")
    
    # Annual transportation metrics
    annual_vmt: Optional[float] = Field(None, description="Annual vehicle miles traveled estimate")
    annual_transport_cost: Optional[float] = Field(None, description="Annual transportation cost estimate")
    
    # Accessibility scores
    highway_accessibility: Optional[float] = Field(None, description="Highway accessibility score (0-100)")
    multimodal_connectivity: Optional[float] = Field(None, description="Multimodal connectivity score (0-100)")
    
    # Location context
    location_name: str = Field(..., description="Location identifier")
    coordinates: Optional[tuple] = Field(None, description="Lat/lng coordinates")
    
    # Data metadata
    data_source: Optional[str] = Field(None, description="Source of transportation data")
    data_date: Optional[str] = Field(None, description="Date of data collection")
    
    # Additional metrics
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional transport metrics")
    
    class Config:
        arbitrary_types_allowed = True
    
    def to_dict(self) -> dict:
        """Convert to dictionary format"""
        return self.model_dump()
    
    @classmethod
    def from_dict(cls, data: dict) -> "TransportMetrics":
        """Create from dictionary format"""
        return cls(**data)
    
    def calculate_logistics_score(self) -> float:
        """Calculate overall logistics accessibility score (0-100)"""
        score = 0.0
        weights = 0.0
        
        # Highway access (40% weight)
        if self.distance_to_highway is not None:
            highway_score = max(0, 100 - (self.distance_to_highway * 10))  # Penalty per mile
            score += highway_score * 0.4
            weights += 0.4
        
        # Port access (25% weight)
        if self.distance_to_port is not None:
            port_score = max(0, 100 - (self.distance_to_port * 2))  # Less penalty per mile
            score += port_score * 0.25
            weights += 0.25
        
        # Rail access (20% weight)
        if self.distance_to_rail is not None:
            rail_score = max(0, 100 - (self.distance_to_rail * 5))
            score += rail_score * 0.2
            weights += 0.2
        
        # Airport access (15% weight)
        if self.distance_to_airport is not None:
            airport_score = max(0, 100 - (self.distance_to_airport * 3))
            score += airport_score * 0.15
            weights += 0.15
        
        return score / weights if weights > 0 else 0.0
    
    def estimate_annual_transport_cost(self, annual_shipments: int, avg_distance: float = 100) -> float:
        """Estimate annual transportation costs"""
        if self.trucking_cost_per_mile:
            return annual_shipments * avg_distance * self.trucking_cost_per_mile
        return 0.0 