"""
Parcel model for industrial site data
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from datetime import datetime


class Parcel(BaseModel):
    """Represents a parcel of land for industrial development"""
    
    # Core identifiers
    id: str = Field(..., description="Unique parcel identifier")
    lat: float = Field(..., description="Latitude coordinate")
    lng: float = Field(..., description="Longitude coordinate")
    
    # Basic properties
    land_use: str = Field(..., description="Current land use classification")
    lot_size: float = Field(..., description="Lot size in square feet")
    assessed_value: Optional[float] = Field(None, description="Assessed value in USD")
    
    # Location details
    address: Optional[str] = Field(None, description="Street address")
    city: Optional[str] = Field(None, description="City")
    county: Optional[str] = Field(None, description="County")
    state: Optional[str] = Field(None, description="State")
    zip_code: Optional[str] = Field(None, description="ZIP code")
    
    # Zoning and regulatory
    zoning_code: Optional[str] = Field(None, description="Current zoning designation")
    zoning_description: Optional[str] = Field(None, description="Zoning description")
    
    # Physical characteristics
    frontage: Optional[float] = Field(None, description="Street frontage in feet")
    depth: Optional[float] = Field(None, description="Lot depth in feet")
    topography: Optional[str] = Field(None, description="Topographical description")
    soil_type: Optional[str] = Field(None, description="Soil classification")
    
    # Infrastructure access
    water_available: Optional[bool] = Field(None, description="Water access available")
    sewer_available: Optional[bool] = Field(None, description="Sewer access available")
    electric_available: Optional[bool] = Field(None, description="Electric access available")
    gas_available: Optional[bool] = Field(None, description="Natural gas access available")
    
    # Transportation
    distance_to_highway: Optional[float] = Field(None, description="Distance to highway in miles")
    distance_to_port: Optional[float] = Field(None, description="Distance to port in miles")
    distance_to_airport: Optional[float] = Field(None, description="Distance to airport in miles")
    distance_to_rail: Optional[float] = Field(None, description="Distance to rail in miles")
    
    # Owner information
    owner_name: Optional[str] = Field(None, description="Property owner name")
    owner_contact: Optional[Dict[str, Any]] = Field(None, description="Owner contact information")
    
    # Financial
    market_value: Optional[float] = Field(None, description="Market value in USD")
    price_per_sqft: Optional[float] = Field(None, description="Price per square foot")
    
    # Prospecting flags
    exclusive_flag: Optional[bool] = Field(False, description="Exclusive listing flag")
    exclusive_timestamp: Optional[datetime] = Field(None, description="When exclusive flag was set")
    
    # Additional metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional parcel metadata")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
    
    def to_dict(self) -> dict:
        """Convert to dictionary format"""
        return self.model_dump()
    
    @classmethod
    def from_dict(cls, data: dict) -> "Parcel":
        """Create from dictionary format"""
        return cls(**data) 