"""
Deal models for transaction management
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class TaskStatus(str, Enum):
    """Task status enumeration"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    BLOCKED = "blocked"
    CANCELLED = "cancelled"


class DealStage(str, Enum):
    """Deal stage enumeration"""
    LEAD = "lead"
    QUALIFIED = "qualified"
    PROPOSAL = "proposal"
    NEGOTIATION = "negotiation"
    DUE_DILIGENCE = "due_diligence"
    FINANCING = "financing"
    CLOSING = "closing"
    CLOSED = "closed"
    LOST = "lost"


class DocumentType(str, Enum):
    """Document type enumeration"""
    CONTRACT = "contract"
    FINANCIAL = "financial"
    LEGAL = "legal"
    TECHNICAL = "technical"
    REGULATORY = "regulatory"
    CORRESPONDENCE = "correspondence"


class Task(BaseModel):
    """Represents a task in the deal workflow"""
    
    id: str = Field(..., description="Unique task identifier")
    name: str = Field(..., description="Task name")
    description: Optional[str] = Field(None, description="Task description")
    
    # Status and timing
    status: TaskStatus = Field(TaskStatus.PENDING, description="Current task status")
    priority: Optional[str] = Field(None, description="Task priority (high, medium, low)")
    due_date: Optional[datetime] = Field(None, description="Task due date")
    created_date: datetime = Field(default_factory=datetime.now, description="Task creation date")
    completed_date: Optional[datetime] = Field(None, description="Task completion date")
    
    # Assignment
    assigned_to: Optional[str] = Field(None, description="Person assigned to task")
    assigned_team: Optional[str] = Field(None, description="Team assigned to task")
    
    # Dependencies
    depends_on: List[str] = Field(default_factory=list, description="List of task IDs this depends on")
    blocks: List[str] = Field(default_factory=list, description="List of task IDs this blocks")
    
    # Additional data
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional task metadata")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class Document(BaseModel):
    """Represents a document in the deal"""
    
    id: str = Field(..., description="Unique document identifier")
    name: str = Field(..., description="Document name")
    document_type: DocumentType = Field(..., description="Type of document")
    
    # File information
    file_path: Optional[str] = Field(None, description="Path to document file")
    file_size: Optional[int] = Field(None, description="File size in bytes")
    mime_type: Optional[str] = Field(None, description="MIME type of file")
    
    # Document metadata
    version: str = Field("1.0", description="Document version")
    created_date: datetime = Field(default_factory=datetime.now, description="Document creation date")
    modified_date: Optional[datetime] = Field(None, description="Last modification date")
    
    # Author and approval
    created_by: Optional[str] = Field(None, description="Document creator")
    approved_by: Optional[str] = Field(None, description="Document approver")
    approval_date: Optional[datetime] = Field(None, description="Approval date")
    
    # Status
    is_final: bool = Field(False, description="Whether document is final version")
    requires_signature: bool = Field(False, description="Whether document requires signature")
    is_signed: bool = Field(False, description="Whether document is signed")
    
    # Additional data
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional document metadata")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class Deal(BaseModel):
    """Represents a real estate deal"""
    
    # Core identifiers
    id: str = Field(..., description="Unique deal identifier")
    name: str = Field(..., description="Deal name/title")
    parcel_id: Optional[str] = Field(None, description="Associated parcel ID")
    
    # Deal details
    current_stage: DealStage = Field(DealStage.LEAD, description="Current deal stage")
    deal_type: str = Field(..., description="Type of deal (purchase, lease, etc.)")
    deal_value: Optional[float] = Field(None, description="Total deal value")
    
    # Parties involved
    buyer: Optional[str] = Field(None, description="Buyer information")
    seller: Optional[str] = Field(None, description="Seller information")
    broker: Optional[str] = Field(None, description="Broker information")
    attorney: Optional[str] = Field(None, description="Attorney information")
    
    # Key dates
    created_date: datetime = Field(default_factory=datetime.now, description="Deal creation date")
    target_closing_date: Optional[datetime] = Field(None, description="Target closing date")
    actual_closing_date: Optional[datetime] = Field(None, description="Actual closing date")
    
    # Financial terms
    purchase_price: Optional[float] = Field(None, description="Purchase price")
    earnest_money: Optional[float] = Field(None, description="Earnest money amount")
    financing_amount: Optional[float] = Field(None, description="Financing amount")
    down_payment: Optional[float] = Field(None, description="Down payment amount")
    
    # Workflow
    tasks: List[Task] = Field(default_factory=list, description="List of deal tasks")
    documents: List[Document] = Field(default_factory=list, description="List of deal documents")
    
    # Status tracking
    is_active: bool = Field(True, description="Whether deal is active")
    probability: Optional[float] = Field(None, description="Probability of closing (0-1)")
    
    # Communication
    notes: List[str] = Field(default_factory=list, description="Deal notes")
    last_contact: Optional[datetime] = Field(None, description="Last contact date")
    
    # Additional data
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional deal metadata")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
    
    def to_dict(self) -> dict:
        """Convert to dictionary format"""
        return self.model_dump()
    
    @classmethod
    def from_dict(cls, data: dict) -> "Deal":
        """Create from dictionary format"""
        return cls(**data)
    
    def get_pending_tasks(self) -> List[Task]:
        """Get all pending tasks"""
        return [task for task in self.tasks if task.status == TaskStatus.PENDING]
    
    def get_overdue_tasks(self) -> List[Task]:
        """Get all overdue tasks"""
        now = datetime.now()
        return [
            task for task in self.tasks 
            if task.due_date and task.due_date < now and task.status != TaskStatus.COMPLETED
        ]
    
    def get_documents_by_type(self, doc_type: DocumentType) -> List[Document]:
        """Get documents by type"""
        return [doc for doc in self.documents if doc.document_type == doc_type]
    
    def calculate_completion_percentage(self) -> float:
        """Calculate deal completion percentage based on tasks"""
        if not self.tasks:
            return 0.0
        
        completed_tasks = len([task for task in self.tasks if task.status == TaskStatus.COMPLETED])
        return completed_tasks / len(self.tasks)
    
    def days_until_closing(self) -> Optional[int]:
        """Calculate days until target closing"""
        if not self.target_closing_date:
            return None
        
        delta = self.target_closing_date - datetime.now()
        return delta.days
    
    def add_task(self, task: Task) -> None:
        """Add a task to the deal"""
        self.tasks.append(task)
    
    def add_document(self, document: Document) -> None:
        """Add a document to the deal"""
        self.documents.append(document)
    
    def update_stage(self, new_stage: DealStage) -> None:
        """Update the deal stage"""
        self.current_stage = new_stage
        
        # Update probability based on stage
        stage_probabilities = {
            DealStage.LEAD: 0.1,
            DealStage.QUALIFIED: 0.25,
            DealStage.PROPOSAL: 0.4,
            DealStage.NEGOTIATION: 0.6,
            DealStage.DUE_DILIGENCE: 0.75,
            DealStage.FINANCING: 0.85,
            DealStage.CLOSING: 0.95,
            DealStage.CLOSED: 1.0,
            DealStage.LOST: 0.0
        }
        
        self.probability = stage_probabilities.get(new_stage, self.probability) 