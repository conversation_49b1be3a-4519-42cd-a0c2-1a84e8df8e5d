"""
Energy rates model for utility cost analysis
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field


class EnergyRates(BaseModel):
    """Energy and utility rates for a geographic region"""
    
    # Electricity rates
    electricity_rate: float = Field(..., description="Electricity rate per kWh in USD")
    electricity_demand_charge: Optional[float] = Field(None, description="Demand charge per kW")
    electricity_connection_fee: Optional[float] = Field(None, description="Connection/hookup fee")
    
    # Natural gas rates
    gas_rate: Optional[float] = Field(None, description="Natural gas rate per therm in USD")
    gas_connection_fee: Optional[float] = Field(None, description="Gas connection fee")
    gas_availability: Optional[bool] = Field(None, description="Natural gas service available")
    
    # Water and sewer
    water_rate: Optional[float] = Field(None, description="Water rate per gallon in USD")
    sewer_rate: Optional[float] = Field(None, description="Sewer rate per gallon in USD")
    water_connection_fee: Optional[float] = Field(None, description="Water connection fee")
    sewer_connection_fee: Optional[float] = Field(None, description="Sewer connection fee")
    
    # Rate tiers and structures
    tiered_electricity: Optional[bool] = Field(None, description="Uses tiered electricity pricing")
    time_of_use: Optional[bool] = Field(None, description="Time-of-use pricing available")
    peak_rate: Optional[float] = Field(None, description="Peak hour electricity rate")
    off_peak_rate: Optional[float] = Field(None, description="Off-peak electricity rate")
    
    # Commercial/industrial specific
    industrial_rate_available: Optional[bool] = Field(None, description="Industrial rates available")
    industrial_electricity_rate: Optional[float] = Field(None, description="Industrial electricity rate")
    minimum_monthly_charge: Optional[float] = Field(None, description="Minimum monthly utility charge")
    
    # Renewable energy
    renewable_portfolio: Optional[float] = Field(None, description="Renewable energy percentage")
    solar_net_metering: Optional[bool] = Field(None, description="Solar net metering available")
    green_energy_premium: Optional[float] = Field(None, description="Green energy rate premium")
    
    # Geographic and provider info
    utility_provider: Optional[str] = Field(None, description="Primary utility provider")
    service_territory: Optional[str] = Field(None, description="Service territory name")
    region: str = Field(..., description="Geographic region")
    
    # Rate metadata
    rate_schedule: Optional[str] = Field(None, description="Rate schedule identifier")
    effective_date: Optional[str] = Field(None, description="Rate effective date")
    data_source: Optional[str] = Field(None, description="Source of rate data")
    
    # Additional utility costs
    additional_fees: Dict[str, float] = Field(default_factory=dict, description="Additional utility fees")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional energy data")
    
    def to_dict(self) -> dict:
        """Convert to dictionary format"""
        return self.model_dump()
    
    @classmethod
    def from_dict(cls, data: dict) -> "EnergyRates":
        """Create from dictionary format"""
        return cls(**data)
    
    def monthly_electricity_cost(self, kwh_usage: float) -> float:
        """Calculate monthly electricity cost for given usage"""
        base_cost = kwh_usage * self.electricity_rate
        if self.minimum_monthly_charge:
            return max(base_cost, self.minimum_monthly_charge)
        return base_cost
    
    def annual_electricity_cost(self, annual_kwh: float) -> float:
        """Calculate annual electricity cost"""
        return annual_kwh * self.electricity_rate
    
    def total_connection_fees(self) -> float:
        """Calculate total utility connection fees"""
        total = 0.0
        if self.electricity_connection_fee:
            total += self.electricity_connection_fee
        if self.gas_connection_fee:
            total += self.gas_connection_fee
        if self.water_connection_fee:
            total += self.water_connection_fee
        if self.sewer_connection_fee:
            total += self.sewer_connection_fee
        return total 