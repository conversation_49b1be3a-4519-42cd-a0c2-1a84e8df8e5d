"""
Labor metrics model for workforce analysis
"""

from typing import Optional, Dict, Any
from pydantic import BaseModel, Field


class LaborMetrics(BaseModel):
    """Labor market metrics for a geographic area"""
    
    # Core labor statistics
    median_wage: float = Field(..., description="Median hourly wage in USD")
    unemployment_rate: float = Field(..., description="Unemployment rate as decimal (0.05 = 5%)")
    labor_force_count: int = Field(..., description="Total labor force in the area")
    
    # Employment by sector
    manufacturing_employment: Optional[int] = Field(None, description="Manufacturing jobs count")
    warehouse_employment: Optional[int] = Field(None, description="Warehouse/logistics jobs count")
    construction_employment: Optional[int] = Field(None, description="Construction jobs count")
    
    # Wage ranges by skill level
    entry_level_wage: Optional[float] = Field(None, description="Entry level hourly wage")
    skilled_wage: Optional[float] = Field(None, description="Skilled worker hourly wage")
    management_wage: Optional[float] = Field(None, description="Management hourly wage")
    
    # Labor availability
    available_workforce: Optional[int] = Field(None, description="Estimated available workers")
    seasonal_variation: Optional[float] = Field(None, description="Seasonal employment variation")
    
    # Education and skills
    high_school_rate: Optional[float] = Field(None, description="High school completion rate")
    college_rate: Optional[float] = Field(None, description="College completion rate")
    technical_training: Optional[float] = Field(None, description="Technical/vocational training rate")
    
    # Union presence
    union_penetration: Optional[float] = Field(None, description="Union membership rate")
    prevailing_wage: Optional[float] = Field(None, description="Prevailing wage for area")
    
    # Geographic identifier
    geographic_level: str = Field(..., description="Geographic level (county, msa, state)")
    geographic_name: str = Field(..., description="Geographic area name")
    
    # Data metadata
    data_source: Optional[str] = Field(None, description="Source of labor data")
    data_date: Optional[str] = Field(None, description="Date of data collection")
    
    # Additional metrics
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional labor metrics")
    
    def to_dict(self) -> dict:
        """Convert to dictionary format"""
        return self.model_dump()
    
    @classmethod
    def from_dict(cls, data: dict) -> "LaborMetrics":
        """Create from dictionary format"""
        return cls(**data)
    
    def annual_cost_per_worker(self, hours_per_year: int = 2080) -> float:
        """Calculate annual cost per worker based on median wage"""
        return self.median_wage * hours_per_year
    
    def total_annual_cost(self, worker_count: int, hours_per_year: int = 2080) -> float:
        """Calculate total annual labor cost for given worker count"""
        return self.annual_cost_per_worker(hours_per_year) * worker_count 