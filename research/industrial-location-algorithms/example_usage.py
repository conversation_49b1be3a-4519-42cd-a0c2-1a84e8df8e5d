"""
🏭 Industrial Site Selection System - Example Usage

This file demonstrates how to use the industrial site selection system
for both prospecting and underwriting analysis.
"""

import json
from modules.prospecting.parcel_prospecting import run_prospecting_pipeline
from modules.underwriting.capex import calculate_capex
from modules.underwriting.opex import calculate_opex
from modules.underwriting.revenue_projection import calculate_revenue_projection
from modules.underwriting.financial_analysis import run_financial_analysis
from modules.underwriting.stress_tests import run_stress_tests

def main():
    print("🏭 Industrial Site Selection System - Example Usage")
    print("=" * 60)
    
    # ===== PART 1: PROSPECTING ANALYSIS =====
    print("\n📍 PART 1: PROSPECTING ANALYSIS")
    print("-" * 40)
    
    # Define search parameters
    search_area = {
        "type": "city",
        "value": "Dallas, TX"
    }
    
    parcel_filters = {
        "min_area_sqft": 50000,
        "max_area_sqft": 200000,
        "zoning": "industrial",
        "max_distance_to_port_miles": 100
    }
    
    user_constraints = {
        "workers": 100,
        "annual_kwh": 500000,
        "port_hub": "Houston, TX"
    }
    
    # Run prospecting analysis
    print("\nSearching for industrial parcels...")
    parcels = run_prospecting_pipeline(
        search_area=search_area,
        parcel_filters=parcel_filters,
        user_constraints=user_constraints,
        max_candidates=10
    )
    
    print(f"\nFound {len(parcels)} suitable parcels")
    
    # Display top 3 parcels
    for i, parcel in enumerate(parcels[:3]):
        print(f"\n#{i+1} Parcel ID: {parcel['parcel_id']}")
        print(f"   Total Cost: ${parcel['total_cost']:,.0f}")
        print(f"   Area: {parcel['area_sqft']:,.0f} sqft")
        print(f"   Cost Breakdown:")
        metadata = parcel['metadata']
        print(f"     - Labor: ${metadata['labor_cost']:,.0f}")
        print(f"     - Utilities: ${metadata['utility_cost']:,.0f}")
        print(f"     - Transportation: ${metadata['transportation_cost']:,.0f}")
        print(f"     - Land: ${metadata['land_cost']:,.0f}")
    
    # Select best parcel for underwriting
    best_parcel = parcels[0]
    
    # ===== PART 2: UNDERWRITING ANALYSIS =====
    print("\n\n💰 PART 2: UNDERWRITING ANALYSIS")
    print("-" * 40)
    print(f"Analyzing Parcel: {best_parcel['parcel_id']}")
    
    # Calculate CapEx
    print("\n1. Capital Expenditure Analysis:")
    capex_result = calculate_capex(
        building_area_sqft=best_parcel['area_sqft'],
        construction_cost_per_sqft=120.00,
        equipment_cost=2500000,
        land_cost=best_parcel['metadata']['land_cost'],
        down_payment_pct=0.25,
        mortgage_rate=0.055,
        amortization_years=25
    )
    print(f"   Total Project Cost: ${capex_result['total_project_cost']:,.0f}")
    print(f"   Mortgage Amount: ${capex_result['mortgage_amount']:,.0f}")
    print(f"   Equity Investment: ${capex_result['equity_investment']:,.0f}")
    
    # Calculate OpEx
    print("\n2. Operating Expense Analysis:")
    opex_result = calculate_opex(
        building_area_sqft=best_parcel['area_sqft'],
        property_tax_rate=0.015,
        insurance_rate_per_sqft=1.20,
        maintenance_pct=0.02,
        utilities_annual=best_parcel['metadata']['utility_cost'],
        labor_annual=best_parcel['metadata']['labor_cost'],
        management_fee_pct=0.05
    )
    print(f"   Total Annual OpEx: ${opex_result['total_opex']:,.0f}")
    
    # Calculate Revenue
    print("\n3. Revenue Projection:")
    revenue_result = calculate_revenue_projection(
        building_area_sqft=best_parcel['area_sqft'],
        starting_rent_per_sqft=12.00,
        occupancy_rate=0.95,
        escalation_rate=0.025,
        projection_years=10
    )
    print(f"   Year 1 Revenue: ${revenue_result['annual_revenue'][0]:,.0f}")
    print(f"   Year 10 Revenue: ${revenue_result['annual_revenue'][9]:,.0f}")
    
    # Run Financial Analysis
    print("\n4. Financial Analysis:")
    analysis_params = {
        "discount_rate": 0.10,
        "sale_cap_rate": 0.07,
        "sale_year": 10
    }
    
    financial_result = run_financial_analysis(
        capex=capex_result,
        opex=opex_result,
        revenue=revenue_result,
        analysis_params=analysis_params
    )
    
    print(f"   IRR: {financial_result['returns']['irr']:.1%}")
    print(f"   NPV: ${financial_result['returns']['npv']:,.0f}")
    print(f"   Cash-on-Cash Return: {financial_result['returns']['cash_on_cash']:.1%}")
    print(f"   Min DSCR: {financial_result['ratios']['min_dscr']:.2f}")
    print(f"   Performance Rating: {financial_result['performance_rating']}")
    
    # Run Stress Tests
    print("\n5. Stress Testing:")
    stress_params = {
        "rate_shock": {"delta_rate": 0.01},
        "rent_downturn": {"down_pct": 0.10},
        "occupancy_drop": {"drop_pct": 0.10}
    }
    
    # Combine inputs for stress testing
    base_inputs = {
        **capex_result,
        **opex_result,
        **revenue_result,
        "building_area_sqft": best_parcel['area_sqft'],
        "starting_rent_per_sqft": 12.00,
        "occupancy_rate": 0.95
    }
    
    stress_results = run_stress_tests(base_inputs, stress_params)
    
    print(f"   Base Case IRR: {stress_results['analysis']['base_case']['irr']:.1%}")
    print(f"   Rate Shock IRR: {stress_results['analysis']['scenarios']['rate_shock']['irr']:.1%}")
    print(f"   Rent Downturn IRR: {stress_results['analysis']['scenarios']['rent_downturn']['irr']:.1%}")
    print(f"   Resilience Rating: {stress_results['analysis']['summary_metrics']['resilience_rating']}")
    
    # ===== PART 3: SAVE RESULTS =====
    print("\n\n💾 PART 3: SAVING RESULTS")
    print("-" * 40)
    
    # Combine all results
    complete_analysis = {
        "parcel": best_parcel,
        "financial_analysis": financial_result,
        "stress_tests": stress_results['analysis'],
        "recommendations": stress_results['recommendations']
    }
    
    # Save to file
    with open("analysis_results.json", "w") as f:
        json.dump(complete_analysis, f, indent=2)
    
    print("Results saved to analysis_results.json")
    
    # Print recommendations
    print("\n📋 RECOMMENDATIONS:")
    for i, rec in enumerate(stress_results['recommendations'][:5]):
        print(f"{i+1}. {rec}")

if __name__ == "__main__":
    main() 