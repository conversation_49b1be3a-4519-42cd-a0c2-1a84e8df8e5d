"""
Energy Information Administration API client
"""

import requests
from typing import Optional, Dict, Any
import time
from models.energy import EnergyRates


class EIAClient:
    """Client for EIA energy data API"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.eia.gov/v2"):
        self.api_key = api_key
        self.base_url = base_url
        self.session = requests.Session()
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0
    
    def _rate_limit(self):
        """Implement rate limiting"""
        elapsed = time.time() - self.last_request_time
        if elapsed < self.min_request_interval:
            time.sleep(self.min_request_interval - elapsed)
        self.last_request_time = time.time()
    
    def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Make a rate-limited API request"""
        self._rate_limit()
        
        url = f"{self.base_url}/{endpoint}"
        
        if params is None:
            params = {}
        params["api_key"] = self.api_key
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"EIA API request failed: {e}")
    
    def fetch_energy_rates(self, region: str) -> EnergyRates:
        """Fetch energy rates for a region"""
        try:
            # Get electricity rates
            electricity_data = self._get_electricity_rates(region)
            
            # Get natural gas rates 
            gas_data = self._get_gas_rates(region)
            
            return EnergyRates(
                electricity_rate=electricity_data.get("rate", 0.12),
                electricity_demand_charge=electricity_data.get("demand_charge"),
                gas_rate=gas_data.get("rate"),
                gas_availability=gas_data.get("available", True),
                industrial_rate_available=True,
                industrial_electricity_rate=electricity_data.get("industrial_rate"),
                utility_provider=electricity_data.get("provider", "Local Utility"),
                region=region,
                data_source="EIA",
                metadata={
                    "electricity_data": electricity_data,
                    "gas_data": gas_data
                }
            )
        except Exception as e:
            # Return default rates if API fails
            return EnergyRates(
                electricity_rate=0.12,
                gas_rate=1.20,
                region=region,
                data_source="EIA (default)",
                metadata={"error": str(e)}
            )
    
    def _get_electricity_rates(self, region: str) -> Dict[str, Any]:
        """Get electricity rates for region"""
        try:
            # Map region to EIA series codes
            state_code = self._get_state_code(region)
            
            # Typical series for average retail price
            series_id = f"ELEC.PRICE.{state_code}-ALL.M"
            
            params = {
                "frequency": "monthly",
                "data[0]": series_id,
                "start": "2023-01",
                "end": "2023-12",
                "sort[0][column]": "period",
                "sort[0][direction]": "desc"
            }
            
            data = self._make_request("electricity/retail-sales", params)
            
            # Parse latest rate
            if data.get("response", {}).get("data"):
                latest_rate = data["response"]["data"][0].get("price", 12.0) / 100  # cents to dollars
            else:
                latest_rate = 0.12  # Default rate
            
            return {
                "rate": latest_rate,
                "industrial_rate": latest_rate * 0.8,  # Industrial typically lower
                "demand_charge": 15.0,  # Typical demand charge
                "provider": f"{region} Electric",
                "series_id": series_id
            }
        except Exception as e:
            return {
                "rate": 0.12,
                "industrial_rate": 0.096,
                "demand_charge": 15.0,
                "provider": f"{region} Electric"
            }
    
    def _get_gas_rates(self, region: str) -> Dict[str, Any]:
        """Get natural gas rates for region"""
        try:
            state_code = self._get_state_code(region)
            
            # Natural gas price series
            series_id = f"NG.N3035{state_code}3.M"
            
            params = {
                "frequency": "monthly", 
                "data[0]": series_id,
                "start": "2023-01",
                "end": "2023-12",
                "sort[0][column]": "period",
                "sort[0][direction]": "desc"
            }
            
            data = self._make_request("natural-gas/pri/sum", params)
            
            # Parse latest rate
            if data.get("response", {}).get("data"):
                latest_rate = data["response"]["data"][0].get("price", 10.0)
            else:
                latest_rate = 10.0
            
            return {
                "rate": latest_rate,
                "available": True,
                "series_id": series_id
            }
        except Exception as e:
            return {
                "rate": 10.0,
                "available": True
            }
    
    def _get_state_code(self, region: str) -> str:
        """Map region to EIA state code"""
        state_mapping = {
            "Texas": "TX",
            "TX": "TX",
            "California": "CA", 
            "CA": "CA",
            "New York": "NY",
            "NY": "NY",
            "Florida": "FL",
            "FL": "FL"
        }
        
        # Extract state from region string
        for state_name, code in state_mapping.items():
            if state_name.lower() in region.lower():
                return code
        
        return "TX"  # Default to Texas
    
    def get_renewable_portfolio(self, state: str) -> Dict[str, Any]:
        """Get renewable energy portfolio for state"""
        try:
            params = {
                "frequency": "annual",
                "data[0]": f"ELEC.GEN.{state}-99.A",
                "start": "2023",
                "end": "2023"
            }
            
            data = self._make_request("electricity/electric-power-operational-data", params)
            
            # Calculate renewable percentage (simplified)
            renewable_pct = 0.25  # Default assumption
            
            return {
                "renewable_percentage": renewable_pct,
                "solar_available": True,
                "wind_available": True
            }
        except Exception as e:
            return {
                "renewable_percentage": 0.25,
                "solar_available": True,
                "wind_available": True
            } 