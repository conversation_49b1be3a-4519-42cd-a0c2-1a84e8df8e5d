"""
Document management for deal closure
"""

from typing import List
from models.deal import Deal, Document, DocumentType, DealStage
from datetime import datetime


def generate_docs(deal: Deal) -> List[Document]:
    """Generate required documents for deal stage"""
    
    existing_docs = deal.documents.copy()
    required_docs = _get_required_documents_for_stage(deal.current_stage)
    
    for doc_name, doc_type in required_docs:
        if not any(doc.name == doc_name for doc in existing_docs):
            new_doc = Document(
                id=f"doc_{len(existing_docs) + 1}",
                name=doc_name,
                document_type=doc_type,
                created_date=datetime.now()
            )
            existing_docs.append(new_doc)
    
    # Update deal with new documents
    deal.documents = existing_docs
    
    return existing_docs


def _get_required_documents_for_stage(stage: DealStage) -> List[tuple]:
    """Get required documents for each deal stage"""
    
    document_templates = {
        DealStage.PROPOSAL: [
            ("Letter of Intent", DocumentType.LEGAL),
            ("Property Information Package", DocumentType.TECHNICAL)
        ],
        DealStage.NEGOTIATION: [
            ("Purchase Agreement Draft", DocumentType.LEGAL),
            ("Terms Sheet", DocumentType.LEGAL)
        ],
        DealStage.DUE_DILIGENCE: [
            ("Environmental Report", DocumentType.REGULATORY),
            ("Title Report", DocumentType.LEGAL),
            ("Survey", DocumentType.TECHNICAL),
            ("Zoning Letter", DocumentType.REGULATORY),
            ("Inspection Report", DocumentType.TECHNICAL)
        ],
        DealStage.FINANCING: [
            ("Loan Application", DocumentType.FINANCIAL),
            ("Appraisal Report", DocumentType.FINANCIAL),
            ("Loan Commitment Letter", DocumentType.FINANCIAL),
            ("Loan Documents", DocumentType.LEGAL)
        ],
        DealStage.CLOSING: [
            ("Final Purchase Agreement", DocumentType.LEGAL),
            ("Deed", DocumentType.LEGAL),
            ("Title Insurance Policy", DocumentType.LEGAL),
            ("Closing Statement", DocumentType.FINANCIAL),
            ("Wire Instructions", DocumentType.FINANCIAL)
        ]
    }
    
    return document_templates.get(stage, [])