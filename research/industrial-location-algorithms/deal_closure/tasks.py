"""
Task management for deal closure
"""

from typing import List
from models.deal import Deal, Task, TaskStatus, DealStage
from datetime import datetime, timedelta


def sync_tasks(deal: Deal) -> List[Task]:
    """Synchronize and manage deal tasks"""
    
    # Get existing tasks
    existing_tasks = deal.tasks.copy()
    
    # Add stage-specific tasks if not present
    required_tasks = _get_required_tasks_for_stage(deal.current_stage)
    
    for task_name in required_tasks:
        if not any(task.name == task_name for task in existing_tasks):
            new_task = Task(
                id=f"task_{len(existing_tasks) + 1}",
                name=task_name,
                status=TaskStatus.PENDING,
                due_date=datetime.now() + timedelta(days=14)
            )
            existing_tasks.append(new_task)
    
    # Update deal with new tasks
    deal.tasks = existing_tasks
    
    return existing_tasks


def _get_required_tasks_for_stage(stage: DealStage) -> List[str]:
    """Get required tasks for each deal stage"""
    
    task_templates = {
        DealStage.LEAD: [
            "Initial Contact",
            "Property Tour"
        ],
        DealStage.QUALIFIED: [
            "Financial Qualification",
            "Initial Underwriting"
        ],
        DealStage.PROPOSAL: [
            "Prepare Letter of Intent",
            "Market Analysis"
        ],
        DealStage.NEGOTIATION: [
            "Price Negotiation",
            "Terms Negotiation"
        ],
        DealStage.DUE_DILIGENCE: [
            "Phase I Environmental Assessment",
            "Title Review",
            "Survey Review",
            "Zoning Verification",
            "Physical Inspection"
        ],
        DealStage.FINANCING: [
            "Loan Application",
            "Appraisal",
            "Loan Approval",
            "Loan Documents"
        ],
        DealStage.CLOSING: [
            "Final Walkthrough",
            "Closing Documents Review",
            "Wire Transfer Setup",
            "Title Insurance",
            "Recording"
        ]
    }
    
    return task_templates.get(stage, []) 