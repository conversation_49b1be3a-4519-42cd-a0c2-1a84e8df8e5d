"""
Compliance verification for deal closure
"""

from typing import Dict, Any, List
from models.deal import Deal, DealStage


def verify_compliance(deal: Deal) -> Dict[str, Any]:
    """Verify deal compliance with regulations and requirements"""
    
    compliance_checks = []
    issues = []
    
    # Stage-specific compliance checks
    if deal.current_stage in [DealStage.DUE_DILIGENCE, DealStage.FINANCING, DealStage.CLOSING]:
        
        # Environmental compliance
        env_check = _check_environmental_compliance(deal)
        compliance_checks.append(env_check)
        if not env_check["passed"]:
            issues.extend(env_check["issues"])
        
        # Title compliance
        title_check = _check_title_compliance(deal)
        compliance_checks.append(title_check)
        if not title_check["passed"]:
            issues.extend(title_check["issues"])
        
        # Zoning compliance
        zoning_check = _check_zoning_compliance(deal)
        compliance_checks.append(zoning_check)
        if not zoning_check["passed"]:
            issues.extend(zoning_check["issues"])
    
    if deal.current_stage in [DealStage.FINANCING, DealStage.CLOSING]:
        
        # Financial compliance
        financial_check = _check_financial_compliance(deal)
        compliance_checks.append(financial_check)
        if not financial_check["passed"]:
            issues.extend(financial_check["issues"])
    
    if deal.current_stage == DealStage.CLOSING:
        
        # Closing compliance
        closing_check = _check_closing_compliance(deal)
        compliance_checks.append(closing_check)
        if not closing_check["passed"]:
            issues.extend(closing_check["issues"])
    
    # Overall status
    all_passed = all(check["passed"] for check in compliance_checks)
    status = "compliant" if all_passed else "non_compliant"
    
    return {
        "status": status,
        "checks_performed": len(compliance_checks),
        "checks_passed": sum(1 for check in compliance_checks if check["passed"]),
        "issues": issues,
        "compliance_checks": compliance_checks
    }


def _check_environmental_compliance(deal: Deal) -> Dict[str, Any]:
    """Check environmental compliance"""
    
    # Look for environmental assessment document
    env_docs = [doc for doc in deal.documents 
                if "environmental" in doc.name.lower()]
    
    # Look for environmental assessment task
    env_tasks = [task for task in deal.tasks 
                 if "environmental" in task.name.lower()]
    
    passed = bool(env_docs) or bool(env_tasks and 
                                   any(task.status.value == "completed" for task in env_tasks))
    
    issues = [] if passed else ["Phase I Environmental Assessment required"]
    
    return {
        "check_type": "environmental",
        "passed": passed,
        "issues": issues
    }


def _check_title_compliance(deal: Deal) -> Dict[str, Any]:
    """Check title compliance"""
    
    # Look for title documents
    title_docs = [doc for doc in deal.documents 
                  if "title" in doc.name.lower()]
    
    # Look for title tasks
    title_tasks = [task for task in deal.tasks 
                   if "title" in task.name.lower()]
    
    passed = bool(title_docs) or bool(title_tasks and 
                                     any(task.status.value == "completed" for task in title_tasks))
    
    issues = [] if passed else ["Title review and insurance required"]
    
    return {
        "check_type": "title",
        "passed": passed,
        "issues": issues
    }


def _check_zoning_compliance(deal: Deal) -> Dict[str, Any]:
    """Check zoning compliance"""
    
    # Look for zoning documents
    zoning_docs = [doc for doc in deal.documents 
                   if "zoning" in doc.name.lower()]
    
    # Look for zoning tasks
    zoning_tasks = [task for task in deal.tasks 
                    if "zoning" in task.name.lower()]
    
    passed = bool(zoning_docs) or bool(zoning_tasks and 
                                      any(task.status.value == "completed" for task in zoning_tasks))
    
    issues = [] if passed else ["Zoning verification required"]
    
    return {
        "check_type": "zoning",
        "passed": passed,
        "issues": issues
    }


def _check_financial_compliance(deal: Deal) -> Dict[str, Any]:
    """Check financial compliance"""
    
    issues = []
    
    # Check for required financial elements
    if not deal.purchase_price:
        issues.append("Purchase price not specified")
    
    if not deal.financing_amount:
        # If no financing amount, assume cash deal - this is okay
        pass
    
    # Look for loan documents if financing
    if deal.financing_amount:
        loan_docs = [doc for doc in deal.documents 
                     if "loan" in doc.name.lower() or "appraisal" in doc.name.lower()]
        
        if not loan_docs:
            issues.append("Loan documentation required")
    
    passed = len(issues) == 0
    
    return {
        "check_type": "financial",
        "passed": passed,
        "issues": issues
    }


def _check_closing_compliance(deal: Deal) -> Dict[str, Any]:
    """Check closing compliance"""
    
    issues = []
    
    # Required closing documents
    required_docs = ["deed", "closing statement", "title insurance"]
    
    for req_doc in required_docs:
        found = any(req_doc in doc.name.lower() for doc in deal.documents)
        if not found:
            issues.append(f"{req_doc.title()} document required")
    
    # All tasks should be complete
    incomplete_tasks = [task for task in deal.tasks 
                       if task.status.value != "completed"]
    
    if incomplete_tasks:
        issues.append(f"{len(incomplete_tasks)} tasks still incomplete")
    
    passed = len(issues) == 0
    
    return {
        "check_type": "closing",
        "passed": passed,
        "issues": issues
    } 