"""
Negotiate listing functions for exclusive opportunities
"""

from models.parcel import Parcel
from datetime import datetime
from typing import Dict, Any


def flag_exclusive(parcel: Parcel) -> Parcel:
    """Flag parcel for exclusive listing if criteria are met"""
    
    # Create updated parcel with exclusive flag
    parcel_dict = parcel.to_dict()
    parcel_dict["exclusive_flag"] = True
    parcel_dict["exclusive_timestamp"] = datetime.now()
    
    return Parcel.from_dict(parcel_dict)


def evaluate_exclusivity_criteria(parcel: Parcel) -> Dict[str, Any]:
    """Evaluate if parcel meets criteria for exclusive listing"""
    
    score = 0
    criteria_met = []
    criteria_failed = []
    
    # Size criteria - large parcels get priority
    if parcel.lot_size and parcel.lot_size > 150000:  # 150k+ sqft
        score += 25
        criteria_met.append("Large parcel size (>150k sqft)")
    else:
        criteria_failed.append("Parcel size below threshold")
    
    # Location criteria - industrial zoning preferred
    if parcel.zoning_code and "I" in parcel.zoning_code:
        score += 20
        criteria_met.append("Industrial zoning")
    else:
        criteria_failed.append("Non-industrial zoning")
    
    # Value criteria - high-value properties
    if parcel.assessed_value and parcel.assessed_value > 750000:
        score += 20
        criteria_met.append("High assessed value")
    else:
        criteria_failed.append("Lower assessed value")
    
    # Owner type criteria - business owners more likely to transact
    if parcel.owner_contact and parcel.owner_contact.get("type") == "business":
        score += 15
        criteria_met.append("Business owner")
    else:
        criteria_failed.append("Individual owner or unknown")
    
    # Market criteria - prime markets
    if parcel.city in ["Dallas", "Houston", "Austin", "San Antonio"]:
        score += 20
        criteria_met.append("Prime market location")
    else:
        criteria_failed.append("Secondary market")
    
    # Determine exclusivity recommendation
    if score >= 70:
        recommendation = "Highly recommended for exclusive listing"
        priority = "high"
    elif score >= 50:
        recommendation = "Consider for exclusive listing"
        priority = "medium"
    else:
        recommendation = "Standard listing approach"
        priority = "low"
    
    return {
        "exclusivity_score": score,
        "criteria_met": criteria_met,
        "criteria_failed": criteria_failed,
        "recommendation": recommendation,
        "priority": priority,
        "should_flag": score >= 70
    }


def generate_exclusive_listing_strategy(parcel: Parcel) -> Dict[str, Any]:
    """Generate strategy for exclusive listing approach"""
    
    evaluation = evaluate_exclusivity_criteria(parcel)
    
    if not evaluation["should_flag"]:
        return {
            "strategy": "standard_listing",
            "approach": "Market through standard channels",
            "timeline": "Standard 60-90 day marketing period",
            "resources": "Standard marketing resources"
        }
    
    # High-priority exclusive strategy
    strategy = {
        "strategy": "exclusive_priority",
        "approach": "Direct outreach with exclusive opportunity presentation",
        "timeline": "15-30 day exclusive period before broader marketing",
        "resources": "Dedicated relationship manager, custom marketing materials",
        "target_buyers": _identify_target_buyers(parcel),
        "marketing_materials": _plan_marketing_materials(parcel),
        "outreach_sequence": _plan_outreach_sequence(parcel)
    }
    
    return strategy


def _identify_target_buyers(parcel: Parcel) -> list:
    """Identify target buyer segments for exclusive outreach"""
    
    targets = []
    
    # Industrial developers
    if parcel.lot_size and parcel.lot_size > 200000:
        targets.append("Large-scale industrial developers")
    
    # Logistics companies
    if parcel.zoning_code and "I" in parcel.zoning_code:
        targets.append("Logistics and distribution companies")
        targets.append("E-commerce fulfillment operators")
    
    # Manufacturing companies
    if parcel.city in ["Dallas", "Houston"]:
        targets.append("Manufacturing companies seeking expansion")
    
    # Investment funds
    if parcel.assessed_value and parcel.assessed_value > 1000000:
        targets.append("Industrial real estate investment funds")
        targets.append("Institutional investors")
    
    # Owner-users
    targets.append("Owner-user industrial companies")
    
    return targets


def _plan_marketing_materials(parcel: Parcel) -> Dict[str, Any]:
    """Plan custom marketing materials for exclusive listing"""
    
    return {
        "executive_summary": "High-level investment opportunity overview",
        "detailed_analysis": "Comprehensive market and financial analysis",
        "site_plan": "Professional site plan and development potential",
        "market_research": "Local market conditions and comparables",
        "financial_projections": "Investment return projections",
        "virtual_tour": "3D virtual site tour and aerial photography",
        "exclusive_book": "Bound presentation book for key prospects"
    }


def _plan_outreach_sequence(parcel: Parcel) -> list:
    """Plan the outreach sequence for exclusive period"""
    
    sequence = [
        {
            "day": 1,
            "action": "Initial contact with top 3 prospects",
            "method": "Personal phone call + email",
            "materials": "Executive summary"
        },
        {
            "day": 3,
            "action": "Follow-up with detailed materials",
            "method": "Courier delivery",
            "materials": "Exclusive presentation book"
        },
        {
            "day": 7,
            "action": "Schedule site visits",
            "method": "Coordinated site tours",
            "materials": "Site plan and virtual tour"
        },
        {
            "day": 14,
            "action": "First offer deadline",
            "method": "Phone + email reminder",
            "materials": "Market feedback summary"
        },
        {
            "day": 21,
            "action": "Expand outreach to secondary prospects",
            "method": "Targeted email campaign",
            "materials": "Updated executive summary"
        },
        {
            "day": 30,
            "action": "Transition to open marketing if needed",
            "method": "Broader market exposure",
            "materials": "Full marketing campaign"
        }
    ]
    
    return sequence


def track_exclusive_performance(parcel: Parcel) -> Dict[str, Any]:
    """Track performance metrics for exclusive listings"""
    
    # In a real system, this would pull from CRM/tracking system
    return {
        "days_since_exclusive": 5,  # Mock data
        "prospects_contacted": 8,
        "site_visits_scheduled": 3,
        "offers_received": 1,
        "highest_offer": parcel.assessed_value * 1.15 if parcel.assessed_value else None,
        "conversion_probability": 0.75,
        "estimated_close_date": "2024-02-15"
    } 