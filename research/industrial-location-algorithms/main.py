#!/usr/bin/env python3
"""
Industrial Discovery Inc. - Main Application

Command-line interface for industrial site selection and deal management.
"""

import click
import yaml
from typing import Dict, Any
from pathlib import Path

from utils.config import load_config
from utils.logging import setup_logging
from services.prospecting_service import ProspectingService
from services.underwriting_service import UnderwritingService
from services.deal_closure_service import DealClosureService


@click.group()
@click.option('--config', '-c', type=click.Path(exists=True), 
              help='Path to configuration file')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.pass_context
def cli(ctx, config, verbose):
    """Industrial Discovery Inc. - Site Selection Platform"""
    
    # Ensure context object exists
    ctx.ensure_object(dict)
    
    # Load configuration
    if config:
        ctx.obj['config'] = load_config(config)
    else:
        # Try to load default config
        default_config = Path("config.yaml")
        if default_config.exists():
            ctx.obj['config'] = load_config(str(default_config))
        else:
            ctx.obj['config'] = {}
    
    # Setup logging
    setup_logging(verbose)


@cli.command()
@click.option('--latitude', type=float, required=True,
              help='Latitude for site analysis')
@click.option('--longitude', type=float, required=True,
              help='Longitude for site analysis')
@click.option('--radius', type=float, default=5.0,
              help='Search radius in miles')
@click.option('--industry', default="manufacturing",
              help='Target industry type')
@click.option('--output', '-o', type=click.Path(),
              help='Save results to file')
@click.pass_context
def prospect(ctx, latitude, longitude, radius, industry, output):
    """Analyze industrial sites with monetized value calculations"""
    
    config = ctx.obj.get('config', {})
    
    click.echo(f"🏭 Analyzing industrial sites near {latitude}, {longitude}")
    click.echo(f"Industry: {industry} | Radius: {radius} miles")
    
    # Initialize service
    prospecting_service = ProspectingService(config)
    
    try:
        # Run analysis
        with click.progressbar(length=100, label="Analyzing site potential") as bar:
            bar.update(20)
            result = prospecting_service.run_prospecting(
                latitude=latitude,
                longitude=longitude,
                radius_miles=radius,
                target_industry=industry
            )
            bar.update(80)
        
        # Display monetized results
        click.echo("\n" + "="*60)
        click.echo("💰 MONETIZED INVESTMENT ANALYSIS")
        click.echo("="*60)
        
        click.echo(f"\n📍 Property: {result.parcel.address or 'Address not available'}")
        click.echo(f"🏗️  Size: {result.parcel.lot_size:,} sqft")
        
        click.echo(f"\n💵 FINANCIAL OVERVIEW:")
        click.echo(f"   Market Value:        ${result.estimated_market_value:,.0f}")
        click.echo(f"   Development Cost:    ${result.development_cost_estimate:,.0f}")
        click.echo(f"   Total Investment:    ${result.acquisition_cost:,.0f}")
        click.echo(f"   Risk-Adjusted Value: ${result.risk_adjusted_value:,.0f}")
        
        click.echo(f"\n📈 REVENUE POTENTIAL:")
        click.echo(f"   Annual Rental Income: ${result.potential_rental_income:,.0f}")
        click.echo(f"   5-Year Sale Value:    ${result.sale_value_estimate:,.0f}")
        
        click.echo(f"\n💰 VALUE DRIVERS:")
        click.echo(f"   Location Premium:     ${result.location_value_premium:,.0f}")
        click.echo(f"   Transport Savings:    ${result.transportation_cost_savings:,.0f}")
        click.echo(f"   Labor Advantage:      ${result.labor_cost_advantage:,.0f}")
        click.echo(f"   Energy Savings:       ${result.energy_cost_savings:,.0f}")
        click.echo(f"   Tax Incentives:       ${result.incentive_value:,.0f}")
        
        click.echo(f"\n📊 INVESTMENT METRICS:")
        click.echo(f"   ROI:                  {result.estimated_roi_percent:.1f}%")
        click.echo(f"   Payback Period:       {result.payback_period_years:.1f} years")
        click.echo(f"   Net Present Value:    ${result.net_present_value:,.0f}")
        
        # Show strategic advantages
        if result.strategic_advantages:
            click.echo(f"\n✅ STRATEGIC ADVANTAGES:")
            for advantage in result.strategic_advantages:
                click.echo(f"   • {advantage}")
        
        # Show risk factors
        if result.risk_factors:
            click.echo(f"\n⚠️  RISK FACTORS:")
            for risk in result.risk_factors:
                click.echo(f"   • {risk}")
        
        # Investment recommendation
        if result.net_present_value > 0 and result.estimated_roi_percent > 12:
            click.echo(f"\n🎯 RECOMMENDATION: STRONG BUY")
            click.echo("   This investment meets financial criteria.")
        elif result.net_present_value > 0:
            click.echo(f"\n🤔 RECOMMENDATION: CONSIDER")
            click.echo("   Positive NPV but moderate returns.")
        else:
            click.echo(f"\n❌ RECOMMENDATION: PASS")
            click.echo("   Investment does not meet financial thresholds.")
        
        # Save results if requested
        if output:
            with open(output, 'w') as f:
                yaml.dump(result.to_dict(), f, default_flow_style=False)
            click.echo(f"\n💾 Results saved to {output}")
            
    except Exception as e:
        click.echo(f"❌ Error during analysis: {str(e)}", err=True)
        raise click.Abort()


@cli.command()
@click.option('--parcel-id', required=True, help='Parcel ID to underwrite')
@click.option('--purchase-price', type=float, help='Proposed purchase price')
@click.option('--loan-amount', type=float, help='Loan amount')
@click.option('--hold-period', type=int, default=10, help='Hold period in years')
@click.option('--output', '-o', type=click.Path(), help='Save results to file')
@click.pass_context
def underwrite(ctx, parcel_id, purchase_price, loan_amount, hold_period, output):
    """Perform detailed financial underwriting analysis"""
    
    config = ctx.obj.get('config', {})
    
    click.echo(f"📊 Underwriting parcel: {parcel_id}")
    
    # Initialize service
    underwriting_service = UnderwritingService(config)
    
    try:
        # Run underwriting
        with click.progressbar(length=100, label="Running financial analysis") as bar:
            bar.update(20)
            result = underwriting_service.run_underwriting(
                parcel_id=parcel_id,
                purchase_price=purchase_price,
                loan_amount=loan_amount,
                hold_period=hold_period
            )
            bar.update(80)
        
        # Display results
        click.echo("\n" + "="*60)
        click.echo("📊 FINANCIAL UNDERWRITING ANALYSIS")
        click.echo("="*60)
        
        # Cash flow summary
        cashflow = result.cashflow_analysis
        click.echo(f"\n💰 CASH FLOW ANALYSIS:")
        click.echo(f"   Year 1 NOI:           ${cashflow['year_1_noi']:,.0f}")
        click.echo(f"   Year 1 Cash Flow:     ${cashflow['annual_cash_flows'][0]:,.0f}")
        click.echo(f"   NPV (10 years):       ${cashflow['npv']:,.0f}")
        click.echo(f"   IRR:                  {cashflow['irr']:.2%}")
        
        # Key ratios
        ratios = result.financial_ratios
        click.echo(f"\n📈 KEY RATIOS:")
        click.echo(f"   Cash-on-Cash Return:  {ratios['cash_on_cash_return']:.2%}")
        click.echo(f"   Cap Rate:             {ratios['cap_rate']:.2%}")
        click.echo(f"   DSCR (min):           {ratios['min_dscr']:.2f}")
        click.echo(f"   Equity Multiple:      {ratios['equity_multiple']:.2f}x")
        click.echo(f"   Payback Period:       {ratios['payback_period']:.1f} years")
        
        # Stress test results
        stress = result.stress_test_results
        click.echo(f"\n🔬 STRESS TEST RESULTS:")
        click.echo(f"   Base Case NPV:        ${stress['base_case_npv']:,.0f}")
        click.echo(f"   Best Case NPV:        ${stress['best_case_npv']:,.0f}")
        click.echo(f"   Worst Case NPV:       ${stress['worst_case_npv']:,.0f}")
        click.echo(f"   Break-even Rent:      ${stress['break_even_rent']:.2f}/sqft")
        
        # Performance vs benchmarks
        performance = result.performance_metrics
        click.echo(f"\n🎯 PERFORMANCE vs BENCHMARKS:")
        click.echo(f"   Overall Grade:        {performance['grade']}")
        click.echo(f"   IRR vs Target:        {performance['irr_vs_target']:+.1%}")
        click.echo(f"   Cash-on-Cash vs Target: {performance['cash_on_cash_vs_target']:+.1%}")
        
        # Investment recommendation
        grade = performance['grade']
        if grade in ['A+', 'A']:
            click.echo(f"\n🎯 RECOMMENDATION: STRONG BUY")
            click.echo("   Exceeds all financial benchmarks.")
        elif grade in ['B+', 'B']:
            click.echo(f"\n🤔 RECOMMENDATION: BUY")
            click.echo("   Meets most financial criteria.")
        elif grade == 'C':
            click.echo(f"\n⚠️  RECOMMENDATION: MARGINAL")
            click.echo("   Below-average returns.")
        else:
            click.echo(f"\n❌ RECOMMENDATION: REJECT")
            click.echo("   Does not meet investment criteria.")
        
        # Save results if requested
        if output:
            with open(output, 'w') as f:
                yaml.dump(result.to_dict(), f, default_flow_style=False)
            click.echo(f"\n💾 Results saved to {output}")
            
    except Exception as e:
        click.echo(f"❌ Error during underwriting: {str(e)}", err=True)
        raise click.Abort()


@cli.command()
@click.option('--deal-id', required=True, help='Deal ID to manage')
@click.option('--output', '-o', type=click.Path(), help='Save results to file')
@click.pass_context
def close_deal(ctx, deal_id, output):
    """Manage deal closure workflow"""
    
    config = ctx.obj.get('config', {})
    
    click.echo(f"🏁 Managing deal closure: {deal_id}")
    
    # Initialize service
    deal_service = DealClosureService(config)
    
    try:
        # Run deal closure workflow
        result = deal_service.run_deal_closure(deal_id)
        
        # Display results
        click.echo("\n" + "="*50)
        click.echo("🏁 DEAL CLOSURE STATUS")
        click.echo("="*50)
        
        click.echo(f"\n📋 Deal: {deal_id}")
        click.echo(f"   Stage: {result.current_stage}")
        click.echo(f"   Completion: {result.completion_percentage:.1f}%")
        click.echo(f"   Compliance: {result.compliance_status}")
        
        if result.estimated_closing_date:
            click.echo(f"   Est. Closing: {result.estimated_closing_date}")
        
        if result.pending_tasks:
            click.echo(f"\n⏳ PENDING TASKS ({len(result.pending_tasks)}):")
            for task in result.pending_tasks[:5]:  # Show top 5
                click.echo(f"   • {task}")
        
        if result.next_actions:
            click.echo(f"\n🎯 NEXT ACTIONS:")
            for action in result.next_actions:
                click.echo(f"   • {action}")
        
        # Save results if requested
        if output:
            with open(output, 'w') as f:
                yaml.dump(result.to_dict(), f, default_flow_style=False)
            click.echo(f"\n💾 Results saved to {output}")
            
    except Exception as e:
        click.echo(f"❌ Error managing deal: {str(e)}", err=True)
        raise click.Abort()


@cli.command()
@click.option('--area', required=True, help='Geographic area to analyze')
@click.option('--max-sites', type=int, default=10, help='Maximum sites to analyze')
@click.option('--industry', default="manufacturing", help='Target industry')
@click.option('--output-dir', type=click.Path(), help='Directory to save results')
@click.pass_context
def batch_analysis(ctx, area, max_sites, industry, output_dir):
    """Run batch analysis on multiple sites"""
    
    config = ctx.obj.get('config', {})
    
    click.echo(f"🔄 Running batch analysis in {area}")
    click.echo(f"Target: {max_sites} {industry} sites")
    
    # This would implement batch processing logic
    # For now, show placeholder
    click.echo("\n⚠️  Batch analysis feature coming soon!")
    click.echo("   Use 'prospect' command for individual site analysis.")


@cli.command()
@click.option('--template', type=click.Choice(['basic', 'enterprise']),
              default='basic', help='Configuration template')
@click.pass_context
def init(ctx, template):
    """Initialize configuration files"""
    
    click.echo(f"🔧 Initializing {template} configuration...")
    
    # Create config.yaml
    config_data = {
        'api_keys': {
            'regrid': 'your_regrid_api_key_here',
            'google_maps': 'your_google_maps_api_key_here',
            'bls': 'your_bls_api_key_here',
            'eia': 'your_eia_api_key_here',
            'census': 'your_census_api_key_here',
            'incentives': 'your_incentives_api_key_here',
            'dealpath': 'your_dealpath_api_key_here'
        },
        'analysis': {
            'default_hold_period': 10,
            'discount_rate': 0.10,
            'target_irr': 0.12
        },
        'logging': {
            'level': 'INFO',
            'file': 'industrial_discovery.log'
        }
    }
    
    if template == 'enterprise':
        config_data['database'] = {
            'url': 'postgresql://user:pass@localhost/industrial_db'
        }
        config_data['notifications'] = {
            'email': '<EMAIL>',
            'slack_webhook': 'your_slack_webhook_url'
        }
    
    with open('config.yaml', 'w') as f:
        yaml.dump(config_data, f, default_flow_style=False)
    
    click.echo("✅ Configuration file created: config.yaml")
    click.echo("📝 Please update API keys in config.yaml")


if __name__ == "__main__":
    cli() 