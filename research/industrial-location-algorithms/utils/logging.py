"""
Logging configuration utilities
"""

import logging
import structlog
from typing import Optional, Dict, Any


def setup_logging(config: Optional[Dict[str, Any]] = None) -> structlog.stdlib.BoundLogger:
    """Setup structured logging with configuration"""
    
    if config is None:
        config = {
            "level": "INFO",
            "file": "industrial_discovery.log",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        }
    
    # Configure standard library logging
    logging_config = config.get("logging", config)
    
    level = getattr(logging, logging_config.get("level", "INFO").upper())
    log_format = logging_config.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    
    # Configure root logger
    logging.basicConfig(
        level=level,
        format=log_format,
        handlers=[
            logging.StreamHandler(),  # Console output
            logging.FileHandler(logging_config.get("file", "industrial_discovery.log"))  # File output
        ]
    )
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    return structlog.get_logger("industrial_discovery")


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """Get a logger instance for a specific module"""
    return structlog.get_logger(name)