"""
Configuration management utilities
"""

import os
import yaml
from typing import Dict, Any
from pathlib import Path


def load_config(config_path: str = "config.yaml") -> Dict[str, Any]:
    """Load configuration from YAML file"""
    
    config_file = Path(config_path)
    
    if not config_file.exists():
        # Return default configuration if no file exists
        return get_default_config()
    
    try:
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
        
        # Merge with defaults to ensure all required keys exist
        default_config = get_default_config()
        merged_config = merge_configs(default_config, config)
        
        # Override with environment variables if they exist
        override_with_env_vars(merged_config)
        
        return merged_config
    except Exception as e:
        raise Exception(f"Failed to load configuration from {config_path}: {e}")


def get_default_config() -> Dict[str, Any]:
    """Get default configuration values"""
    return {
        "api_keys": {
            "regrid": "",
            "google_maps": "",
            "bls": "",
            "eia": "",
            "attom": "",
            "census": ""
        },
        "database": {
            "type": "sqlite",
            "path": "industrial_discovery.db",
            "host": "localhost",
            "port": 5432,
            "username": "",
            "password": "",
            "database": "industrial_discovery"
        },
        "analysis": {
            "prospecting": {
                "strategic_score_threshold": 60
            },
            "underwriting": {
                "discount_rate": 0.10,
                "sale_cap_rate": 0.07,
                "hold_period_years": 10
            },
            "deal_closure": {
                "default_due_diligence_days": 30,
                "default_financing_days": 45
            }
        },
        "logging": {
            "level": "INFO",
            "file": "industrial_discovery.log",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        },
        "external_apis": {
            "regrid_base_url": "https://api.regrid.com/api/v1",
            "bls_base_url": "https://api.bls.gov/publicAPI/v2",
            "eia_base_url": "https://api.eia.gov/v2",
            "census_base_url": "https://api.census.gov/data"
        }
    }


def merge_configs(default: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
    """Recursively merge configuration dictionaries"""
    result = default.copy()
    
    for key, value in override.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = merge_configs(result[key], value)
        else:
            result[key] = value
    
    return result


def override_with_env_vars(config: Dict[str, Any]) -> None:
    """Override configuration with environment variables"""
    
    # API keys from environment
    env_mapping = {
        "REGRID_API_KEY": ["api_keys", "regrid"],
        "GOOGLE_MAPS_API_KEY": ["api_keys", "google_maps"],
        "BLS_API_KEY": ["api_keys", "bls"],
        "EIA_API_KEY": ["api_keys", "eia"],
        "ATTOM_API_KEY": ["api_keys", "attom"],
        "CENSUS_API_KEY": ["api_keys", "census"],
        
        # Database configuration
        "DB_HOST": ["database", "host"],
        "DB_PORT": ["database", "port"],
        "DB_USERNAME": ["database", "username"],
        "DB_PASSWORD": ["database", "password"],
        "DB_NAME": ["database", "database"],
        
        # Logging
        "LOG_LEVEL": ["logging", "level"],
        "LOG_FILE": ["logging", "file"]
    }
    
    for env_var, config_path in env_mapping.items():
        value = os.getenv(env_var)
        if value:
            # Navigate to the correct nested dictionary
            current = config
            for key in config_path[:-1]:
                current = current[key]
            
            # Set the value (convert port to int if needed)
            if config_path[-1] == "port":
                current[config_path[-1]] = int(value)
            else:
                current[config_path[-1]] = value


def validate_config(config: Dict[str, Any]) -> None:
    """Validate configuration and raise errors for missing required values"""
    
    required_sections = ["api_keys", "database", "analysis", "logging"]
    
    for section in required_sections:
        if section not in config:
            raise ValueError(f"Missing required configuration section: {section}")
    
    # Check for required API keys (at least one should be configured)
    api_keys = config["api_keys"]
    if not any(api_keys.values()):
        raise ValueError("At least one API key must be configured")
    
    # Validate analysis parameters
    analysis = config["analysis"]
    if "underwriting" in analysis:
        uw_config = analysis["underwriting"]
        if uw_config.get("discount_rate", 0) <= 0:
            raise ValueError("Discount rate must be greater than 0")
        if uw_config.get("hold_period_years", 0) <= 0:
            raise ValueError("Hold period must be greater than 0")


def get_api_key(config: Dict[str, Any], service: str) -> str:
    """Get API key for a specific service"""
    api_key = config.get("api_keys", {}).get(service, "")
    if not api_key:
        raise ValueError(f"API key for {service} is not configured")
    return api_key


def get_database_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """Get database configuration"""
    return config.get("database", {})


def get_analysis_config(config: Dict[str, Any], analysis_type: str) -> Dict[str, Any]:
    """Get analysis configuration for a specific type"""
    return config.get("analysis", {}).get(analysis_type, {}) 