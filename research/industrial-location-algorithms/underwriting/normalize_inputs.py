"""
Input normalization for financial modeling
"""

import pandas as pd
from typing import Dict, Any
from models.parcel import Parcel
from models.labor import LaborMetrics
from models.energy import EnergyRates
from models.demographics import Demographics
from models.incentives import IncentiveData


def merge_inputs(parcel: Parcel, labor: LaborMetrics, energy: EnergyRates, 
                demographics: Demographics, incentives: IncentiveData) -> Dict[str, Any]:
    """Merge and normalize all inputs for financial modeling"""
    
    # Base property metrics
    lot_size_sqft = parcel.lot_size or 250000  # Default 250k sqft
    building_size_sqft = lot_size_sqft * 0.5  # 50% lot coverage
    market_value = parcel.market_value or parcel.assessed_value or 12000000
    
    # Development costs (realistic industrial construction)
    land_cost = market_value
    construction_cost_per_sqft = 85  # $85/sqft for industrial construction
    development_cost = building_size_sqft * construction_cost_per_sqft
    soft_costs = development_cost * 0.15  # 15% soft costs
    financing_costs = (land_cost + development_cost + soft_costs) * 0.03  # 3% financing fees
    
    total_project_cost = land_cost + development_cost + soft_costs + financing_costs
    
    # Revenue assumptions (industrial lease rates)
    base_rent_per_sqft = _calculate_market_rent(parcel, demographics)
    annual_rent_escalation = 0.025  # 2.5% annual increases
    
    # Operating expenses (annual)
    property_tax_rate = incentives.property_tax_rate
    property_taxes = (land_cost + development_cost) * property_tax_rate
    
    # Apply tax abatements if available
    if incentives.property_tax_abatement:
        abatement_savings = property_taxes * (incentives.abatement_percentage or 0.5)
        property_taxes_net = property_taxes - abatement_savings
    else:
        property_taxes_net = property_taxes
    
    # Other operating costs
    insurance = building_size_sqft * 0.25  # $0.25/sqft
    utilities = building_size_sqft * energy.electricity_rate * 12 * 50  # 50 kWh/sqft/year
    maintenance = building_size_sqft * 1.50  # $1.50/sqft maintenance
    management_fees = building_size_sqft * base_rent_per_sqft * 0.05  # 5% management fee
    
    # Labor costs for development
    construction_jobs = int(building_size_sqft / 2500)  # Jobs needed for construction
    average_construction_wage = labor.skilled_wage or 28.00
    construction_labor_cost = construction_jobs * average_construction_wage * 2080 * 1.5  # 1.5 years
    
    # Financing assumptions
    loan_to_cost = 0.75  # 75% LTC
    loan_amount = total_project_cost * loan_to_cost
    interest_rate = 0.065  # 6.5% interest rate
    loan_term_years = 25
    
    # Cap rate for exit valuation
    exit_cap_rate = 0.07  # 7% exit cap rate
    
    return {
        "property": {
            "lot_size_sqft": lot_size_sqft,
            "building_size_sqft": building_size_sqft,
            "market_value": market_value,
            "city": parcel.city,
            "state": parcel.state
        },
        "development": {
            "total_project_cost": total_project_cost,
            "construction_cost_per_sqft": construction_cost_per_sqft,
            "construction_labor_cost": construction_labor_cost
        },
        "costs": {
            "land_cost": land_cost,
            "development_cost": development_cost,
            "soft_costs": soft_costs,
            "financing_costs": financing_costs
        },
        "revenue": {
            "base_rent_per_sqft": base_rent_per_sqft,
            "annual_rent_escalation": annual_rent_escalation,
            "building_size_sqft": building_size_sqft
        },
        "operating": {
            "property_taxes": property_taxes_net,
            "property_taxes_gross": property_taxes,
            "tax_abatement_savings": property_taxes - property_taxes_net,
            "insurance": insurance,
            "utilities": utilities,
            "maintenance": maintenance,
            "management": management_fees
        },
        "financing": {
            "loan_amount": loan_amount,
            "interest_rate": interest_rate,
            "loan_term_years": loan_term_years,
            "loan_to_cost": loan_to_cost
        },
        "assumptions": {
            "exit_cap_rate": exit_cap_rate,
            "hold_period_years": 10,
            "discount_rate": 0.10  # 10% discount rate for NPV
        },
        "market_data": {
            "median_income": demographics.median_income,
            "unemployment_rate": labor.unemployment_rate,
            "electricity_rate": energy.electricity_rate,
            "workforce_quality": demographics.workforce_quality_score()
        }
    }


def _calculate_market_rent(parcel: Parcel, demographics: Demographics) -> float:
    """Calculate market rent per square foot based on location and demographics"""
    
    # Base rent by market tier
    base_rents = {
        "Dallas": 6.50,
        "Houston": 6.25,
        "Austin": 7.00,
        "San Antonio": 5.75
    }
    
    base_rent = base_rents.get(parcel.city, 5.50)  # Default $5.50/sqft
    
    # Adjust for demographics and economic conditions
    if demographics.median_income > 80000:
        base_rent *= 1.10  # 10% premium for high-income areas
    elif demographics.median_income < 50000:
        base_rent *= 0.95  # 5% discount for lower-income areas
    
    # Adjust for workforce quality
    workforce_score = demographics.workforce_quality_score()
    if workforce_score > 70:
        base_rent *= 1.05  # 5% premium for skilled workforce
    
    return round(base_rent, 2)


def _estimate_industrial_demand(demographics: Demographics, market_data: Dict) -> float:
    """Estimate industrial real estate demand multiplier"""
    
    demand_score = 1.0
    
    # Population growth indicates demand
    if demographics.total_population > 500000:
        demand_score += 0.10
    
    # Employment in manufacturing/logistics
    if demographics.pct_manufacturing and demographics.pct_manufacturing > 0.15:
        demand_score += 0.05
    
    # Education level affects demand for higher-value industrial
    if demographics.pct_college > 0.40:
        demand_score += 0.05
    
    return min(demand_score, 1.25)  # Cap at 25% premium 