"""
Cash flow calculation for industrial real estate investments
"""

import numpy as np
import numpy_financial as npf
from typing import Dict, Any, List


def compute_cashflow(inputs: Dict[str, Any]) -> Dict[str, float]:
    """Compute detailed cash flow projections and financial metrics"""
    
    # Extract key inputs
    building_size = inputs["property"]["building_size_sqft"]
    base_rent = inputs["revenue"]["base_rent_per_sqft"]
    rent_escalation = inputs["revenue"]["annual_rent_escalation"]
    hold_period = inputs["assumptions"]["hold_period_years"]
    discount_rate = inputs["assumptions"]["discount_rate"]
    exit_cap_rate = inputs["assumptions"]["exit_cap_rate"]
    
    # Operating expenses
    property_taxes = inputs["operating"]["property_taxes"]
    insurance = inputs["operating"]["insurance"]
    utilities = inputs["operating"]["utilities"]
    maintenance = inputs["operating"]["maintenance"]
    management = inputs["operating"]["management"]
    
    total_operating_expenses = property_taxes + insurance + utilities + maintenance + management
    
    # Financing
    loan_amount = inputs["financing"]["loan_amount"]
    interest_rate = inputs["financing"]["interest_rate"]
    loan_term = inputs["financing"]["loan_term_years"]
    
    # Calculate annual debt service
    annual_debt_service = -npf.pmt(interest_rate, loan_term, loan_amount)
    
    # Project cash flows year by year
    cash_flows = []
    noi_by_year = []
    rental_income_by_year = []
    
    for year in range(1, hold_period + 1):
        # Rental income with escalation
        annual_rent = base_rent * (1 + rent_escalation) ** (year - 1)
        rental_income = building_size * annual_rent
        rental_income_by_year.append(rental_income)
        
        # Net Operating Income
        noi = rental_income - total_operating_expenses
        noi_by_year.append(noi)
        
        # Cash flow after debt service
        cash_flow = noi - annual_debt_service
        cash_flows.append(cash_flow)
    
    # Calculate exit value and terminal cash flow
    final_year_noi = noi_by_year[-1]
    exit_value = final_year_noi / exit_cap_rate
    
    # Remaining loan balance at exit
    remaining_balance = npf.fv(interest_rate, hold_period, -annual_debt_service, loan_amount)
    net_exit_proceeds = exit_value - abs(remaining_balance)
    
    # Total cash flow including exit
    total_cash_flows = cash_flows[:-1] + [cash_flows[-1] + net_exit_proceeds]
    
    # Initial investment (equity)
    initial_investment = inputs["development"]["total_project_cost"] - loan_amount
    
    # Calculate financial metrics
    npv = npf.npv(discount_rate, [-initial_investment] + total_cash_flows)
    
    # IRR calculation
    try:
        irr = npf.irr([-initial_investment] + total_cash_flows)
        if np.isnan(irr) or irr < -0.5 or irr > 2.0:  # Sanity check
            irr = 0.0
    except:
        irr = 0.0
    
    # Total return multiple
    total_cash_received = sum(total_cash_flows)
    total_return_multiple = total_cash_received / initial_investment if initial_investment > 0 else 0
    
    # Depreciation benefit (simplified)
    depreciable_basis = inputs["costs"]["development_cost"] + inputs["costs"]["soft_costs"]
    annual_depreciation = depreciable_basis / 39  # 39-year depreciation for commercial
    depreciation_tax_benefit = annual_depreciation * 0.25  # Assume 25% tax rate
    total_depreciation_benefit = depreciation_tax_benefit * hold_period
    
    return {
        "npv": round(npv, 0),
        "irr": round(irr, 4),
        "total_project_cost": inputs["development"]["total_project_cost"],
        "initial_investment": initial_investment,
        "total_revenue": sum(rental_income_by_year),
        "total_cash_flows": total_cash_flows,
        "annual_cash_flows": cash_flows,
        "annual_debt_service": annual_debt_service,
        "year_1_noi": noi_by_year[0],
        "year_10_noi": noi_by_year[-1] if len(noi_by_year) >= 10 else noi_by_year[-1],
        "year_1_rental_income": rental_income_by_year[0],
        "year_10_rental_income": rental_income_by_year[-1] if len(rental_income_by_year) >= 10 else rental_income_by_year[-1],
        "exit_value": exit_value,
        "net_exit_proceeds": net_exit_proceeds,
        "total_return_multiple": total_return_multiple,
        "total_depreciation_benefit": total_depreciation_benefit,
        "loan_balance_at_exit": abs(remaining_balance),
        "cumulative_cash_flow": sum(cash_flows),
        "average_annual_noi": sum(noi_by_year) / len(noi_by_year)
    }


def calculate_levered_returns(unlevered_cashflows: List[float], 
                            loan_amount: float, 
                            interest_rate: float, 
                            loan_term: int) -> Dict[str, Any]:
    """Calculate levered returns given unlevered cash flows and debt terms"""
    
    # Calculate debt service
    annual_debt_service = -npf.pmt(interest_rate, loan_term, loan_amount)
    
    # Apply debt service to cash flows
    levered_cashflows = [cf - annual_debt_service for cf in unlevered_cashflows]
    
    return {
        "levered_cashflows": levered_cashflows,
        "annual_debt_service": annual_debt_service,
        "total_debt_service": annual_debt_service * len(unlevered_cashflows)
    }


def calculate_tax_benefits(inputs: Dict[str, Any], hold_period: int) -> Dict[str, float]:
    """Calculate tax benefits including depreciation and incentives"""
    
    # Depreciation benefits
    depreciable_basis = inputs["costs"]["development_cost"] + inputs["costs"]["soft_costs"]
    annual_depreciation = depreciable_basis / 39  # Commercial real estate depreciation
    
    # Assume investor tax rate
    tax_rate = 0.25
    annual_depreciation_benefit = annual_depreciation * tax_rate
    total_depreciation_benefit = annual_depreciation_benefit * hold_period
    
    # Tax abatement savings (already calculated in operating expenses)
    tax_abatement_savings = inputs["operating"]["tax_abatement_savings"]
    total_abatement_savings = tax_abatement_savings * hold_period
    
    return {
        "annual_depreciation": annual_depreciation,
        "annual_depreciation_benefit": annual_depreciation_benefit,
        "total_depreciation_benefit": total_depreciation_benefit,
        "annual_tax_abatement": tax_abatement_savings,
        "total_tax_abatement": total_abatement_savings,
        "total_tax_benefits": total_depreciation_benefit + total_abatement_savings
    }


def calculate_break_even_metrics(inputs: Dict[str, Any]) -> Dict[str, float]:
    """Calculate break-even rent and occupancy metrics"""
    
    building_size = inputs["property"]["building_size_sqft"]
    annual_debt_service = inputs["financing"]["loan_amount"] * inputs["financing"]["interest_rate"]
    
    # Operating expenses
    total_operating_expenses = sum([
        inputs["operating"]["property_taxes"],
        inputs["operating"]["insurance"],
        inputs["operating"]["utilities"],
        inputs["operating"]["maintenance"],
        inputs["operating"]["management"]
    ])
    
    # Break-even NOI
    break_even_noi = annual_debt_service
    
    # Break-even gross income
    break_even_gross_income = break_even_noi + total_operating_expenses
    
    # Break-even rent per square foot
    break_even_rent_per_sqft = break_even_gross_income / building_size
    
    return {
        "break_even_noi": break_even_noi,
        "break_even_gross_income": break_even_gross_income,
        "break_even_rent_per_sqft": break_even_rent_per_sqft,
        "break_even_occupancy": 1.0  # Assuming full occupancy needed
    } 