"""
Financial ratios calculation for real estate investment analysis
"""

from typing import Dict, Any


def compute_ratios(cashflow_result: Dict[str, Any]) -> Dict[str, float]:
    """Compute key financial ratios and performance metrics"""
    
    # Extract data from cashflow results
    annual_cash_flows = cashflow_result["annual_cash_flows"]
    annual_debt_service = cashflow_result["annual_debt_service"]
    year_1_noi = cashflow_result["year_1_noi"]
    initial_investment = cashflow_result["initial_investment"]
    total_project_cost = cashflow_result["total_project_cost"]
    npv = cashflow_result["npv"]
    irr = cashflow_result["irr"]
    
    # Cash-on-Cash Return (Year 1)
    year_1_cash_flow = annual_cash_flows[0] if annual_cash_flows else 0
    cash_on_cash_return = year_1_cash_flow / initial_investment if initial_investment > 0 else 0
    
    # Debt Service Coverage Ratio (DSCR)
    dscr_values = []
    for i, cash_flow in enumerate(annual_cash_flows):
        noi = cash_flow + annual_debt_service  # Add back debt service to get NOI
        dscr = noi / annual_debt_service if annual_debt_service > 0 else 0
        dscr_values.append(dscr)
    
    min_dscr = min(dscr_values) if dscr_values else 0
    max_dscr = max(dscr_values) if dscr_values else 0
    average_dscr = sum(dscr_values) / len(dscr_values) if dscr_values else 0
    
    # Cap Rate (Year 1 NOI / Total Project Cost)
    cap_rate = year_1_noi / total_project_cost if total_project_cost > 0 else 0
    
    # Return on Investment (ROI)
    total_returns = sum(annual_cash_flows) + cashflow_result.get("net_exit_proceeds", 0)
    roi = (total_returns - initial_investment) / initial_investment if initial_investment > 0 else 0
    
    # Payback Period (years to recover initial investment)
    cumulative_cash_flow = 0
    payback_period = 0
    for i, cash_flow in enumerate(annual_cash_flows):
        cumulative_cash_flow += cash_flow
        if cumulative_cash_flow >= initial_investment:
            payback_period = i + 1
            break
    
    if payback_period == 0:  # Not paid back within hold period
        payback_period = len(annual_cash_flows) + 1
    
    # Equity Multiple
    total_cash_received = sum(annual_cash_flows) + cashflow_result.get("net_exit_proceeds", 0)
    equity_multiple = total_cash_received / initial_investment if initial_investment > 0 else 0
    
    # Net Present Value per Dollar Invested
    npv_per_dollar = npv / initial_investment if initial_investment > 0 else 0
    
    # Modified Internal Rate of Return (MIRR) - simplified
    # Assuming reinvestment rate of 6% and finance rate of 7%
    reinvestment_rate = 0.06
    finance_rate = 0.07
    
    # Positive and negative cash flows
    positive_flows = [max(0, cf) for cf in annual_cash_flows]
    negative_flows = [min(0, cf) for cf in annual_cash_flows]
    
    # Future value of positive flows
    fv_positive = sum(cf * (1 + reinvestment_rate) ** (len(positive_flows) - i - 1) 
                     for i, cf in enumerate(positive_flows))
    
    # Present value of negative flows
    pv_negative = initial_investment + sum(abs(cf) / (1 + finance_rate) ** (i + 1) 
                                          for i, cf in enumerate(negative_flows) if cf < 0)
    
    # MIRR calculation
    if pv_negative > 0 and len(annual_cash_flows) > 0:
        mirr = (fv_positive / pv_negative) ** (1 / len(annual_cash_flows)) - 1
    else:
        mirr = 0
    
    # Operating Expense Ratio
    total_operating_expenses = year_1_noi - annual_cash_flows[0] - annual_debt_service
    gross_income = year_1_noi + total_operating_expenses
    operating_expense_ratio = total_operating_expenses / gross_income if gross_income > 0 else 0
    
    # Loan-to-Value Ratio
    loan_amount = total_project_cost - initial_investment
    ltv_ratio = loan_amount / total_project_cost if total_project_cost > 0 else 0
    
    # Break-even Occupancy (simplified - assuming single tenant)
    break_even_occupancy = annual_debt_service / year_1_noi if year_1_noi > 0 else 1.0
    break_even_occupancy = min(break_even_occupancy, 1.0)  # Cap at 100%
    
    return {
        "cash_on_cash_return": round(cash_on_cash_return, 4),
        "min_dscr": round(min_dscr, 2),
        "max_dscr": round(max_dscr, 2),
        "average_dscr": round(average_dscr, 2),
        "cap_rate": round(cap_rate, 4),
        "roi": round(roi, 4),
        "payback_period": payback_period,
        "equity_multiple": round(equity_multiple, 2),
        "npv_per_dollar": round(npv_per_dollar, 2),
        "mirr": round(mirr, 4),
        "operating_expense_ratio": round(operating_expense_ratio, 4),
        "ltv_ratio": round(ltv_ratio, 4),
        "break_even_occupancy": round(break_even_occupancy, 4),
        "annual_dscr_values": [round(dscr, 2) for dscr in dscr_values]
    }


def calculate_performance_metrics(cashflow_result: Dict[str, Any], 
                                benchmark_metrics: Dict[str, float] = None) -> Dict[str, Any]:
    """Calculate performance metrics compared to benchmarks"""
    
    if benchmark_metrics is None:
        # Default industrial real estate benchmarks
        benchmark_metrics = {
            "target_irr": 0.12,
            "target_cash_on_cash": 0.08,
            "minimum_dscr": 1.15,
            "target_cap_rate": 0.07
        }
    
    ratios = compute_ratios(cashflow_result)
    
    performance = {
        "irr_vs_target": cashflow_result["irr"] / benchmark_metrics["target_irr"] - 1,
        "cash_on_cash_vs_target": ratios["cash_on_cash_return"] / benchmark_metrics["target_cash_on_cash"] - 1,
        "dscr_cushion": ratios["min_dscr"] - benchmark_metrics["minimum_dscr"],
        "cap_rate_vs_target": ratios["cap_rate"] / benchmark_metrics["target_cap_rate"] - 1
    }
    
    # Overall performance score
    score = 0
    if performance["irr_vs_target"] > 0:
        score += 25
    if performance["cash_on_cash_vs_target"] > 0:
        score += 25
    if performance["dscr_cushion"] > 0:
        score += 25
    if ratios["payback_period"] <= 7:  # 7 years or less
        score += 25
    
    performance["overall_score"] = score
    performance["grade"] = _assign_grade(score)
    
    return performance


def _assign_grade(score: int) -> str:
    """Assign letter grade based on performance score"""
    if score >= 90:
        return "A+"
    elif score >= 80:
        return "A"
    elif score >= 70:
        return "B+"
    elif score >= 60:
        return "B"
    elif score >= 50:
        return "C"
    else:
        return "D"


def calculate_risk_metrics(cashflow_result: Dict[str, Any]) -> Dict[str, float]:
    """Calculate risk-related metrics"""
    
    annual_cash_flows = cashflow_result["annual_cash_flows"]
    
    # Cash flow volatility (standard deviation)
    if len(annual_cash_flows) > 1:
        mean_cf = sum(annual_cash_flows) / len(annual_cash_flows)
        variance = sum((cf - mean_cf) ** 2 for cf in annual_cash_flows) / len(annual_cash_flows)
        volatility = variance ** 0.5
        coefficient_of_variation = volatility / abs(mean_cf) if mean_cf != 0 else 0
    else:
        volatility = 0
        coefficient_of_variation = 0
    
    # Downside risk (probability of negative cash flows)
    negative_periods = sum(1 for cf in annual_cash_flows if cf < 0)
    downside_risk = negative_periods / len(annual_cash_flows) if annual_cash_flows else 0
    
    # Maximum drawdown
    cumulative_cf = 0
    peak_cf = 0
    max_drawdown = 0
    
    for cf in annual_cash_flows:
        cumulative_cf += cf
        if cumulative_cf > peak_cf:
            peak_cf = cumulative_cf
        
        drawdown = (peak_cf - cumulative_cf) / peak_cf if peak_cf > 0 else 0
        max_drawdown = max(max_drawdown, drawdown)
    
    return {
        "cash_flow_volatility": round(volatility, 0),
        "coefficient_of_variation": round(coefficient_of_variation, 4),
        "downside_risk": round(downside_risk, 4),
        "max_drawdown": round(max_drawdown, 4)
    } 