
## Module Specifications


#### Parcel prospector  (`Parcel use_analysis.py`)
- Calculates operational costs and expenses using data from:
  - **EIA API**: Energy costs, utility rates, and consumption patterns
  - **BLS API**: Labor costs, wage rates, and employment expenses
  - **US Census API**: Regional economic indicators and market rates
  - Building / lot size	Scales the archetype curve into absolute dollars (20 k sf vs. 200 k sf plant).
  - Age / class	Tells you whether you’re converting an existing shell (CapEx down, OpEx up) or building ground‑up (CapEx up, OpEx down).
  - Property tax	Fixed OpEx that’s parcel‑specific and not in the archetype tables.
  - Geo tag	Chooses which regional multipliers (EIA, BLS) apply to that archetype.
  - Industry-Specific Cost models
- Produces detailed cost estimations for:
  - Manufacturing facilities (labor, energy, utilities)
  - Warehouse operations (storage, logistics, maintenance)
  - Custom industrial uses (user-defined parameters)
- Generates itemized cost breakdown:
  - Energy consumption and rates
  - Labor and staffing expenses
  - Utility costs
  - Maintenance estimates
  - Regional cost adjustments
- Provides configurable parameters:
  - Users can override any cost assumptions
  - Supports custom rate adjustments
  - Allows for scenario modeling
- Implements caching for API data to improve performance
- Includes rate limiting and error handling for API calls

### 3. Underwriting Layer

#### Underwriting (underwriting.py)
Extracts and evaluates historical mortgage and transaction data from sales[] and mortgages[] to build a timeline of financing events for each parcel.

Estimates Loan-to-Value (LTV) by comparing mortgages[0].original_loan_amount with the most recent sale_price from sales[] or the assessment.total_value.

Projects debt coverage needs and refinancing risk using mortgages[0].maturity_date, interest_rate, and recording_date.

Uses lender_name to cluster regional lending activity and analyze lender concentration trends.

Constructs full financing trajectory per parcel: date and size of each mortgage, lender behavior, debt stacking, and implied financial leverage.

Enables underwriting teams to distinguish between high-leverage parcels and fully owned assets, a critical step in determining acquisition strategy and funding structures.

Flags risky parcels (e.g. near-term maturity, recent large refinancing) for exclusion or enhanced due diligence in underwriting.


### 4. Document Generation Layer

#### Template System
- Markdown/HTML templates for:
  - Letters of Intent (LOI)
  - Requests for Proposal (RFP)
  - Underwriting memos
- Dynamic field insertion
- Formatting preservation

### 5. API Layer

#### FastAPI Application (`main.py`)
- RESTful endpoints:
  - `/search`: Cluster and parcel search
  - `/analyze`: Property analysis
  - `/generate`: Document generation
- Authentication and rate limiting
- Response caching

### Module Name (`filename.py`)

#### Dependencies
- Required packages
- External services
- System requirements

#### Input Schema
```python
# Example input data structure
```

#### Output Schema
```python
# Example output data structure
```

#### API Integration Details
- Endpoint specifications
- Authentication requirements
- Rate limits
- Retry strategy

#### Error Handling
- Expected error cases
- Fallback behavior
- Logging requirements

#### Performance Considerations
- Caching strategy
- Batch processing requirements
- Memory constraints