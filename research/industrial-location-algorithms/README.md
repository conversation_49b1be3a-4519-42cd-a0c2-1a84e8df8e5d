# Industrial Discovery Inc. — Developer README

This document guides you through implementing each part of our codebase. Follow the instructions under each section to build out the modules needed for Prospecting, Underwriting, and Deal Closure.

---

## 📦 Project Layout

industrial_discovery/
├── api_clients/
├── models/
├── services/
├── prospecting/
├── underwriting/
├── deal_closure/
├── utils/
├── main.py
└── requirements.txt

markdown
Copy
Edit

---

## 1. `api_clients/`

**Purpose:** Wrap every external data source behind a uniform interface.

- **regrid_client.py**  
  - `fetch_parcel(parcel_id) → Parcel`  
  - Methods: `get_boundaries()`, `get_owner_info()`, `get_assessed_value()`
- **attom_client.py**  
  - `fetch_parcel_zoning(parcel_id) → ZoningData`
- **bls_client.py**  
  - `fetch_labor_stats(geo) → LaborMetrics`
- **eia_client.py**  
  - `fetch_energy_rates(region) → EnergyRates`
- **maps_client.py**  
  - `get_distance_matrix(coords, targets) → DistanceMetrics`  
  - `get_directions(coords, targets) → TravelTimeMetrics`
- **census_client.py**  
  - `fetch_demographics(tract) → Demographics`
- **incentives_client.py**  
  - `fetch_incentives(parcel_id) → IncentiveData`
- **dealpath_client.py**  
  - `fetch_deal(deal_id) → Deal`  
  - `update_stage(deal_id, stage)`, `list_tasks(deal_id)`
- **proprietary_db_client.py**  
  - `load_parcel(parcel_id) → Parcel`  
  - `save_results(table_name, records)`

> **To code:**  
> - Use `requests` or SDKs.  
> - Handle auth, retries, rate-limits.  
> - Parse JSON → Pydantic/dataclass models.

---

## 2. `models/`

**Purpose:** Define all domain entities and ensure type safety.

- **parcel.py** → `class Parcel(BaseModel):`  
  - fields: `id`, `lat`, `lng`, `land_use`, `lot_size`, `assessed_value`, …
- **labor.py** → `class LaborMetrics(BaseModel):`  
  - fields: `median_wage`, `unemployment_rate`, `labor_force_count`
- **energy.py** → `class EnergyRates(BaseModel):`  
  - `electricity_rate`, `gas_rate`, `water_rate`
- **transport.py** → `class TransportMetrics(BaseModel):`  
  - `distance_to_highway`, `drive_time_to_port`, `annual_vmt`
- **demographics.py** → `class Demographics(BaseModel):`  
  - `population_density`, `median_income`, `pct_college`
- **incentives.py** → `class IncentiveData(BaseModel):`  
  - `tax_rate`, `abatement_years`, `programs: List[str]`
- **deal.py** → `class Deal(BaseModel):`  
  - `id`, `current_stage`, `tasks: List[Task]`, `documents: List[Document]`

> **To code:**  
> - Use Pydantic for validation.  
> - Include `from_dict()` and `to_dict()` utilities as needed.

---

## 3. `services/`

**Purpose:** High-level workflows that tie together API clients and domain logic.

- **prospecting_service.py**  
  - `def run_prospect(parcel_id):`  
    1. Load base parcel via proprietary or Regrid client  
    2. Enrich owner info (`parcel_prospecting`)  
    3. Compute overlays (`strategic_value`)  
    4. Return `ProspectResult` model
- **underwriting_service.py**  
  - `def run_underwriting(parcel_id):`  
    1. Load parcel  
    2. Fetch labor, energy, demographics, incentives  
    3. Normalize inputs (`normalize_inputs.py`)  
    4. Run cashflow, ratios, sensitivity  
    5. Return `UnderwritingResult`
- **deal_closure_service.py**  
  - `def run_deal_closure(deal_id):`  
    1. Fetch deal from Dealpath/Salesforce  
    2. Advance pipeline, create tasks, generate docs, compliance  
    3. Return `DealClosureResult`

> **To code:**  
> - Import only clients and pure-logic modules.  
> - Assemble inputs, call domain functions, wrap outputs in result models.

---

## 4. `prospecting/`

**Purpose:** Pure-logic functions for parcel discovery and evaluation.

- **parcel_prospecting.py**  
  - `def obtain_contact(parcel: Parcel) -> Parcel`: attach `owner_contact`
- **strategic_value.py**  
  - `def compute_overlays(parcel: Parcel) -> dict`: distance, travel-time, risk layers
- **negotiate_listing.py**  
  - `def flag_exclusive(parcel: Parcel) -> Parcel`: set `exclusive_flag`, timestamp

> **To code:**  
> - Functions take `Parcel` → return enriched `Parcel`.  
> - No I/O or API calls—pure transforms.

---

## 5. `underwriting/`

**Purpose:** Financial models and sensitivity analyses.

- **normalize_inputs.py**  
  - `def merge_inputs(parcel, labor, energy, demo, incentives) -> Dict[str, DataFrame]`
- **calc_cashflow.py**  
  - `def compute_cashflow(inputs) -> CashflowResult`
- **calc_ratios.py**  
  - `def compute_ratios(cashflow) -> RatioResult`
- **sensitivity.py**  
  - `def run_stress_tests(inputs) -> SensitivityReport`

> **To code:**  
> - Use NumPy/Pandas for calculations.  
> - Keep functions pure—inputs in, results out.

---

## 6. `deal_closure/`

**Purpose:** Manage the final transactional workflow.

- **pipeline.py**  
  - `def advance_stage(deal: Deal) -> Deal`
- **tasks.py**  
  - `def sync_tasks(deal: Deal) -> List[Task]`
- **documents.py**  
  - `def generate_docs(deal: Deal) -> List[Document]`
- **compliance.py**  
  - `def verify_compliance(deal: Deal) -> ComplianceResult`

> **To code:**  
> - Each module transforms or updates a `Deal` model.  
> - No direct HTTP—clients are in `api_clients`.

---

## 7. `utils/`

- **config.py**: load `.env` or YAML for API keys, endpoints, DB URIs  
- **logging.py**: set up `structlog` or Python `logging` with JSON output  

> **To code:**  
> - Centralize all configuration and logging setups here.

---

## 8. `main.py`

**Purpose:** CLI / batch runner entrypoint.

- Parse arguments (`parcel_id` / `deal_id` / job file)  
- Call `services.prospecting_service.run_prospect(...)`, etc.  
- Handle global errors, write outputs to DB or files  

> **To code:**  
> - Use `argparse` or `click`.  
> - Provide `--mode prospect | underwrite | close` and relevant IDs.

---

## 9. `requirements.txt`

- Pin versions for:  
  - `pydantic`, `requests`, `numpy`, `pandas`, `click`, `structlog`, etc.

> **To code:**  
> - Freeze after initial install: `pip freeze > requirements.txt`

---

Follow these instructions to implement each branch. As you build out modules, write unit tests targeting pure-logic functions and mock API calls in `api_clients/`. This structure will keep your codebase maintainable, testable, and ready to integrate new proprietary data sources at will.







