"""
Underwriting service for financial analysis and investment evaluation
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import numpy as np

from models.parcel import Parcel
from models.labor import LaborMetrics
from models.energy import EnergyRates
from models.demographics import Demographics
from models.incentives import IncentiveData
from api_clients.regrid_client import RegridClient
from api_clients.bls_client import BLSClient
from api_clients.eia_client import EIAClient
from api_clients.census_client import CensusClient
from api_clients.incentives_client import IncentivesClient
from underwriting.normalize_inputs import merge_inputs
from underwriting.calc_cashflow import compute_cashflow
from underwriting.calc_ratios import compute_ratios
from underwriting.sensitivity import run_stress_tests
from utils.config import get_api_key


class UnderwritingResult(BaseModel):
    """Result of underwriting analysis with monetized outputs"""
    parcel_id: str
    
    # Financial metrics (dollar values)
    npv: float  # Net Present Value
    irr: float  # Internal Rate of Return
    total_project_cost: float
    total_revenue_10_year: float
    annual_noi: float  # Net Operating Income
    cash_on_cash_return: float
    
    # Debt service metrics
    annual_debt_service: float
    min_dscr: float  # Debt Service Coverage Ratio
    average_dscr: float
    
    # Cost breakdowns (dollar amounts)
    land_cost: float
    development_cost: float
    soft_costs: float
    financing_costs: float
    
    # Revenue projections
    rental_income_year_1: float
    rental_income_year_10: float
    
    # Tax benefits and incentives (dollar values)
    tax_incentive_value: float
    depreciation_benefit: float
    
    # Operating costs (annual dollars)
    property_taxes: float
    insurance: float
    utilities: float
    maintenance: float
    management_fees: float
    
    # Sensitivity analysis
    best_case_npv: float
    worst_case_npv: float
    break_even_rent: float  # $/sqft
    
    # Investment recommendation
    recommendation: str
    roi_summary: Dict[str, float]
    
    def to_dict(self) -> dict:
        return self.model_dump()


class UnderwritingService:
    """Comprehensive underwriting service with real financial calculations"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Initialize API clients with fallback to mock data
        self.regrid_client = RegridClient(
            api_key=get_api_key(config, "regrid")
        ) if config.get("api_keys", {}).get("regrid") else None
        
        self.bls_client = BLSClient(
            api_key=config.get("api_keys", {}).get("bls")
        )
        
        self.eia_client = EIAClient(
            api_key=get_api_key(config, "eia")
        ) if config.get("api_keys", {}).get("eia") else None
        
        self.census_client = CensusClient(
            api_key=config.get("api_keys", {}).get("census")
        )
        
        self.incentives_client = IncentivesClient()
    
    def run_underwriting(self, parcel_id: str) -> UnderwritingResult:
        """Run comprehensive financial underwriting analysis"""
        
        # 1. Load parcel data
        if self.regrid_client:
            parcel = self.regrid_client.fetch_parcel(parcel_id)
        else:
            parcel = self._create_mock_parcel(parcel_id)
        
        # 2. Gather market data
        labor_data = self.bls_client.fetch_labor_stats(f"{parcel.city}, {parcel.state}")
        
        if self.eia_client:
            energy_data = self.eia_client.fetch_energy_rates(parcel.state or "TX")
        else:
            energy_data = EnergyRates(electricity_rate=0.12, region="TX")
        
        demographics = self.census_client.fetch_demographics("123", parcel.county, parcel.state)
        
        incentives = self.incentives_client.fetch_incentives(
            parcel_id, 
            {"city": parcel.city, "county": parcel.county, "state": parcel.state}
        )
        
        # 3. Normalize and merge inputs
        inputs = merge_inputs(parcel, labor_data, energy_data, demographics, incentives)
        
        # 4. Perform financial calculations
        cashflow_result = compute_cashflow(inputs)
        ratios_result = compute_ratios(cashflow_result)
        sensitivity_result = run_stress_tests(inputs)
        
        # 5. Calculate tax incentive value
        project_details = {
            "assessed_value": parcel.assessed_value or 10000000,
            "jobs_created": 75,  # Estimated for industrial development
            "investment_amount": inputs["development"]["total_project_cost"]
        }
        tax_benefits = self.incentives_client.calculate_incentive_value(incentives, project_details)
        
        # 6. Generate recommendation
        recommendation = self._generate_recommendation(cashflow_result, ratios_result)
        
        return UnderwritingResult(
            parcel_id=parcel_id,
            npv=cashflow_result["npv"],
            irr=cashflow_result["irr"],
            total_project_cost=inputs["development"]["total_project_cost"],
            total_revenue_10_year=cashflow_result["total_revenue"],
            annual_noi=cashflow_result["year_1_noi"],
            cash_on_cash_return=ratios_result["cash_on_cash_return"],
            annual_debt_service=cashflow_result["annual_debt_service"],
            min_dscr=ratios_result["min_dscr"],
            average_dscr=ratios_result["average_dscr"],
            land_cost=inputs["costs"]["land_cost"],
            development_cost=inputs["costs"]["development_cost"],
            soft_costs=inputs["costs"]["soft_costs"],
            financing_costs=inputs["costs"]["financing_costs"],
            rental_income_year_1=cashflow_result["year_1_rental_income"],
            rental_income_year_10=cashflow_result["year_10_rental_income"],
            tax_incentive_value=tax_benefits["total_incentive_value"],
            depreciation_benefit=cashflow_result["total_depreciation_benefit"],
            property_taxes=inputs["operating"]["property_taxes"],
            insurance=inputs["operating"]["insurance"],
            utilities=inputs["operating"]["utilities"],
            maintenance=inputs["operating"]["maintenance"],
            management_fees=inputs["operating"]["management"],
            best_case_npv=sensitivity_result["best_case_npv"],
            worst_case_npv=sensitivity_result["worst_case_npv"],
            break_even_rent=sensitivity_result["break_even_rent"],
            recommendation=recommendation,
            roi_summary={
                "npv": cashflow_result["npv"],
                "irr": cashflow_result["irr"],
                "cash_on_cash": ratios_result["cash_on_cash_return"],
                "total_return_multiple": cashflow_result["total_return_multiple"],
                "payback_period_years": ratios_result["payback_period"]
            }
        )
    
    def _create_mock_parcel(self, parcel_id: str) -> Parcel:
        """Create mock parcel for testing"""
        return Parcel(
            id=parcel_id,
            lat=32.7767,
            lng=-96.7970,
            land_use="industrial",
            lot_size=250000,  # 250k sqft for industrial development
            assessed_value=8000000,  # $8M assessed value
            address="456 Industrial Blvd",
            city="Dallas",
            county="Dallas County", 
            state="TX",
            zip_code="75201",
            zoning_code="I-1",
            owner_name="Industrial Holdings LLC",
            market_value=12000000,  # $12M market value
            metadata={"source": "mock_underwriting_data"}
        )
    
    def _generate_recommendation(self, cashflow_result: Dict, ratios_result: Dict) -> str:
        """Generate investment recommendation based on financial metrics"""
        
        npv = cashflow_result["npv"]
        irr = cashflow_result["irr"]
        min_dscr = ratios_result["min_dscr"]
        
        if npv > 2000000 and irr > 0.15 and min_dscr > 1.25:
            return "STRONG BUY - Excellent returns with strong cash flow coverage"
        elif npv > 1000000 and irr > 0.12 and min_dscr > 1.15:
            return "BUY - Good investment opportunity with acceptable risk"
        elif npv > 0 and irr > 0.10 and min_dscr > 1.10:
            return "CONSIDER - Marginal returns, evaluate alternatives"
        else:
            return "PASS - Returns below threshold, high risk" 