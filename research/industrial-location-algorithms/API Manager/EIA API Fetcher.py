import time
import logging
from typing import List, Dict, Optional, Tuple, Any
from eia_client import EIAClient, EIAClientError

"""
Fetch monthly electricity sales (MWh) for commercial and industrial sectors by state.

Usage:
    python fetch_eia_utility_sales.py
"""

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('eia_fetcher.log')
    ]
)
logger = logging.getLogger(__name__)

def fetch_state_sales():
    """Fetch and display electricity sales data for all states and sectors."""
    client = EIAClient()
    # All U.S. states + DC
    states = [
        'AL','AK','AZ','AR','CA','CO','CT','DE','FL','GA','HI','ID','IL','IN','IA','KS','KY','LA',
        'ME','MD','MA','MI','MN','MS','MO','MT','NE','NV','NH','NJ','NM','NY','NC','ND','OH','OK',
        'OR','PA','RI','SC','SD','TN','TX','UT','VT','VA','WA','WI','WV','WY','DC'
    ]
    sectors = {'COM': 'Commercial', 'IND': 'Industrial'}

    results: List[Dict[str, Optional[float]]] = []

    print("\n⚡ EIA Electricity Sales by State and Sector (Monthly, MWh)")
    print("=" * 70)
    print(f"{'State':<5} {'Sector':<12} {'Sales (MWh)':>15}")
    print("-" * 70)

    for state in states:
        for sector_code, sector_name in sectors.items():
            try:
                # Attempt to fetch sales data
                sales = client.get_latest_sales_mwh(state, sector_code)
                
                # Handle None result (API-level failure)
                if sales is None:
                    logger.warning(f"No data available for {state} - {sector_name}")
                    sales_str = "N/A"
                else:
                    sales_str = f"{sales:,.0f}"
                    
                print(f"{state:<5} {sector_name:<12} {sales_str:>15}")
                
            except Exception as e:
                # Log unexpected errors (non-API failures)
                logger.error(f"Unexpected error fetching data for {state} - {sector_name}: {str(e)}")
                print(f"{state:<5} {sector_name:<12} {'ERROR':>15}")
                sales = None
                
            results.append({
                'state': state,
                'sector': sector_name,
                'sales': sales
            })
            
            # Rate limiting
            time.sleep(0.5)

    # Summary statistics
    print("\n" + "=" * 70)
    print("SUMMARY STATISTICS")
    print("=" * 70)

    valid_sales = [r['sales'] for r in results if r['sales'] is not None]
    if valid_sales:
        avg_sales = sum(valid_sales) / len(valid_sales)
        max_sales = max(valid_sales)
        min_sales = min(valid_sales)

        print(f"Average monthly sales: {avg_sales:,.0f} MWh")
        print(f"Range: {min_sales:,.0f} - {max_sales:,.0f} MWh")
        print(f"Data points: {len(valid_sales)}/{len(results)}")

        # Identify highest and lowest
        highest = [r for r in results if r['sales'] == max_sales]
        lowest = [r for r in results if r['sales'] == min_sales]

        print("\nHighest sales:")
        for entry in highest:
            print(f"  • {entry['state']} ({entry['sector']}): {entry['sales']:,.0f} MWh")

        print("\nLowest sales:")
        for entry in lowest:
            print(f"  • {entry['state']} ({entry['sector']}): {entry['sales']:,.0f} MWh")

    else:
        logger.warning("No valid sales data retrieved for any state/sector combination")
        print("No valid sales data retrieved.")

    # Log summary of failures
    failed_states = [(r['state'], r['sector']) for r in results if r['sales'] is None]
    if failed_states:
        logger.warning(f"Failed to retrieve data for {len(failed_states)} state/sector combinations:")
        for state, sector in failed_states:
            logger.warning(f"  - {state} ({sector})")


def main():
    logger.info("🎯 Starting EIA utility sales data fetch...")
    try:
        fetch_state_sales()
        logger.info("✅ EIA data collection complete!")
    except Exception as e:
        logger.error(f"❌ Critical error during data fetch: {str(e)}", exc_info=True)
        raise


if __name__ == "__main__":
    main()
