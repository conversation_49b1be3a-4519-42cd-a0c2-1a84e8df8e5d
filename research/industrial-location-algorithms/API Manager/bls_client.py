import requests
from functools import lru_cache
from datetime import datetime
from typing import Dict, Optional, List, Set
import time
import json

import config

class BLSClientError(Exception):
    """Custom exception for BLS API client errors."""
    pass

class BLSClient:
    """Client for fetching BLS (Bureau of Labor Statistics) data."""
    
    API_URL = "https://api.bls.gov/publicAPI/v2/timeseries/data/"
    
    def __init__(self, api_key: str = None):
        """Initialize the BLS client with optional API key."""
        self.api_key = api_key or config.BLS_API_KEY
        
    @lru_cache(maxsize=128)
    def fetch_series_data(
        self,
        series_id: str,
        start_year: Optional[int] = None,
        end_year: Optional[int] = None
    ) -> Dict:
        """
        Fetch data for a given BLS series ID.
        
        Args:
            series_id: BLS series identifier
            start_year: Start year for data (defaults to last year)
            end_year: End year for data (defaults to current year)
            
        Returns:
            Dict containing the series data
            
        Raises:
            BLSClientError: If the API request fails
        """
        # Determine year range
        this_year = datetime.now().year
        end_year = end_year or this_year
        start_year = start_year or (end_year - 1)
        
        payload = {
            "seriesid": [series_id],
            "startyear": str(start_year),
            "endyear": str(end_year),
            "registrationKey": self.api_key
        }
        
        try:
            resp = requests.post(self.API_URL, json=payload, timeout=config.REQUEST_TIMEOUT_SECONDS)
            resp.raise_for_status()
            data = resp.json()
            
            if data.get("status") == "REQUEST_FAILED":
                raise BLSClientError(f"API request failed: {data.get('message', 'Unknown error')}")
                
            return data
            
        except requests.exceptions.RequestException as e:
            raise BLSClientError(f"Failed to fetch data: {str(e)}")
    
    def get_latest_value(self, series_id: str) -> Optional[float]:
        """
        Get the most recent value for a series, prioritizing quarterly data.
        
        Args:
            series_id: BLS series identifier
            
        Returns:
            float: The most recent value, or None if not available
        """
        try:
            data = self.fetch_series_data(series_id)
            series = data.get("Results", {}).get("series", [])[0]
            data_points = series.get("data", [])
            
            if not data_points:
                return None
            
            # Sort data points by year and period to get most recent
            # Prioritize quarterly data (Q01-Q04) over annual (M13)
            quarterly_points = [dp for dp in data_points if dp.get("period", "").startswith("Q")]
            annual_points = [dp for dp in data_points if dp.get("period", "") == "M13"]
            other_points = [dp for dp in data_points if not dp.get("period", "").startswith("Q") and dp.get("period", "") != "M13"]
            
            # Use the most recent quarterly data if available
            if quarterly_points:
                latest = quarterly_points[0]  # Data is typically sorted by recency
            elif annual_points:
                latest = annual_points[0]
            elif other_points:
                latest = other_points[0]
            else:
                latest = data_points[0]
            
            return float(latest.get("value"))
            
        except (IndexError, KeyError, ValueError, BLSClientError) as e:
            print(f"Error fetching value for series {series_id}: {str(e)}")
            return None
    
    def discover_all_bls_surveys(self) -> Dict[str, List[Dict]]:
        """
        Systematically explore BLS API to discover all available survey types and series.
        Tests major BLS survey prefixes to see what data is available.
        """
        print("🔍 DISCOVERING ALL AVAILABLE BLS SURVEYS")
        print("="*80)
        
        # Major BLS survey prefixes and their descriptions
        survey_patterns = {
            # Employment & Unemployment
            "LNS": "Labor Force Statistics (Current Population Survey)",
            "LNU": "Labor Force Statistics - Unemployment", 
            "LNS14": "Unemployment Rate",
            "CES": "Current Employment Statistics (Establishment Survey)",
            "CEU": "Current Employment Statistics - Private",
            "CFS": "Job Openings and Labor Turnover Survey",
            "JTS": "Job Openings and Labor Turnover",
            
            # Wages & Compensation  
            "CMU": "Employer Costs for Employee Compensation (ECEC)",
            "CWU": "Real Earnings", 
            "ECI": "Employment Cost Index",
            "LEU": "National Compensation Survey",
            "NBD": "National Compensation Survey - Benefits",
            "OCU": "Occupational Employment Statistics",
            "OEU": "Occupational Employment and Wage Statistics",
            
            # Prices & Inflation
            "CPI": "Consumer Price Index",
            "CPU": "Consumer Price Index - Urban",
            "APU": "Average Price Data",
            "PPI": "Producer Price Index",
            "PPU": "Producer Price Index",
            "IPU": "Import/Export Price Indexes",
            
            # Productivity  
            "PRS": "Productivity and Costs",
            "PRU": "Productivity - Major Sectors",
            "MPU": "Multifactor Productivity",
            
            # Industry Data
            "IPN": "Industrial Production",
            "SMU": "State and Metro Area Employment",
            "SAU": "State and Area Unemployment",
            "LAU": "Local Area Unemployment",
            "MSU": "Metropolitan Statistical Area",
            
            # Safety & Injuries
            "IIF": "Injuries, Illnesses, and Fatalities",
            "SHU": "Survey of Occupational Injuries & Illnesses",
            "CFI": "Census of Fatal Occupational Injuries",
            
            # International
            "ICU": "International Comparisons",
            "FLU": "Foreign Labor Statistics",
        }
        
        survey_results = {}
        
        for prefix, description in survey_patterns.items():
            print(f"\n📊 Testing {prefix}: {description}")
            print("-" * 60)
            
            # Test common series patterns for each prefix
            test_series = []
            
            if prefix in ["LNS", "LNU"]:
                # Labor force patterns
                test_series = [
                    f"{prefix}14000000",  # Unemployment rate
                    f"{prefix}12000000",  # Labor force
                    f"{prefix}11000000",  # Civilian labor force
                ]
            elif prefix in ["CES", "CEU"]:
                # Employment patterns  
                test_series = [
                    f"{prefix}0000000001",  # Total nonfarm
                    f"{prefix}0500000001",  # Private
                    f"{prefix}6054000001",  # Professional services
                ]
            elif prefix == "CMU":
                # ECEC patterns - using CMU2 for private industry
                test_series = [
                    f"{prefix}2010000000000D",  # Total compensation - private industry
                    f"{prefix}2020000000000D",  # Wages only - private industry
                    f"{prefix}2010000000210D",  # Northeast region total comp
                    f"{prefix}2010000000240D",  # West region total comp
                ]
            elif prefix in ["CPI", "CPU"]:
                # Price index patterns
                test_series = [
                    f"{prefix}U000SA0",    # All items
                    f"{prefix}U000SAF1",   # Food
                    f"{prefix}U000SAH1",   # Housing
                ]
            elif prefix in ["PPI", "PPU"]:
                # Producer price patterns
                test_series = [
                    f"{prefix}FD4A01",     # Final demand
                    f"{prefix}ITSI01",     # Intermediate demand
                ]
            else:
                # Generic patterns for other surveys
                test_series = [
                    f"{prefix}0000000000",
                    f"{prefix}00000001", 
                    f"{prefix}1000000000",
                    f"{prefix}U000000000",
                ]
            
            # Test each series pattern
            found_series = []
            for series_id in test_series:
                try:
                    data = self.fetch_series_data(series_id)
                    series_list = data.get("Results", {}).get("series", [])
                    
                    if series_list and len(series_list) > 0:
                        series_data = series_list[0]
                        data_points = series_data.get("data", [])
                        
                        if data_points:
                            latest = data_points[0] 
                            found_series.append({
                                "series_id": series_id,
                                "title": series_data.get("title", "Unknown"),
                                "latest_value": latest.get("value"),
                                "latest_year": latest.get("year"),
                                "latest_period": latest.get("period"),
                                "data_count": len(data_points)
                            })
                            print(f"✓ {series_id}: {series_data.get('title', 'No title')}")
                            print(f"   Latest: {latest.get('value')} ({latest.get('year')}-{latest.get('period')})")
                        else:
                            print(f"⚠ {series_id}: Found series but no data")
                    else:
                        print(f"✗ {series_id}: Not found")
                        
                except BLSClientError as e:
                    print(f"✗ {series_id}: Error - {str(e)}")
                
                # Rate limiting
                time.sleep(0.3)
            
            if found_series:
                survey_results[prefix] = {
                    "description": description,
                    "series_found": found_series,
                    "count": len(found_series)
                }
            
            print(f"Found {len(found_series)} working series for {prefix}")
        
        return survey_results
    
    def discover_available_series(self, series_patterns: List[str]) -> Dict[str, Dict]:
        """
        Test multiple series patterns to discover what data is actually available.
        
        Args:
            series_patterns: List of series ID patterns to test
            
        Returns:
            Dict mapping series IDs to their available data info
        """
        available_series = {}
        
        for series_id in series_patterns:
            try:
                print(f"Testing series: {series_id}")
                data = self.fetch_series_data(series_id)
                
                # Check if we got actual data
                series_list = data.get("Results", {}).get("series", [])
                if series_list and len(series_list) > 0:
                    series_data = series_list[0]
                    data_points = series_data.get("data", [])
                    
                    if data_points:
                        latest = data_points[0]
                        available_series[series_id] = {
                            "status": "available",
                            "latest_value": latest.get("value"),
                            "latest_year": latest.get("year"),
                            "latest_period": latest.get("period"),
                            "data_points_count": len(data_points),
                            "series_title": series_data.get("title", "Unknown")
                        }
                        print(f"✓ Found data: {series_data.get('title', 'No title')}")
                    else:
                        available_series[series_id] = {
                            "status": "no_data",
                            "series_title": series_data.get("title", "Unknown")
                        }
                        print(f"⚠ No data points for: {series_data.get('title', 'No title')}")
                else:
                    available_series[series_id] = {"status": "not_found"}
                    print(f"✗ Series not found")
                    
            except BLSClientError as e:
                available_series[series_id] = {"status": "error", "error": str(e)}
                print(f"✗ Error: {str(e)}")
            
            # Rate limiting
            time.sleep(0.5)
        
        return available_series

# Official BLS ECEC subcell codes - restrict to only these valid codes
VALID_ECEC_SUBCELLS = {
    # National
    "000": "National",
    
    # Census Regions  
    "210": "Northeast Region",
    "220": "South Region", 
    "230": "Midwest Region",
    "240": "West Region",
    
    # Census Divisions
    "211": "New England",
    "212": "Middle Atlantic",
    "221": "South Atlantic", 
    "222": "East South Central",
    "223": "West South Central",
    "231": "East North Central",
    "232": "West North Central", 
    "241": "Mountain",
    "242": "Pacific",
    
    # Major Metropolitan Areas (CSAs)
    "0LG": "Los Angeles CSA",
    "0LK": "San Francisco CSA", 
    "0LL": "Seattle CSA",
    "0LC": "Chicago CSA",
    "0LD": "Washington DC CSA",
    "0LE": "Boston CSA",
    "0LF": "New York CSA",
    "0LH": "Philadelphia CSA",
    "0LI": "Detroit CSA",
    "0LJ": "Atlanta CSA"
}

def generate_ecec_series_id(subcell: str, metric: str) -> str:
    """
    Generate a valid ECEC series ID for the given subcell and metric.
    Uses CMU2 for private industry data and validates subcell codes.
    
    Args:
        subcell: 3-character geographic subcell code (must be in VALID_ECEC_SUBCELLS)
        metric: Either "total" for total compensation or "wages" for wages only
        
    Returns:
        str: Valid ECEC series ID
        
    Raises:
        ValueError: If metric is not "total" or "wages", or subcell is invalid
    """
    # Validate subcell code
    if subcell not in VALID_ECEC_SUBCELLS:
        valid_codes = ", ".join(sorted(VALID_ECEC_SUBCELLS.keys()))
        raise ValueError(f"Invalid subcell code '{subcell}'. Valid codes are: {valid_codes}")
    
    # Validate metric
    if metric == "total":
        estimate_code = "01"
    elif metric == "wages":
        estimate_code = "02"
    else:
        raise ValueError(f"Metric must be 'total' or 'wages', got '{metric}'")
    
    # Format: CMU2 + estimate + industry(0000) + occupation(000) + subcell + datatype(D)
    # Using CMU2 for private industry (not CMU1 for civilian)
    series_id = f"CMU2{estimate_code}0000000{subcell}D"
    
    # Verify the series ID is exactly 17 characters
    if len(series_id) != 17:
        raise ValueError(f"Generated series ID '{series_id}' is not 17 characters (got {len(series_id)})")
    
    return series_id

def fetch_fully_loaded_hourly(
    oews_series: Optional[str] = None,
    total_comp_series: Optional[str] = None, 
    wages_only_series: Optional[str] = None
) -> Optional[float]:
    """
    Unified function to fetch fully-loaded hourly compensation rate.
    
    This function tries multiple data sources in order of preference:
    1. ECEC total compensation data (most accurate)
    2. ECEC wages-only data with benefit multiplier
    3. OEWS data with benefit multiplier (fallback)
    
    Args:
        oews_series: OEWS series ID for base wage data
        total_comp_series: ECEC total compensation series ID
        wages_only_series: ECEC wages only series ID
        
    Returns:
        float: Fully-loaded hourly rate, or None if data unavailable
    """
    client = BLSClient()
    
    try:
        # First priority: ECEC total compensation data (most accurate)
        if total_comp_series:
            total_comp = client.get_latest_value(total_comp_series)
            if total_comp is not None:
                print(f"Using ECEC total compensation: ${total_comp:.2f}/hr from {total_comp_series}")
                return total_comp
        
        # Second priority: ECEC wages only with benefit multiplier
        if wages_only_series:
            wages = client.get_latest_value(wages_only_series)
            if wages is not None:
                # Calculate benefit ratio if we have both series
                if total_comp_series:
                    total_comp = client.get_latest_value(total_comp_series)
                    if total_comp is not None and wages > 0:
                        # Use actual ECEC benefit ratio
                        benefit_ratio = total_comp / wages
                        fully_loaded = wages * benefit_ratio
                        print(f"Using ECEC wages with actual benefit ratio ({benefit_ratio:.2f}): ${fully_loaded:.2f}/hr")
                        return fully_loaded
                
                # Fall back to config multiplier
                fully_loaded = wages * config.FULLY_LOADED_LABOR_MULTIPLIER
                print(f"Using ECEC wages with config multiplier ({config.FULLY_LOADED_LABOR_MULTIPLIER}): ${fully_loaded:.2f}/hr from {wages_only_series}")
                return fully_loaded
        
        # Third priority: OEWS data with benefit multiplier (fallback)
        if oews_series:
            oews_wage = client.get_latest_value(oews_series)
            if oews_wage is not None:
                # Convert annual to hourly if needed
                if oews_wage > 1000:  # Assume annual if > $1000
                    hourly_wage = oews_wage / config.DEFAULT_WORK_HOURS_PER_YEAR
                else:
                    hourly_wage = oews_wage
                
                fully_loaded = hourly_wage * config.FULLY_LOADED_LABOR_MULTIPLIER
                print(f"Using OEWS fallback with multiplier: ${fully_loaded:.2f}/hr from {oews_series}")
                return fully_loaded
        
        print("No data available from any source")
        return None
        
    except BLSClientError as e:
        print(f"Error fetching fully loaded hourly rate: {str(e)}")
        return None

def format_dollars(value: float) -> str:
    """Format a dollar value with 2 decimal places."""
    return f"${value:.2f}"

def format_percent(value: float) -> str:
    """Format a percentage value with 1 decimal place."""
    return f"{value:.1f}%"

def print_survey_results(results: Dict[str, Dict]):
    """Pretty print the survey discovery results."""
    print("\n" + "="*100)
    print("BLS SURVEY DISCOVERY RESULTS")
    print("="*100)
    
    total_surveys = len(results)
    total_series = sum(survey['count'] for survey in results.values())
    
    print(f"Found {total_surveys} survey types with {total_series} working series")
    print("="*100)
    
    for prefix, survey_data in results.items():
        print(f"\n📊 {prefix}: {survey_data['description']}")
        print(f"Found {survey_data['count']} working series:")
        print("-" * 80)
        
        for series in survey_data['series_found']:
            print(f"  ✓ {series['series_id']}: {series['title']}")
            print(f"    Latest: {series['latest_value']} ({series['latest_year']}-{series['latest_period']}) | {series['data_count']} data points")

def print_valid_subcells():
    """Print all valid ECEC subcell codes for reference."""
    print("\n📋 VALID ECEC SUBCELL CODES:")
    print("="*50)
    for code, description in sorted(VALID_ECEC_SUBCELLS.items()):
        print(f"  {code}: {description}")

if __name__ == "__main__":
    client = BLSClient()
    
    print("🎯 BLS CLIENT WITH CORRECTED ECEC HANDLING")
    print("="*80)
    
    # Show valid subcell codes
    print_valid_subcells()
    
    # Test the corrected ECEC series generation
    print("\n🧪 TESTING CORRECTED ECEC SERIES GENERATION:")
    print("-" * 60)
    
    test_subcells = ["000", "210", "240", "0LG"]  # National, Northeast, West, LA CSA
    
    for subcell in test_subcells:
        try:
            total_series = generate_ecec_series_id(subcell, "total")
            wages_series = generate_ecec_series_id(subcell, "wages")
            
            print(f"\n{VALID_ECEC_SUBCELLS[subcell]} ({subcell}):")
            print(f"  Total Comp: {total_series}")
            print(f"  Wages Only: {wages_series}")
            
            # Test fetching
            rate = fetch_fully_loaded_hourly(
                total_comp_series=total_series,
                wages_only_series=wages_series
            )
            
            if rate:
                print(f"  ✅ Rate: ${rate:.2f}/hr")
            else:
                print(f"  ❌ No data available")
                
        except ValueError as e:
            print(f"  ❌ Error: {e}")
    
    # Discover all available BLS surveys
    print(f"\n🔍 DISCOVERING ALL BLS SURVEYS...")
    survey_results = client.discover_all_bls_surveys()
    
    # Print formatted results
    print_survey_results(survey_results)
    
    # Save results to file for reference
    with open("bls_survey_discovery.json", "w") as f:
        json.dump(survey_results, f, indent=2)
    
    print(f"\n💾 Results saved to 'bls_survey_discovery.json'")
    print("🎯 Use the series IDs above in your applications!")
