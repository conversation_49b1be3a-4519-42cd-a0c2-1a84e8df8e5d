const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config({ path: require('path').resolve(__dirname, '../.env') });
const fs = require('fs');
const path = require('path');
const WebSearchService = require('./webSearchService');
const ContextEngine = require('./contextEngine');
const SearchOptimizer = require('./searchOptimizer');

// 简单的内存向量存储
class MemoryVectorStore {
  constructor() {
    this.documents = [];
    this.embeddings = [];
    this.metadatas = [];
    this.ids = [];
  }

  async add(documents, embeddings, metadatas, ids) {
    this.documents.push(...documents);
    this.embeddings.push(...embeddings);
    this.metadatas.push(...metadatas);
    this.ids.push(...ids);
  }

  async query(queryEmbedding, nResults = 5) {
    if (this.embeddings.length === 0) {
      return { documents: [[]], metadatas: [[]], distances: [[]] };
    }

    // 计算余弦相似度
    const similarities = this.embeddings.map(embedding => {
      return this.cosineSimilarity(queryEmbedding, embedding);
    });

    // 获取最相似的结果
    const indices = similarities
      .map((sim, idx) => ({ sim, idx }))
      .sort((a, b) => b.sim - a.sim)
      .slice(0, nResults)
      .map(item => item.idx);

    return {
      documents: [indices.map(idx => this.documents[idx])],
      metadatas: [indices.map(idx => this.metadatas[idx])],
      distances: [indices.map(idx => 1 - similarities[idx])]
    };
  }

  cosineSimilarity(a, b) {
    const dotProduct = a.reduce((sum, val, i) => sum + val * b[i], 0);
    const magnitudeA = Math.sqrt(a.reduce((sum, val) => sum + val * val, 0));
    const magnitudeB = Math.sqrt(b.reduce((sum, val) => sum + val * val, 0));
    return dotProduct / (magnitudeA * magnitudeB);
  }

  getCount() {
    return this.documents.length;
  }
}

/**
 * RAG 智能助手服务
 * 实现检索增强生成功能，为 Industrial Geo Dev 项目提供智能问答
 */
class RAGService {
  constructor() {
    this.chroma = null;
    this.collection = null;
    this.gemini = null;
    this.embeddingModel = null;
    this.isInitialized = false;
    this.knowledgeBaseBuilt = false;
    this.currentModel = null;
    this.modelFailures = new Map();

    // 初始化网络搜索服务
    this.webSearchService = new WebSearchService();

    // 初始化Context Engine
    this.contextEngine = new ContextEngine();

    // 初始化搜索优化器
    this.searchOptimizer = new SearchOptimizer();

    // 使用内存向量存储
    this.vectorStore = new MemoryVectorStore();

    // 配置参数
    this.config = {
      collectionName: 'industrial_geo_knowledge',
      embeddingModel: 'text-embedding-004',
      generativeModel: 'gemini-1.5-flash',
      chunkSize: 1000,
      chunkOverlap: 200,
      maxRetrievalResults: 5
    };

    // 多模型配置
    this.modelConfig = [
      {
        name: 'gemini-1.5-flash',
        type: 'gemini',
        priority: 1,
        apiKey: process.env.GEMINI_API_KEY,
        available: !!process.env.GEMINI_API_KEY,
        cost: 'free',
        description: 'Google Gemini 1.5 Flash - 快速响应，有免费额度'
      },
      {
        name: 'deepseek/deepseek-r1:free',
        type: 'openrouter',
        priority: 2,
        apiKey: process.env.OPENROUTER_API_KEY,
        available: !!process.env.OPENROUTER_API_KEY,
        cost: 'free',
        description: 'DeepSeek R1 (Tairon) - 免费的推理模型，专业分析能力强',
        maxTokens: parseInt(process.env.DEEPSEEK_MAX_TOKENS) || 2048,
        temperature: parseFloat(process.env.DEEPSEEK_TEMPERATURE) || 0.3,
        timeout: parseInt(process.env.DEEPSEEK_TIMEOUT) || 15000
      }
    ];
  }

  /**
   * 初始化RAG服务
   */
  async initialize() {
    try {
      console.log('🚀 初始化 RAG 智能助手服务...');

      // 初始化AI模型
      await this.initializeModels();

      // 不再构建知识库，直接使用网络搜索和项目文档
      console.log('📁 准备项目文档访问功能...');

      this.isInitialized = true;
      console.log('✅ RAG 服务初始化完成');

    } catch (error) {
      console.error('❌ RAG 服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 初始化AI模型
   */
  async initializeModels() {
    console.log('🔧 初始化AI模型...');

    // 初始化Gemini
    if (process.env.GEMINI_API_KEY) {
      this.gemini = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
      this.embeddingModel = this.gemini.getGenerativeModel({ 
        model: this.config.embeddingModel 
      });
      console.log('✅ Gemini 客户端初始化成功');
    }

    // 选择最佳可用模型
    this.currentModel = await this.selectBestModel();
    
    if (!this.currentModel) {
      console.warn('⚠️ 没有可用的AI模型，将使用离线模式');
    }
  }

  /**
   * 选择最佳可用模型
   */
  async selectBestModel() {
    const availableModels = this.modelConfig
      .filter(model => model.available && (model.working !== false))
      .sort((a, b) => a.priority - b.priority);

    for (const model of availableModels) {
      try {
        const isWorking = await this.testModel(model);
        if (isWorking) {
          console.log(`✅ ${model.name}: 可用`);
          return model;
        } else {
          console.log(`❌ ${model.name}: 不可用`);
          model.working = false;
        }
      } catch (error) {
        console.log(`❌ ${model.name}: 测试失败 - ${error.message}`);
        model.working = false;
      }
    }

    return null;
  }

  /**
   * 测试模型可用性
   */
  async testModel(model) {
    if (model.type === 'gemini') {
      if (!this.gemini) return false;
      
      try {
        const testModel = this.gemini.getGenerativeModel({ 
          model: model.name 
        });
        
        const testPrompt = "Hello";
        await Promise.race([
          testModel.generateContent(testPrompt),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('网络连接超时')), 10000)
          )
        ]);
        
        return true;
      } catch (error) {
        return false;
      }
    }
    
    return model.available;
  }

  // 知识库相关方法已移除，现在使用网络搜索和项目文档

  // createSampleDocuments 方法已移除，不再使用预设知识库

  /**
   * 收集项目文档
   */
  async collectDocuments(rootPath) {
    const documents = [];
    const excludePatterns = [
      'node_modules', '.git', 'dist', 'build', '.next',
      '__pycache__', '.pytest_cache', 'coverage',
      '.env', '.env.local', '.DS_Store', 'public/data'
    ];

    const includeExtensions = [
      '.md', '.txt', '.js', '.jsx', '.ts', '.tsx',
      '.py', '.json', '.yml', '.yaml'
    ];

    const walkDir = async (dirPath) => {
      try {
        const items = await fs.readdir(dirPath);
        
        for (const item of items) {
          const fullPath = path.join(dirPath, item);
          const relativePath = path.relative(rootPath, fullPath);
          
          // 跳过排除的目录和文件
          if (excludePatterns.some(pattern => relativePath.includes(pattern))) {
            continue;
          }

          const stat = await fs.stat(fullPath);
          
          if (stat.isDirectory()) {
            await walkDir(fullPath);
          } else if (stat.isFile()) {
            const ext = path.extname(item).toLowerCase();

            // 跳过过大的文件（超过1MB）
            if (stat.size > 1024 * 1024) {
              console.warn(`⚠️ 跳过大文件 ${relativePath} (${(stat.size / 1024 / 1024).toFixed(2)}MB)`);
              continue;
            }

            if (includeExtensions.includes(ext)) {
              try {
                const content = await fs.readFile(fullPath, 'utf-8');

                if (content.trim().length > 0 && content.length < 100000) { // 限制单个文件内容大小
                  documents.push({
                    content: content,
                    metadata: {
                      file_path: relativePath,
                      file_type: ext.slice(1),
                      file_size: stat.size,
                      last_modified: stat.mtime.toISOString()
                    }
                  });
                }
              } catch (readError) {
                console.warn(`⚠️ 无法读取文件 ${relativePath}:`, readError.message);
              }
            }
          }
        }
      } catch (error) {
        console.warn(`⚠️ 无法访问目录 ${dirPath}:`, error.message);
      }
    };

    await walkDir(rootPath);
    return documents;
  }

  /**
   * 分割文档为块
   */
  splitDocuments(documents) {
    const chunks = [];

    documents.forEach((doc, docIndex) => {
      const content = doc.content;
      const fileType = (doc.metadata?.file_type || '').toLowerCase();

      // 优先对 Markdown 文档按标题分段切块
      if (fileType === 'md') {
        const mdChunks = this.splitMarkdownDocument(content, {
          ...doc.metadata,
          doc_index: docIndex
        });
        chunks.push(...mdChunks);
        return;
      }

      // 常规按长度切块
      const chunkSize = this.config.chunkSize;
      const overlap = this.config.chunkOverlap;

      if (content.length <= chunkSize) {
        chunks.push({
          content: content,
          metadata: {
            ...doc.metadata,
            chunk_index: 0,
            total_chunks: 1,
            doc_index: docIndex
          }
        });
      } else {
        let start = 0;
        let chunkIndex = 0;

        while (start < content.length) {
          const end = Math.min(start + chunkSize, content.length);
          const chunkContent = content.slice(start, end);

          chunks.push({
            content: chunkContent,
            metadata: {
              ...doc.metadata,
              chunk_index: chunkIndex,
              doc_index: docIndex
            }
          });

          start = end - overlap;
          chunkIndex++;
        }
      }
    });

    return chunks;
  }

  /**
   * 基于 Markdown 标题的分段切块
   */
  splitMarkdownDocument(content, baseMetadata) {
    const lines = content.split('\n');
    const sections = [];
    let current = { title: '', body: [] };

    const flush = () => {
      if (current.body.length > 0) {
        sections.push({
          title: current.title,
          body: current.body.join('\n')
        });
      }
    };

    for (const line of lines) {
      if (/^#{1,6}\s+/.test(line)) {
        // 新标题开始，先导出前一段
        flush();
        current = { title: line.replace(/^#{1,6}\s+/, '').trim(), body: [line] };
      } else {
        current.body.push(line);
      }
    }
    flush();

    // 若无标题，视为单段
    if (sections.length === 0) {
      sections.push({ title: '', body: content });
    }

    const chunkSize = this.config.chunkSize;
    const overlap = this.config.chunkOverlap;
    const chunks = [];
    let globalChunkIndex = 0;

    sections.forEach((sec, sectionIndex) => {
      const text = typeof sec.body === 'string' ? sec.body : sec.body;
      if (text.length <= chunkSize) {
        chunks.push({
          content: text,
          metadata: {
            ...baseMetadata,
            is_md_section: true,
            section_title: sec.title || null,
            section_index: sectionIndex,
            chunk_index: globalChunkIndex++
          }
        });
      } else {
        let start = 0;
        while (start < text.length) {
          const end = Math.min(start + chunkSize, text.length);
          const part = text.slice(start, end);
          chunks.push({
            content: part,
            metadata: {
              ...baseMetadata,
              is_md_section: true,
              section_title: sec.title || null,
              section_index: sectionIndex,
              chunk_index: globalChunkIndex++
            }
          });
          start = end - overlap;
        }
      }
    });

    return chunks;
  }

  /**
   * 存储文档块
   */
  async storeChunks(chunks) {
    console.log('🌐 使用在线嵌入向量构建知识库');
    
    const documents = chunks.map(chunk => chunk.content);
    const metadatas = chunks.map(chunk => chunk.metadata);
    const ids = chunks.map((_, index) => `chunk_${index}`);
    
    // 生成简单的文本向量（用于演示）
    const embeddings = documents.map(doc => this.generateSimpleEmbedding(doc));
    
    await this.vectorStore.add(documents, embeddings, metadatas, ids);
    
    console.log(`✅ 知识库构建完成，共处理 ${chunks.length} 个文档块`);
  }

  /**
   * 生成增强的文本嵌入向量
   */
  generateSimpleEmbedding(text) {
    // 预处理文本
    const processedText = this.preprocessTextForEmbedding(text);

    // 使用改进的向量化方法
    const vector = new Array(512).fill(0); // 增加到512维

    // 1. 词频特征
    const wordFreq = this.calculateWordFrequency(processedText);
    Object.entries(wordFreq).forEach(([word, freq]) => {
      const hash = this.improvedHash(word);
      vector[hash % 256] += freq; // 前256维用于词频
    });

    // 2. N-gram特征
    const bigrams = this.extractNGrams(processedText, 2);
    bigrams.forEach(gram => {
      const hash = this.improvedHash(gram);
      vector[256 + (hash % 128)] += 1; // 中间128维用于bigram
    });

    // 3. 语义特征
    const semanticFeatures = this.extractSemanticFeatures(processedText);
    semanticFeatures.forEach((feature, index) => {
      if (index < 128) {
        vector[384 + index] = feature; // 后128维用于语义特征
      }
    });

    // 归一化
    const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    return magnitude > 0 ? vector.map(val => val / magnitude) : vector;
  }

  /**
   * 文本预处理
   */
  preprocessTextForEmbedding(text) {
    return text
      .toLowerCase()
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, ' ') // 保留中文、英文、数字
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * 计算词频
   */
  calculateWordFrequency(text) {
    const words = text.split(/\s+/).filter(word => word.length > 1);
    const freq = {};

    words.forEach(word => {
      freq[word] = (freq[word] || 0) + 1;
    });

    // 归一化词频
    const maxFreq = Math.max(...Object.values(freq));
    Object.keys(freq).forEach(word => {
      freq[word] = freq[word] / maxFreq;
    });

    return freq;
  }

  /**
   * 提取N-gram特征
   */
  extractNGrams(text, n) {
    const words = text.split(/\s+/).filter(word => word.length > 1);
    const ngrams = [];

    for (let i = 0; i <= words.length - n; i++) {
      ngrams.push(words.slice(i, i + n).join(' '));
    }

    return ngrams;
  }

  /**
   * 提取语义特征
   */
  extractSemanticFeatures(text) {
    const features = new Array(128).fill(0);

    // 关键词权重映射
    const keywordWeights = {
      '工业园区': 0.9, '304': 0.8, '联系方式': 0.9, '电话': 0.7,
      '平台': 0.6, '功能': 0.6, '数据分析': 0.8, 'qcew': 0.8,
      'cpi': 0.8, '地图': 0.6, '选址': 0.7, '投资': 0.7
    };

    // 计算关键词特征
    let featureIndex = 0;
    Object.entries(keywordWeights).forEach(([keyword, weight]) => {
      if (featureIndex < 64) {
        features[featureIndex] = text.includes(keyword) ? weight : 0;
        featureIndex++;
      }
    });

    // 文本长度特征
    features[64] = Math.min(text.length / 1000, 1); // 归一化文本长度

    // 数字密度特征
    const numbers = text.match(/\d+/g) || [];
    features[65] = Math.min(numbers.length / 10, 1);

    // 中文字符比例
    const chineseChars = text.match(/[\u4e00-\u9fa5]/g) || [];
    features[66] = chineseChars.length / text.length;

    // 英文字符比例
    const englishChars = text.match(/[a-zA-Z]/g) || [];
    features[67] = englishChars.length / text.length;

    return features;
  }

  /**
   * 改进的哈希函数
   */
  improvedHash(str) {
    let hash = 5381;
    for (let i = 0; i < str.length; i++) {
      hash = ((hash << 5) + hash) + str.charCodeAt(i);
    }
    return Math.abs(hash);
  }

  /**
   * 简单哈希函数（保留兼容性）
   */
  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  /**
   * 智能问答 - 基于网络搜索和项目文档
   */
  async askQuestion(question, userId = null, sessionId = null, options = {}) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      console.log(`🤔 用户提问: ${question}`);

      // 从选项中获取网络搜索开关
      const { enableWebSearch = false } = options;

      // 检查是否为项目相关问题
      const isProjectRelated = this.isProjectRelatedQuery(question);
      let projectContext = null;

      if (isProjectRelated) {
        console.log('📁 检测到项目相关问题，获取项目文档信息...');
        projectContext = await this.getProjectDocumentation(question);
      }

      // 判断是否需要网络搜索（考虑用户开关）
      const needsWebSearch = enableWebSearch && this.shouldPerformWebSearch(question, projectContext);
      let searchResults = null;

      if (needsWebSearch) {
        console.log('🔍 执行智能网络搜索获取实时信息...');
        try {
          // 使用搜索优化器优化查询
          const originalQuery = this.extractSearchQuery(question);
          const optimizedQuery = this.searchOptimizer.optimizeQuery(originalQuery, {
            userProfile: null, // 可以传入用户画像
            questionType: this.analyzeQuestionType(question)
          });

          console.log(`🎯 原始查询: "${originalQuery}"`);
          console.log(`✨ 优化查询: "${optimizedQuery}"`);

          searchResults = await this.executeTool('web_search', {
            query: optimizedQuery,
            num_results: 8 // 增加搜索结果数量以便后续筛选
          });

          // 对搜索结果进行相关性评分和排序
          if (searchResults && searchResults.raw_data) {
            const scoredResults = this.searchOptimizer.scoreSearchResults(
              searchResults.raw_data,
              question,
              { questionType: this.analyzeQuestionType(question) }
            );

            searchResults.raw_data = scoredResults.slice(0, 5); // 取前5个最相关的结果
            console.log(`📊 搜索结果已按相关性重新排序，平均得分: ${
              scoredResults.slice(0, 5).reduce((sum, r) => sum + r.relevanceScore, 0) / 5
            }`);
          }
        } catch (error) {
          console.warn('⚠️ 智能网络搜索失败:', error.message);
        }
      }

      // 构建基于搜索和项目文档的提示词
      const prompt = this.buildSearchBasedPrompt(question, projectContext, searchResults);

      // 调用AI生成回答
      const answer = await this.generateAnswer(prompt);

      console.log(`✅ AI回答生成完成`);

      const result = {
        question,
        answer,
        sources: projectContext ? [{ type: 'project_docs', files: projectContext.sources }] : [],
        searchUsed: !!searchResults,
        searchResults: searchResults?.raw_data,
        mode: 'search_based',
        sessionId: sessionId,
        timestamp: new Date().toISOString()
      };

      console.log('📤 返回问答结果...');
      return result;

      // 原始的Context Engine流程（暂时注释掉）
      /*
      // 1. Context Engine 分析
      console.log('🧠 分析上下文和用户意图...');
      const contextAnalysis = await this.contextEngine.analyzeContext(question, userId, sessionId);
      
      console.log(`📊 意图分析: ${contextAnalysis.intent.category} (置信度: ${contextAnalysis.intent.confidence.toFixed(2)})`);

      // 2. 基于意图优化知识检索
      const relevantChunks = await this.retrieveRelevantChunksWithContext(question, contextAnalysis);

      // 3. 智能判断是否需要网络搜索
      let searchResults = null;
      const needsWebSearch = this.intelligentSearchDecisionWithContext(question, relevantChunks, contextAnalysis);
      
      if (needsWebSearch) {
        console.log('🔍 检测到需要网络搜索，正在获取实时信息...');
        try {
          searchResults = await this.executeTool('web_search', {
            query: this.extractSearchQueryWithContext(question, contextAnalysis),
            num_results: 5
          });
        } catch (error) {
          console.warn('⚠️ 网络搜索失败，继续使用本地知识:', error.message);
        }
      }

      // 4. 构建上下文增强的提示词
      const prompt = this.buildContextEnhancedPrompt(question, relevantChunks, searchResults, contextAnalysis);

      // 5. 调用AI生成回答
      const answer = await this.generateAnswer(prompt);

      // 6. 记录问答历史
      await this.logQAWithContext(question, answer, userId, contextAnalysis);

      return {
        question,
        answer,
        sources: relevantChunks.map(chunk => ({
          file_path: chunk.metadata.file_path,
          file_type: chunk.metadata.file_type
        })),
        searchUsed: !!searchResults,
        searchResults: searchResults?.raw_data,
        contextAnalysis: {
          intent: contextAnalysis.intent,
          userProfile: contextAnalysis.userProfile,
          conversationContext: contextAnalysis.conversationContext
        },
        sessionId: contextAnalysis.sessionId,
        timestamp: new Date().toISOString()
      };
      */

    } catch (error) {
      console.error('❌ 智能问答失败:', error);
      
      return {
        question,
        answer: '抱歉，我暂时无法回答您的问题。可能的原因：\n1. AI服务暂时不可用\n2. 知识库正在更新\n3. 网络连接问题\n\n请稍后重试，或尝试重新表述您的问题。',
        sources: [],
        error: true,
        timestamp: new Date().toISOString()
      };
    }
  }

  // 检索相关方法已移除，现在使用网络搜索和项目文档

  /**
   * 构建增强查询
   */
  buildEnhancedQuery(question, contextAnalysis) {
    const { intent, entities, relatedConcepts } = contextAnalysis;

    let enhancedQuery = question;

    if (relatedConcepts.length > 0) {
      enhancedQuery += ' ' + relatedConcepts.slice(0, 3).join(' ');
    }

    const intentKeywords = this.getIntentKeywords(intent.category);
    if (intentKeywords.length > 0) {
      enhancedQuery += ' ' + intentKeywords.join(' ');
    }

    return enhancedQuery;
  }

  /**
   * 获取意图相关关键词
   */
  getIntentKeywords(intentCategory) {
    const keywordMap = {
      'platform_usage': ['使用', '操作', '功能', '步骤'],
      'map_related': ['地图', '可视化', '导航', '图层'],
      'data_analysis': ['数据', '分析', '统计', '趋势'],
      'contact_info': ['联系', '电话', '邮箱', '地址'],
      'labor_analysis': ['劳动力', '就业', '工资', 'QCEW'],
      'site_selection': ['选址', '园区', '评估', '分析']
    };

    return keywordMap[intentCategory] || [];
  }

  /**
   * 去重知识片段
   */
  deduplicateChunks(chunks) {
    const seen = new Set();
    return chunks.filter(chunk => {
      const key = chunk.content.substring(0, 100);
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * 基于上下文重新排序
   */
  rerankWithContext(chunks, contextAnalysis) {
    const { intent, userProfile } = contextAnalysis;

    return chunks.map(chunk => {
      let score = 1 - (chunk.distance || 0);

      if (this.chunkMatchesIntent(chunk, intent)) {
        score += 0.2;
      }

      if (userProfile && this.chunkMatchesUserExpertise(chunk, userProfile)) {
        score += 0.1;
      }

      return { ...chunk, contextScore: score };
    }).sort((a, b) => b.contextScore - a.contextScore);
  }

  /**
   * 检查知识片段是否匹配意图
   */
  chunkMatchesIntent(chunk, intent) {
    const intentKeywords = this.getIntentKeywords(intent.category);
    const content = chunk.content.toLowerCase();

    return intentKeywords.some(keyword => content.includes(keyword.toLowerCase()));
  }

  /**
   * 检查知识片段是否匹配用户专业领域
   */
  chunkMatchesUserExpertise(chunk, userProfile) {
    if (!userProfile.expertise || userProfile.expertise.length === 0) {
      return false;
    }

    const content = chunk.content.toLowerCase();
    const topExpertise = userProfile.expertise.slice(0, 3).map(([category]) => category);

    return topExpertise.some(expertise => content.includes(expertise.toLowerCase()));
  }

  // 旧的搜索决策方法已移除，现在使用简化的 shouldPerformWebSearch 方法

  /**
   * 基于上下文提取搜索查询
   */
  extractSearchQueryWithContext(question, contextAnalysis) {
    const { intent, entities } = contextAnalysis;

    let searchQuery = this.extractSearchQuery(question);

    if (entities.length > 0) {
      const locationEntities = entities.filter(e => e.type === 'location');
      if (locationEntities.length > 0) {
        searchQuery += ' ' + locationEntities[0].value;
      }
    }

    if (intent.category === 'contact_info') {
      searchQuery += ' 联系方式 联系电话';
    }

    return searchQuery;
  }

  /**
   * 智能提取搜索查询
   */
  extractSearchQuery(question) {
    // 扩展的停用词列表
    const stopWords = [
      '的', '是', '在', '有', '和', '与', '或', '但', '如何', '什么', '哪里', '为什么',
      '这个', '那个', '这些', '那些', '可以', '能够', '需要', '想要', '希望', '请问',
      '一下', '一些', '关于', '对于', '由于', '因为', '所以', '然后', '还有', '以及'
    ];

    // 关键词权重映射
    const keywordWeights = {
      '304工业园区': 3, '工业园区': 2, '园区': 1.5,
      '联系方式': 3, '联系': 2, '电话': 2, '邮箱': 2, '地址': 2,
      '平台': 1.5, '功能': 1.5, '使用': 1.5, '操作': 1.5,
      'QCEW': 2, 'CPI': 2, '劳动力': 2, '选址': 2,
      '数据分析': 2, '地图': 1.5, '分析': 1.5
    };

    // 1. 识别关键实体和概念
    const entities = this.extractEntitiesFromQuery(question);

    // 2. 分词并计算权重
    const words = question.split(/\s+|[，。！？；：、]/).filter(word =>
      word.length > 1 && !stopWords.includes(word)
    );

    // 3. 构建加权查询
    const weightedTerms = [];

    // 添加高权重的关键词
    for (const [keyword, weight] of Object.entries(keywordWeights)) {
      if (question.includes(keyword)) {
        weightedTerms.push({ term: keyword, weight });
      }
    }

    // 添加实体
    entities.forEach(entity => {
      weightedTerms.push({ term: entity.value, weight: 2.5 });
    });

    // 添加其他重要词汇
    words.forEach(word => {
      if (!weightedTerms.some(t => t.term.includes(word))) {
        weightedTerms.push({ term: word, weight: 1 });
      }
    });

    // 4. 按权重排序并构建查询
    const sortedTerms = weightedTerms
      .sort((a, b) => b.weight - a.weight)
      .slice(0, 8) // 取前8个最重要的词
      .map(t => t.term);

    // 5. 添加上下文关键词
    const contextKeywords = this.getContextualKeywords(question);
    sortedTerms.push(...contextKeywords);

    return sortedTerms.join(' ');
  }

  /**
   * 从查询中提取实体
   */
  extractEntitiesFromQuery(question) {
    const entities = [];

    // 地理位置实体
    const locationPatterns = [
      /(\d+工业园区?)/g,
      /(泰国|中国|美国|日本|韩国|新加坡)/g,
      /([省市区县][^，。！？；：\s]*)/g
    ];

    locationPatterns.forEach(pattern => {
      const matches = question.match(pattern);
      if (matches) {
        matches.forEach(match => {
          entities.push({ type: 'location', value: match });
        });
      }
    });

    // 组织机构实体
    const orgPatterns = [
      /(工业园区?|开发区|经济区)/g,
      /(公司|企业|集团|有限公司)/g
    ];

    orgPatterns.forEach(pattern => {
      const matches = question.match(pattern);
      if (matches) {
        matches.forEach(match => {
          entities.push({ type: 'organization', value: match });
        });
      }
    });

    return entities;
  }

  /**
   * 获取上下文关键词
   */
  getContextualKeywords(question) {
    const contextMap = {
      '联系': ['联系方式', '电话', '邮箱', '地址', '官网'],
      '功能': ['使用方法', '操作指南', '教程'],
      '数据': ['分析', '统计', '报告', '图表'],
      '地图': ['导航', '位置', '坐标', '标记'],
      '选址': ['评估', '分析', '建议', '因素']
    };

    const keywords = [];
    for (const [key, values] of Object.entries(contextMap)) {
      if (question.includes(key)) {
        keywords.push(...values.slice(0, 2)); // 每个类别最多添加2个关键词
      }
    }

    return keywords.slice(0, 3); // 最多添加3个上下文关键词
  }

  /**
   * 判断是否为项目相关查询
   */
  isProjectRelatedQuery(question) {
    const projectKeywords = [
      'Industrial Geo Dev', '平台', '地图', '劳动力', '选址', 'QCEW', 'CPI',
      '功能', '使用', '操作', '如何', '怎么', '接口', 'API', '数据分析'
    ];
    return projectKeywords.some(keyword => question.includes(keyword));
  }

  /**
   * 判断是否需要执行网络搜索
   */
  shouldPerformWebSearch(question, projectContext) {
    // 如果是项目相关问题且有足够的项目文档信息，可能不需要搜索
    if (projectContext && projectContext.hasRelevantInfo) {
      // 但如果问题涉及实时信息，仍然需要搜索
      const realTimeKeywords = [
        '最新', '现在', '目前', '当前', '实时', '今年', '2024', '2025',
        '价格', '成本', '费用', '市场', '趋势', '新闻', '动态', '联系方式'
      ];
      return realTimeKeywords.some(keyword => question.includes(keyword));
    }

    // 对于非项目相关问题，默认使用网络搜索
    return true;
  }

  /**
   * 智能获取项目文档信息
   */
  async getProjectDocumentation(question) {
    try {
      const fs = require('fs').promises;
      const path = require('path');

      const projectRoot = path.join(__dirname, '..');
      const sources = [];
      let content = '';

      // 根据问题类型确定需要读取的文档
      const docPriority = this.determineDocumentPriority(question);

      // 1. 读取README文件
      if (docPriority.includes('readme')) {
        try {
          const readmePath = path.join(projectRoot, '..', 'README.md');
          const readmeContent = await fs.readFile(readmePath, 'utf8');
          content += `# 项目概述 (README.md)\n${this.extractRelevantSections(readmeContent, question)}\n\n`;
          sources.push('README.md');
        } catch (error) {
          console.log('📄 README.md 未找到');
        }
      }

      // 2. 读取项目配置信息
      if (docPriority.includes('config')) {
        try {
          const packagePath = path.join(projectRoot, 'package.json');
          const packageContent = await fs.readFile(packagePath, 'utf8');
          const packageData = JSON.parse(packageContent);
          content += `# 项目配置信息\n`;
          content += `项目名称: ${packageData.name}\n`;
          content += `描述: ${packageData.description}\n`;
          content += `版本: ${packageData.version}\n`;
          if (packageData.scripts) {
            content += `可用脚本: ${Object.keys(packageData.scripts).join(', ')}\n`;
          }
          content += `\n`;
          sources.push('package.json');
        } catch (error) {
          console.log('📄 package.json 读取失败');
        }
      }

      // 3. 读取API文档
      if (docPriority.includes('api')) {
        const apiDocPaths = [
          path.join(projectRoot, '..', 'API_DOCUMENTATION.md'),
          path.join(projectRoot, '..', 'docs', 'api.md'),
          path.join(projectRoot, 'routes', 'README.md')
        ];

        for (const docPath of apiDocPaths) {
          try {
            const docContent = await fs.readFile(docPath, 'utf8');
            content += `# API文档\n${this.extractRelevantSections(docContent, question)}\n\n`;
            sources.push(path.basename(docPath));
            break;
          } catch (error) {
            // 继续尝试下一个路径
          }
        }
      }

      // 4. 读取功能说明文档
      if (docPriority.includes('features')) {
        const featureDocPaths = [
          path.join(projectRoot, '..', 'FEATURES.md'),
          path.join(projectRoot, '..', 'USER_GUIDE.md'),
          path.join(projectRoot, '..', 'docs', 'features.md')
        ];

        for (const docPath of featureDocPaths) {
          try {
            const docContent = await fs.readFile(docPath, 'utf8');
            content += `# 功能说明\n${this.extractRelevantSections(docContent, question)}\n\n`;
            sources.push(path.basename(docPath));
            break;
          } catch (error) {
            // 继续尝试下一个路径
          }
        }
      }

      // 5. 读取设置和配置说明
      if (docPriority.includes('setup')) {
        const setupDocPaths = [
          path.join(projectRoot, '..', 'SETUP.md'),
          path.join(projectRoot, '..', 'INSTALLATION.md'),
          path.join(projectRoot, '..', 'docs', 'setup.md')
        ];

        for (const docPath of setupDocPaths) {
          try {
            const docContent = await fs.readFile(docPath, 'utf8');
            content += `# 安装配置\n${this.extractRelevantSections(docContent, question)}\n\n`;
            sources.push(path.basename(docPath));
            break;
          } catch (error) {
            // 继续尝试下一个路径
          }
        }
      }

      // 6. 添加内置知识
      content += this.getBuiltInKnowledge(question);

      // 7. 读取算法类文档（cluster 边界算法说明）
      if (docPriority.includes('algorithms')) {
        try {
          const algoDocPath = path.join(projectRoot, '..', 'docs', 'cluster-boundary-algorithm.md');
          const algoContent = await fs.readFile(algoDocPath, 'utf8');
          const relevantAlgo = this.extractRelevantSections(algoContent, question);
          if (relevantAlgo && relevantAlgo.trim()) {
            content += `\n\n# Cluster Boundary Algorithm\n${relevantAlgo}\n`;
            sources.push('docs/cluster-boundary-algorithm.md');
          }
        } catch (error) {
          console.log('📄 cluster-boundary-algorithm.md 未找到或读取失败');
        }
      }

      // 检查是否有相关信息
      const hasRelevantInfo = content.length > 200;

      return {
        content: content.trim(),
        sources,
        hasRelevantInfo
      };
    } catch (error) {
      console.error('❌ 获取项目文档失败:', error);
      return { content: '', sources: [], hasRelevantInfo: false };
    }
  }

  /**
   * 确定文档优先级
   */
  determineDocumentPriority(question) {
    const priorities = [];
    const questionLower = question.toLowerCase();

    // 根据问题内容确定需要的文档类型
    if (questionLower.includes('功能') || questionLower.includes('使用') || questionLower.includes('操作')) {
      priorities.push('features', 'readme');
    }

    if (questionLower.includes('api') || questionLower.includes('接口') || questionLower.includes('数据')) {
      priorities.push('api');
    }

    if (questionLower.includes('安装') || questionLower.includes('配置') || questionLower.includes('设置')) {
      priorities.push('setup', 'config');
    }

    if (questionLower.includes('平台') || questionLower.includes('项目') || questionLower.includes('介绍')) {
      priorities.push('readme', 'features');
    }

    // 算法/边界检测相关（中英文关键词）
    if (
      questionLower.includes('cluster') ||
      questionLower.includes('boundary') ||
      questionLower.includes('algorithm') ||
      questionLower.includes('聚类') ||
      questionLower.includes('边界') ||
      questionLower.includes('轮廓') ||
      questionLower.includes('凸包')
    ) {
      priorities.push('algorithms');
    }

    // 默认优先级
    if (priorities.length === 0) {
      priorities.push('readme', 'features', 'config', 'algorithms');
    }

    return priorities;
  }

  /**
   * 提取文档中的相关章节
   */
  extractRelevantSections(content, question) {
    const lines = content.split('\n');
    const relevantSections = [];
    let currentSection = '';
    let isRelevant = false;

    const questionKeywords = question.toLowerCase().split(/\s+/);

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // 检查是否是标题行
      if (line.startsWith('#')) {
        // 保存之前的章节
        if (isRelevant && currentSection.trim()) {
          relevantSections.push(currentSection.trim());
        }

        // 开始新章节
        currentSection = line + '\n';
        isRelevant = questionKeywords.some(keyword =>
          line.toLowerCase().includes(keyword)
        );
      } else {
        currentSection += line + '\n';

        // 检查内容是否相关
        if (!isRelevant) {
          isRelevant = questionKeywords.some(keyword =>
            line.toLowerCase().includes(keyword)
          );
        }
      }
    }

    // 添加最后一个章节
    if (isRelevant && currentSection.trim()) {
      relevantSections.push(currentSection.trim());
    }

    // 如果没有找到相关章节，返回前500字符
    if (relevantSections.length === 0) {
      return content.substring(0, 500) + (content.length > 500 ? '...' : '');
    }

    return relevantSections.join('\n\n');
  }

  /**
   * 获取内置知识库
   */
  getBuiltInKnowledge(question) {
    const questionLower = question.toLowerCase();
    let knowledge = '';

    // Industrial Geo Dev 平台核心信息
    if (questionLower.includes('平台') || questionLower.includes('功能') || questionLower.includes('介绍')) {
      knowledge += `
Industrial Geo Dev 平台核心功能和界面导航

主要功能模块：
1. 主仪表板（Dashboard）- 在顶部导航栏"服务"菜单中选择"仪表板"进入
   - 交互式地图分析和可视化
   - 多图层数据展示（美国州、泰国省份、经济热点等）
   - 左侧图层控制面板可以切换不同数据图层
   - 右上角地图样式切换按钮

2. AI选址分析器（Site Analyzer）- 在顶部导航栏"服务"菜单中选择"选址分析器"
   - 智能工业选址评估
   - 多因素分析算法
   - 成本效益评估和财务建模
   - 交通便利性分析

3. AI智能助手（AI Assistant）- 在顶部导航栏"服务"菜单中选择"AI智能助手"
   - 专业工业地理开发咨询
   - 节点式可视化对话界面
   - 支持工业选址、劳动力分析、经济热点映射、投资成本建模

4. 劳动力数据分析 - 在主仪表板中访问
   - 基于QCEW季度就业和工资数据
   - 行业就业趋势分析
   - 地区专业化分析（Location Quotient）

5. 经济热点分析 - 在主仪表板的左侧控制面板中开启
   - 点击"经济热点"图层
   - 可选择位置商（LQ）模型或重力模型
   - 支持制造业、政府、私营企业、个体经营四种行业

界面操作指南：
- 顶部导航栏：包含首页、关于、服务、联系我们等主要功能入口
- 主地图区域：支持拖拽、缩放、点击查看详情
- 左侧控制面板：图层开关、经济热点设置、图例显示
- 右上角工具栏：地图样式切换、测量工具、天气信息
- 底部状态栏：显示当前坐标、缩放级别等信息
`;
    }

    // 304工业园区信息
    if (questionLower.includes('304') && questionLower.includes('工业园')) {
      knowledge += `
# 304工业园区信息

## 基本信息
- **位置**: 泰国春武里府
- **类型**: 综合性工业开发区
- **面积**: 约3,000公顷
- **成立时间**: 1988年

## 主要产业
- 汽车及零部件制造
- 电子电器产业
- 石化工业
- 食品加工业

## 投资优势
- 完善的基础设施
- 优惠的投资政策
- 便利的交通网络
- 专业的服务团队

## 联系信息获取建议
建议通过以下官方渠道获取最新联系信息：
1. 泰国投资促进委员会(BOI)官网
2. 304工业园区管理委员会官方网站
3. 中国驻泰国商务参赞处
`;
    }

    // 数据分析相关
    if (questionLower.includes('数据') || questionLower.includes('分析') || questionLower.includes('qcew') || questionLower.includes('cpi')) {
      knowledge += `
# 数据分析功能说明

## QCEW数据分析
- **数据来源**: 美国劳工统计局
- **更新频率**: 季度更新
- **覆盖范围**: 全美各州、县级数据
- **分析维度**: 行业、就业人数、平均工资

## CPI成本分析
- **功能**: 成本上涨预测和分析
- **应用场景**: 投资决策、成本控制
- **计算模型**: 基于历史数据的趋势分析

## 地图数据可视化
- **技术**: Three.js + WebGL
- **数据格式**: GeoJSON、TopoJSON
- **交互功能**: 缩放、平移、图层切换
`;
    }

    // 系统界面导航信息
    if (questionLower.includes('界面') || questionLower.includes('菜单') || questionLower.includes('导航') || 
        questionLower.includes('在哪') || questionLower.includes('在哪里') || questionLower.includes('找到') ||
        questionLower.includes('按钮') || questionLower.includes('功能在')) {
      knowledge += `
Industrial Geo Dev 平台界面导航指南

顶部导航栏结构：
1. "首页" - 返回欢迎页面
2. "关于" - 查看平台介绍
3. "服务" - 主要功能入口，包含：
   - 仪表板：进入主地图分析界面
   - 选址分析器：AI驱动的选址分析工具
   - AI智能助手：专业咨询服务
   - 咨询服务：专业咨询联系方式
4. "联系我们" - 获取联系信息

主要界面布局：

仪表板界面（主地图）：
- 左侧：图层控制面板，包含美国州、泰国省份、经济热点等图层开关
- 中央：交互式地图区域，支持拖拽、缩放、点击查询
- 右上角：地图样式切换（卫星、街道、地形）、测量工具、天气信息
- 右下角：缩放控件
- 底部：坐标显示和状态信息

选址分析器界面：
- 顶部：项目列表和创建新项目按钮
- 左侧：分析参数设置面板
- 中央：选址结果地图展示
- 右侧：财务分析和成本计算

AI智能助手界面：
- 中央：节点式可视化对话区域
- 顶部：功能控制按钮（创建节点、连接管理、自动排列等）
- 底部：输入框和快捷提示词（工业选址、劳动力分析、经济热点映射、投资成本建模）

常见操作位置：
- 经济热点分析：主仪表板 → 左侧面板 → "经济热点"图层
- 劳动力数据：主仪表板 → 点击美国地区 → 查看详情
- 选址分析：顶部导航 → 服务 → 选址分析器
- AI咨询：顶部导航 → 服务 → AI智能助手
`;
    }

    // 使用指南
    if (questionLower.includes('如何') || questionLower.includes('怎么') || questionLower.includes('使用') || questionLower.includes('操作')) {
      knowledge += `
平台详细使用指南

访问主要功能的具体步骤：

1. 工业选址分析：
   - 点击顶部导航栏的"服务"按钮
   - 选择"选址分析器"进入AI分析页面
   - 创建新项目或选择现有项目
   - 填写选址需求（地点、预算、行业等）
   - 点击"开始分析"获得AI选址建议

2. 地图数据分析：
   - 点击顶部导航栏的"服务"按钮
   - 选择"仪表板"进入主地图界面
   - 使用左侧控制面板开启不同图层：
     * 美国州界图层 - 查看美国各州数据
     * 泰国省份图层 - 查看泰国各省数据
     * 经济热点图层 - 分析区域经济活跃度
   - 右上角可切换地图样式（卫星图、街道图、地形图）

3. 经济热点分析具体操作：
   - 在主仪表板左侧面板中找到"经济热点"选项
   - 开启经济热点图层开关
   - 选择分析模型：位置商（LQ）或重力模型
   - 选择行业类型：制造业、政府、私营企业、个体经营
   - 调整强度和服务半径参数
   - 查看热力图可视化结果

4. 劳动力数据查询：
   - 在主地图中点击任意美国地区
   - 查看弹出的详情面板
   - 点击"查看详细数据"按钮
   - 浏览QCEW就业和工资统计数据

5. AI智能助手使用：
   - 点击顶部导航栏"服务"→"AI智能助手"
   - 或点击页面右下角的AI助手浮动按钮
   - 在输入框中描述您的问题
   - 可选择底部快捷提示：工业选址、劳动力分析、经济热点映射、投资成本建模
   - AI会生成节点式可视化回答
   - 点击节点可展开查看完整内容

地图操作技巧：
- 鼠标拖拽：移动地图视野
- 鼠标滚轮：缩放地图
- 双击：快速放大到该区域
- 右上角缩放控件：精确控制缩放级别
- 左侧图层面板：控制数据图层显示/隐藏
- 点击地图上的标记或区域：查看详细信息
`;
    }

    return knowledge;
  }

  /**
   * 判断是否为联系信息查询
   */
  isContactInfoQuery(question) {
    const contactKeywords = ['联系', '电话', '手机', '邮箱', '地址', '联系方式', '联系人', '工业园区', '园区'];
    return contactKeywords.some(keyword => question.includes(keyword));
  }

  /**
   * 智能构建基于搜索和项目文档的提示词
   */
  buildSearchBasedPrompt(question, projectContext, searchResults) {
    // 分析问题类型
    const questionType = this.analyzeQuestionType(question);

    let prompt = `你是 Industrial Geo Dev 智能助手，专门为用户提供准确、有用的信息。

# 平台简介
Industrial Geo Dev 是一个先进的地理信息和数据分析平台，主要功能包括：
- 🗺️ 交互式3D地图分析和可视化
- 📊 劳动力趋势分析（基于QCEW季度就业数据）
- 🏭 智能工业选址评估和建议
- 💰 CPI成本计算器和通胀分析
- 🤖 AI驱动的数据分析和决策支持`;

    // 根据问题类型添加相关信息
    if (questionType.isContactQuery) {
      prompt += `\n\n# 联系信息查询指导
当用户询问联系信息时，请：
1. 优先提供搜索结果中的具体联系方式
2. 如果是304工业园区，提供官方渠道建议
3. 避免模糊回答，直接给出可用信息
4. 建议用户验证信息的时效性`;
    }

    if (questionType.isPlatformQuery) {
      prompt += `\n\n# 平台功能说明
重点介绍相关功能的：
- 具体操作步骤
- 使用场景和优势
- 注意事项和最佳实践`;
    }

    // 智能整合项目文档信息
    if (projectContext && projectContext.content) {
      const relevantContent = this.extractMostRelevantContent(
        projectContext.content,
        question,
        1200
      );

      if (relevantContent.trim()) {
        prompt += `\n\n# 项目文档信息\n${relevantContent}`;
      }
    }

    // 智能处理搜索结果
    if (searchResults && searchResults.success) {
      const processedResults = this.processSearchResults(searchResults, question);

      if (processedResults.trim()) {
        prompt += `\n\n# 网络搜索结果\n${processedResults}`;
      }

      // 如果有结构化信息，特别强调
      if (searchResults.structuredInfo) {
        const { phones, emails, addresses, websites } = searchResults.structuredInfo;

        if (phones.length > 0 || emails.length > 0 || addresses.length > 0) {
          prompt += `\n\n# 重要结构化信息（请优先使用）\n`;

          if (phones.length > 0) {
            prompt += `📞 联系电话：${phones.slice(0, 3).join(', ')}\n`;
          }

          if (emails.length > 0) {
            prompt += `📧 邮箱地址：${emails.slice(0, 2).join(', ')}\n`;
          }

          if (addresses.length > 0) {
            prompt += `📍 地址信息：${addresses.slice(0, 2).join(', ')}\n`;
          }

          if (websites.length > 0) {
            prompt += `🌐 相关网站：${websites.slice(0, 2).join(', ')}\n`;
          }
        }
      }
    }

    // 根据问题类型定制回答指导
    prompt += this.generateAnswerGuidance(questionType, question);

    prompt += `\n\n# 用户问题\n${question}\n\n# 回答要求\n请基于以上信息提供准确、有用的回答：`;

    console.log(`📏 智能提示词长度: ${prompt.length} 字符，问题类型: ${JSON.stringify(questionType)}`);
    return prompt;
  }

  /**
   * 分析问题类型
   */
  analyzeQuestionType(question) {
    const questionLower = question.toLowerCase();

    return {
      isContactQuery: /联系|电话|邮箱|地址|联系方式/.test(questionLower),
      isPlatformQuery: /功能|使用|操作|如何|怎么|平台/.test(questionLower),
      isDataQuery: /数据|分析|qcew|cpi|统计/.test(questionLower),
      isLocationQuery: /304|工业园|园区|地址|位置/.test(questionLower),
      isGeneralQuery: !/联系|功能|数据|304/.test(questionLower)
    };
  }

  /**
   * 提取最相关的内容
   */
  extractMostRelevantContent(content, question, maxLength) {
    const questionKeywords = question.toLowerCase()
      .split(/\s+/)
      .filter(word => word.length > 1);

    const sections = content.split(/\n\s*\n/);
    const scoredSections = sections.map(section => {
      const sectionLower = section.toLowerCase();
      const score = questionKeywords.reduce((acc, keyword) => {
        const matches = (sectionLower.match(new RegExp(keyword, 'g')) || []).length;
        return acc + matches;
      }, 0);

      return { section, score };
    });

    // 按相关性排序并选择最相关的部分
    const relevantSections = scoredSections
      .filter(item => item.score > 0)
      .sort((a, b) => b.score - a.score)
      .map(item => item.section);

    let result = '';
    for (const section of relevantSections) {
      if (result.length + section.length <= maxLength) {
        result += section + '\n\n';
      } else {
        // 添加部分内容
        const remainingLength = maxLength - result.length - 3;
        if (remainingLength > 50) {
          result += section.substring(0, remainingLength) + '...';
        }
        break;
      }
    }

    return result.trim();
  }

  /**
   * 生成英文并行片段（启发式翻译+术语保留），用于提升跨语言检索
   * 不调用外部服务，保证本地可运行
   */
  generateEnglishParallel(chineseMarkdown) {
    if (!chineseMarkdown || typeof chineseMarkdown !== 'string') return '';

    // 轻量启发式“翻译”：仅替换常见标题和关键术语，其余原文保留，避免误译
    const glossary = [
      ['算法', 'Algorithm'],
      ['概述', 'Overview'],
      ['核心设计理念', 'Design Principles'],
      ['问题分析', 'Problem Analysis'],
      ['解决方案', 'Solution'],
      ['实现步骤', 'Implementation Steps'],
      ['预处理', 'Preprocessing'],
      ['极值点', 'Extreme Points'],
      ['几何中心', 'Centroid'],
      ['径向扫描', 'Radial Scanning'],
      ['密度过滤', 'Density Filtering'],
      ['边界平滑', 'Boundary Smoothing'],
      ['凸包优化', 'Convex Hull Optimization'],
      ['时间复杂度', 'Time Complexity'],
      ['空间复杂度', 'Space Complexity'],
      ['视觉效果对比', 'Visual Comparison'],
      ['参数调优', 'Parameter Tuning'],
      ['集成与使用', 'Integration & Usage'],
      ['实际效果', 'Practical Results'],
      ['目的', 'Purpose'],
      ['优势', 'Advantages'],
      ['保证', 'Guarantee'],
      ['效果', 'Effect'],
      ['目的', 'Objective'],
    ];

    let result = chineseMarkdown;

    // 替换标题行中的常见中文标题关键词
    result = result.split('\n').map(line => {
      if (/^#{1,6}\s+/.test(line)) {
        let newLine = line;
        glossary.forEach(([zh, en]) => {
          newLine = newLine.replace(new RegExp(zh, 'g'), en);
        });
        return newLine;
      }
      return line;
    }).join('\n');

    // 为首部添加英文摘要头注，标注为辅助并行片段
    const header = `<!-- English parallel excerpt (heuristic) for improved retrieval. Original: Chinese markdown. -->\n`;
    return header + result;
  }

  /**
   * 智能处理搜索结果
   */
  processSearchResults(searchResults, question) {
    if (!searchResults.raw_data || searchResults.raw_data.length === 0) {
      return searchResults.result || '';
    }

    const questionKeywords = question.toLowerCase().split(/\s+/);

    // 对搜索结果进行相关性评分
    const scoredResults = searchResults.raw_data.map(item => {
      const text = `${item.title} ${item.snippet}`.toLowerCase();
      const score = questionKeywords.reduce((acc, keyword) => {
        return acc + (text.includes(keyword) ? 1 : 0);
      }, 0);

      return { ...item, relevanceScore: score };
    });

    // 按相关性排序
    const sortedResults = scoredResults
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, 3); // 取前3个最相关的结果

    let processedContent = `基于网络搜索，找到以下相关信息：\n\n`;

    sortedResults.forEach((item, index) => {
      processedContent += `${index + 1}. **${item.title}**\n`;
      processedContent += `   ${item.snippet}\n`;
      if (item.displayLink) {
        processedContent += `   来源: ${item.displayLink}\n`;
      }
      processedContent += `\n`;
    });

    return processedContent;
  }

  /**
   * 生成回答指导
   */
  generateAnswerGuidance(questionType, question) {
    let guidance = `\n\n# 回答指导原则\n`;

    if (questionType.isContactQuery) {
      guidance += `
**联系信息查询**：
- 直接提供搜索结果中的具体联系方式（电话、邮箱、地址）
- 不要说"可以在某处找到"，而是直接给出信息
- 如果是304工业园区，提供官方渠道建议
- 提醒用户验证信息的时效性`;
    }

    if (questionType.isPlatformQuery) {
      guidance += `
**平台功能查询**：
- 结合项目文档提供具体操作步骤
- 说明功能的使用场景和优势
- 提供实用的操作建议
- 保持回答的实用性和可操作性`;
    }

    if (questionType.isDataQuery) {
      guidance += `
**数据分析查询**：
- 解释数据来源和更新频率
- 说明分析方法和应用场景
- 提供数据解读建议
- 强调数据的准确性和局限性`;
    }

    guidance += `
通用要求：
- 回答要准确、简洁、有用
- 优先使用最新、最可靠的信息
- 明确指出具体的界面位置和操作步骤
- 避免使用"可能有"、"应该"等模糊词汇
- 保持友好、专业的语调，语言自然流畅
- 严格避免使用格式化符号（如**、*、•等）`;

    return guidance;
  }

  /**
   * 构建上下文增强的提示词
   */
  buildContextEnhancedPrompt(question, relevantChunks, searchResults, contextAnalysis) {
    const { intent, userProfile, conversationContext, emotionAnalysis, responseStrategy } = contextAnalysis;

    const context = relevantChunks.map(chunk => {
      const fileInfo = chunk.metadata.file_path ? `[${chunk.metadata.file_path}]` : '[Unknown]';
      return `${fileInfo}\n${chunk.content}`;
    }).join('\n\n---\n\n');

    let prompt = `你是 Industrial Geo Dev 项目的智能助手，为用户提供专业、友好的帮助。

项目背景：
Industrial Geo Dev 是一个地理信息和数据分析平台，提供交互式地图分析、劳动力趋势分析、工业选址评估等功能。

用户上下文分析：
- 意图类别: ${intent.category} (置信度: ${intent.confidence.toFixed(2)})
- 问题复杂度: ${intent.complexity}
- 紧急程度: ${intent.urgency}`;

    // 添加情感分析信息
    if (emotionAnalysis) {
      const emotion = emotionAnalysis;
      prompt += `
- 情感状态: ${emotion.primary.emotion} (强度: ${emotion.intensity.toFixed(2)})
- 满意度: ${emotion.satisfaction.level}
- 挫折感: ${emotion.frustration.toFixed(2)}`;

      if (emotion.urgency > 0.5) {
        prompt += `
- 紧急感: 高 (${emotion.urgency.toFixed(2)})`;
      }
    }

    // 添加用户画像信息
    if (userProfile) {
      prompt += `
- 用户经验级别: ${userProfile.experienceLevel}
- 偏好风格: ${userProfile.preferredStyle}`;

      if (userProfile.expertise.length > 0) {
        prompt += `
- 专业领域: ${userProfile.expertise.slice(0, 3).map(([cat]) => cat).join(', ')}`;
      }
    }

    // 添加响应策略
    if (responseStrategy) {
      const strategy = responseStrategy;
      prompt += `

响应策略建议：
- 语调: ${strategy.tone}
- 详细程度: ${strategy.detailLevel}`;

      if (strategy.empathy) {
        prompt += `
- 需要共情理解`;
      }
      if (strategy.urgency) {
        prompt += `
- 需要快速响应`;
      }
      if (strategy.encouragement) {
        prompt += `
- 需要鼓励支持`;
      }
    }

    // 添加对话上下文
    if (conversationContext.conversationFlow !== 'initial') {
      prompt += `
- 对话流程: ${conversationContext.conversationFlow}`;

      if (conversationContext.recentTopics.length > 0) {
        prompt += `
- 最近话题: ${conversationContext.recentTopics.join(', ')}`;
      }
    }

    prompt += `

相关信息：
${context}`;

    // 如果有搜索结果，添加到提示词中
    if (searchResults && searchResults.success) {
      prompt += `

网络搜索结果：
${searchResults.result}`;
    }

    prompt += `

用户问题：${question}

回答指导：`;

    // 基于意图和用户画像定制回答指导
    if (intent.category === 'contact_info') {
      prompt += `
这是一个联系信息查询。请：
1. 首先检查平台数据库中是否有相关信息
2. 如有网络搜索结果，直接提供最准确的联系方式
3. 回答要简洁明了，避免过多解释
4. 如果信息来源于网络搜索，简单标注来源即可`;
    } else if (intent.category === 'platform_usage') {
      const detailLevel = userProfile?.experienceLevel === 'expert' ? '专业详细' : '简洁易懂';
      prompt += `
这是关于平台功能的询问。请：
1. 基于项目知识库提供准确的界面导航指导
2. 明确告诉用户具体的按钮位置和操作步骤
3. 使用"点击顶部导航栏的XXX"、"在左侧面板中找到XXX"等具体描述
4. 避免使用"可能"、"应该"等模糊词汇，给出确定的操作指引
5. 回答要${detailLevel}，避免过度格式化符号
6. 如果用户问某个功能在哪里，直接说明具体位置和访问路径`;
    } else {
      prompt += `
请根据用户问题提供准确的系统操作指导：
1. 明确指出具体的界面位置和操作步骤
2. 避免模糊表述，给出确定的导航路径
3. 语言简洁流畅，避免过多格式化符号
4. 基于平台的实际界面结构回答
- 考虑用户的经验级别和偏好风格
- 如果是对话延续，参考之前的话题
- 保持回答自然流畅，避免过度格式化
- 根据问题复杂度和紧急程度调整回答详细程度`;
    }

    return prompt;
  }

  /**
   * 生成AI回答
   */
  async generateAnswer(prompt) {
    if (!this.currentModel) {
      console.warn('⚠️ 没有可用的AI模型，使用降级响应');
      return this.generateFallbackAnswer(prompt);
    }

    try {
      if (this.currentModel.type === 'gemini') {
        return await this.generateGeminiAnswer(prompt);
      } else if (this.currentModel.type === 'openrouter') {
        return await this.generateOpenRouterAnswer(prompt);
      }

      return '抱歉，当前模型类型不支持。';

    } catch (error) {
      console.error(`❌ ${this.currentModel.name} 生成回答失败:`, error);

      // 尝试切换到备用模型
      const backupModel = await this.selectBestModel();
      if (backupModel && backupModel.name !== this.currentModel.name) {
        console.log(`🔄 切换到备用模型: ${backupModel.name}`);
        this.currentModel = backupModel;
        return await this.generateAnswer(prompt);
      }

      // 如果所有模型都失败，使用降级回答
      console.warn('⚠️ 所有AI模型都失败，使用降级响应');
      return this.generateFallbackAnswer(prompt);
    }
  }

  /**
   * 生成降级回答
   */
  generateFallbackAnswer(prompt) {
    // 从prompt中提取问题
    const questionMatch = prompt.match(/用户问题[：:]\s*(.+?)(?:\n|$)/);
    const question = questionMatch ? questionMatch[1].trim() : '';

    // 304工业园区专门的详细信息
    if (question.includes('304')) {
      return this.generate304ParkInfo(question);
    }

    // 基于关键词的详细回答
    const keywords = {
      '工业园': this.generateIndustrialParkInfo(),
      '选址': this.generateLocationAnalysisInfo(),
      '劳动力': this.generateLaborMarketInfo(),
      '成本': this.generateCostAnalysisInfo(),
      '投资': this.generateInvestmentInfo(),
      '交通': this.generateTransportationInfo(),
      '政策': this.generatePolicyInfo()
    };

    // 查找匹配的关键词
    for (const [keyword, answerFunc] of Object.entries(keywords)) {
      if (question.includes(keyword)) {
        return answerFunc;
      }
    }

    // 默认回答
    return `感谢您的咨询。我是Industrial Geo Dev平台的AI助手，很高兴为您服务。

我们的平台专注于工业地理开发分析，主要提供：
- 工业园区选址分析
- 投资环境评估
- 劳动力市场调研
- 成本效益分析
- 政策法规咨询

您可以通过以下方式使用我们的平台：
1. 在顶部导航栏点击"服务"，选择"仪表板"进行地图数据分析
2. 选择"选址分析器"进行AI驱动的选址分析
3. 使用"AI智能助手"（就是我）获取专业咨询

请告诉我您具体需要什么帮助，我将为您提供详细的操作指导。`;
  }

  /**
   * 生成304工业园区详细信息
   */
  generate304ParkInfo(question) {
    const baseInfo = `304工业园区（泰国春布里府）详细信息

基本概况：
- 位置：泰国春布里府（Chonburi）
- 面积：约12,000公顷，是泰国最大的工业园区之一
- 建立时间：1980年代开始开发
- 管理机构：泰国工业园区管理局（IEAT）

主要产业：
- 汽车及零部件制造（丰田、本田、日产等）
- 电子电器产业
- 石化工业
- 食品加工业
- 纺织服装业

投资优势：
- 靠近林查班港（Laem Chabang Port）- 泰国最大深水港
- 距离曼谷约120公里，交通便利
- 完善的基础设施配套
- 政府政策支持和税收优惠
- 丰富的技术工人资源`;

    // 根据具体问题提供更针对性的信息
    if (question.includes('联系') || question.includes('负责人') || question.includes('管理') || question.includes('咨询')) {
      return `${baseInfo}

**📞 联系方式与咨询渠道**

**泰国工业园区管理局(IEAT)**
• 官方网站：www.ieat.go.th
• 电话：+66 2 202 4999
• 邮箱：<EMAIL>

**304工业园区管理办公室**
• 地址：304 Industrial Park, Prachinburi Province, Thailand
• 电话：+66 37 219 222
• 传真：+66 37 219 333

**投资促进服务**
• 一站式服务中心：提供许可证申请、投资咨询
• 中文服务：有中文投资顾问
• 在线申请：支持在线提交投资申请

**🔥 热门咨询项目**
• 土地租赁价格：约500-800泰铢/平方米/年
• 厂房建设成本：约12,000-15,000泰铢/平方米
• 劳动力成本：技术工人约12,000-18,000泰铢/月
• 优惠政策：最高可享受8年企业所得税免征

**💡 投资建议**
基于304工业园区的特点，建议重点关注汽车零部件、电子制造等优势产业，充分利用其临港优势和政策支持。`;
    }

    return baseInfo + `

**💼 投资机会分析**
304工业园区作为泰国东部经济走廊（EEC）的重要组成部分，正在经历新一轮的发展机遇。特别是在：
• 新能源汽车产业链
• 智能制造和自动化
• 绿色环保技术
• 数字经济相关产业

如需了解具体的投资机会、政策详情或实地考察安排，建议联系相关投资促进机构获取最新信息。`;
  }

  /**
   * 生成工业园区信息
   */
  generateIndustrialParkInfo() {
    return `**工业园区选址分析指南**

**🎯 选址关键因素**
• **地理位置**：靠近主要市场、港口、机场
• **基础设施**：电力、水源、通讯、道路完善度
• **劳动力资源**：技能水平、数量、成本
• **政策环境**：税收优惠、监管便利性
• **产业集群**：上下游配套企业聚集度

**📊 评估维度**
1. **交通便利性**（权重25%）
2. **基础设施**（权重20%）
3. **人力资源**（权重20%）
4. **政策支持**（权重15%）
5. **成本效益**（权重10%）
6. **环境因素**（权重10%）

**🔍 推荐园区类型**
• 综合型工业园区：适合多元化投资
• 专业型园区：如汽车城、电子园等
• 保税园区：享受特殊贸易政策
• 绿色园区：注重环保和可持续发展`;
  }

  /**
   * 生成选址分析信息
   */
  generateLocationAnalysisInfo() {
    return `**专业选址分析方法论**

**🗺️ 地理信息分析**
• 使用GIS技术进行空间分析
• 考虑气候、地质、环境因素
• 评估自然灾害风险
• 分析区域发展潜力

**📈 市场接近性分析**
• 目标市场距离和可达性
• 供应链物流成本
• 客户分布密度
• 竞争对手布局

**💰 成本效益建模**
• 土地/厂房成本
• 劳动力成本
• 物流运输成本
• 运营维护成本
• 税收和优惠政策影响

**⚖️ 风险评估矩阵**
• 政治稳定性
• 法律法规变化风险
• 汇率风险
• 供应链中断风险
• 环境合规风险

**🎯 推荐决策模型**
使用AHP（层次分析法）权重评分系统，综合考虑上述因素，为每个候选地点计算综合得分。`;
  }

  /**
   * 生成劳动力市场信息
   */
  generateLaborMarketInfo() {
    return `**劳动力市场深度分析**

**👥 人力资源评估**
• 人口规模和年龄结构
• 教育水平分布
• 技能培训体系
• 语言能力（英语、当地语言）
• 工作稳定性和流动率

**💼 薪酬福利基准**
• 不同技能级别工资水平
• 社会保险和福利成本
• 加班费和节假日工资
• 年度调薪幅度趋势
• 绩效奖金体系

**📚 培训发展体系**
• 职业技术教育资源
• 在职培训项目
• 政府技能发展支持
• 企业内部培养机制
• 国际认证体系

**📊 劳动力市场指标**
• 失业率和就业增长率
• 劳动参与率
• 产业就业分布
• 工会组织化程度
• 劳资关系稳定性

**🔮 未来趋势预测**
• 人口红利变化
• 自动化对就业的影响
• 新兴技能需求
• 远程办公趋势
• 代际工作观念变化`;
  }

  /**
   * 生成成本分析信息
   */
  generateCostAnalysisInfo() {
    return `**全面成本分析框架**

**🏗️ 初始投资成本**
• 土地购买/租赁成本
• 厂房建设/装修成本
• 生产设备采购
• 基础设施配套
• 许可证和注册费用

**⚙️ 运营成本结构**
• 原材料采购成本
• 人力资源成本（工资、培训、福利）
• 能源消耗（电力、燃气、水）
• 物流运输费用
• 维护保养成本

**💰 财务成本**
• 银行贷款利率
• 汇率风险成本
• 保险费用
• 税收负担
• 资金机会成本

**📊 成本优化策略**
• 规模经济效应利用
• 供应链成本控制
• 能源效率提升
• 自动化降本增效
• 政府补贴申请

**⏰ 成本趋势预测**
• 通胀率影响
• 劳动力成本上涨
• 原材料价格波动
• 环保成本增加
• 技术升级投入

**🎯 成本控制KPI**
• 单位产品成本
• 成本结构占比
• 成本削减率
• 投资回报周期
• 盈亏平衡点`;
  }

  /**
   * 生成投资信息
   */
  generateInvestmentInfo() {
    return `**投资决策分析体系**

**📈 投资评估方法**
• NPV（净现值）分析
• IRR（内部收益率）计算
• 投资回收期测算
• 敏感性分析
• 情景分析（乐观/悲观/基准）

**🎯 投资机会识别**
• 行业增长潜力分析
• 市场空白点发现
• 技术创新机会
• 政策红利窗口
• 并购整合机会

**⚖️ 风险管理框架**
• 市场风险评估
• 技术风险分析
• 财务风险控制
• 运营风险防范
• 合规风险管理

**🌍 区域投资比较**
• 不同地区投资环境对比
• 政策支持力度比较
• 基础设施完善度评估
• 人才资源可获得性
• 市场准入便利程度

**💡 投资策略建议**
• 分阶段投资策略
• 合作伙伴选择
• 融资方案设计
• 退出机制规划
• 投资组合优化

**📋 尽职调查清单**
• 法律合规性审查
• 财务状况分析
• 市场前景评估
• 技术可行性验证
• 团队能力评价`;
  }

  /**
   * 生成交通信息
   */
  generateTransportationInfo() {
    return `**交通便利性评估体系**

**🛣️ 公路交通网络**
• 高速公路可达性
• 国道/省道连接情况
• 市内道路状况
• 货运专用通道
• 交通拥堵状况分析

**🚂 铁路运输系统**
• 货运铁路线路
• 客运铁路便利性
• 多式联运枢纽
• 铁路运输成本
• 未来扩建计划

**⚓ 港口海运条件**
• 最近港口距离
• 港口处理能力
• 航线覆盖范围
• 港口费用水平
• 通关效率

**✈️ 航空运输**
• 国际机场距离
• 航班频次和目的地
• 货运航空服务
• 空港物流园区
• 航空运输成本

**🚛 物流配送网络**
• 第三方物流服务商
• 仓储设施分布
• 配送效率
• 冷链物流能力
• 跨境物流服务

**📊 交通成本分析**
• 不同运输方式成本对比
• 运输时间价值评估
• 库存成本影响
• 总物流成本优化
• 运输保险费用`;
  }

  /**
   * 生成政策信息
   */
  generatePolicyInfo() {
    return `**政策环境综合分析**

**🏛️ 投资促进政策**
• 外商投资优惠政策
• 税收减免措施
• 土地使用优惠
• 资金补贴支持
• 一站式服务便利

**📋 产业支持政策**
• 重点支持产业目录
• 产业集群发展规划
• 技术创新支持
• 人才引进政策
• 绿色发展激励

**⚖️ 法律法规环境**
• 外商投资法规
• 劳动法律保护
• 环保法规要求
• 知识产权保护
• 合同执行效力

**🌱 可持续发展政策**
• 环保标准要求
• 碳排放限制
• 清洁能源推广
• 循环经济促进
• ESG合规要求

**🔄 政策变化趋势**
• 政策连续性评估
• 未来政策方向
• 国际协议影响
• 区域一体化进程
• 数字化政策支持

**📈 政策利用策略**
• 政策申报时机
• 申报材料准备
• 政府关系维护
• 合规风险防控
• 政策效益最大化

**💼 政策咨询建议**
建议聘请当地专业的政策咨询机构，及时了解最新政策动态，确保充分享受各项优惠政策，同时规避合规风险。`;
  }

  /**
   * 使用Gemini生成回答
   */
  async generateGeminiAnswer(prompt) {
    console.log(`🤖 调用Gemini API: ${this.currentModel.name}`);

    const model = this.gemini.getGenerativeModel({
      model: this.currentModel.name
    });

    // 添加超时处理 - 增加到60秒
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Gemini API 调用超时')), 60000)
    );

    try {
      console.log('📡 发送请求到Gemini...');
      const result = await Promise.race([
        model.generateContent(prompt),
        timeoutPromise
      ]);

      console.log('✅ 收到Gemini响应');
      const response = await result.response;
      const text = response.text();
      console.log(`📝 响应长度: ${text.length} 字符`);
      return text;
    } catch (error) {
      console.error('❌ Gemini API 调用失败:', error.message);
      throw error;
    }
  }

  /**
   * 使用OpenRouter生成回答
   */
  async generateOpenRouterAnswer(prompt) {
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.currentModel.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://industrial-geo-dev.com',
        'X-Title': 'Industrial Geo Dev AI Assistant'
      },
      body: JSON.stringify({
        model: this.currentModel.name,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: this.currentModel.maxTokens || 2048,
        temperature: this.currentModel.temperature || 0.3
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  /**
   * 执行工具调用
   */
  async executeTool(toolName, params) {
    switch (toolName) {
      case 'web_search':
        return await this.webSearchService.search(params.query, params.num_results);
      default:
        throw new Error(`未知的工具: ${toolName}`);
    }
  }

  /**
   * 记录带上下文的问答历史
   */
  async logQAWithContext(question, answer, userId, contextAnalysis) {
    try {
      console.log(`📝 上下文问答记录 - 用户: ${userId || 'anonymous'}, 意图: ${contextAnalysis.intent.category}, 问题: ${question.substring(0, 50)}...`);
    } catch (error) {
      console.error('❌ 记录上下文问答历史失败:', error);
    }
  }

  /**
   * 构建知识库
   * @param {boolean} force - 是否强制重建
   */
  async buildKnowledgeBase(force = false) {
    try {
      console.log('🔨 开始构建知识库...');

      if (this.knowledgeBaseBuilt && !force) {
        console.log('✅ 知识库已存在，跳过构建');
        return {
          success: true,
          message: '知识库已存在',
          status: this.getStatus()
        };
      }

      // 初始化向量存储（如果尚未初始化）
      if (!this.vectorStore) {
        this.vectorStore = new MemoryVectorStore();
        console.log('🗄️ 初始化向量存储');
      }

      // 这里可以添加实际的文档加载和向量化逻辑
      // 现在先简单标记为已构建
      await this.simulateKnowledgeBaseBuild();

      this.knowledgeBaseBuilt = true;
      console.log('✅ 知识库构建完成');

      return {
        success: true,
        message: '知识库构建完成',
        status: this.getStatus()
      };

    } catch (error) {
      console.error('❌ 构建知识库失败:', error);
      throw new Error(`知识库构建失败: ${error.message}`);
    }
  }

  /**
   * 模拟知识库构建过程
   */
  async simulateKnowledgeBaseBuild() {
    // 真实加载本地算法文档到内存向量库（不引入本地数据库，不添加模拟数据）
    const fsp = fs.promises;
    const projectRoot = path.join(__dirname, '..');

    // 确保向量存储已初始化
    if (!this.vectorStore) {
      this.vectorStore = new MemoryVectorStore();
    }

    const algoDocPath = path.join(projectRoot, '..', 'docs', 'cluster-boundary-algorithm.md');
    try {
      const fileStat = await fsp.stat(algoDocPath);
      if (!fileStat.isFile()) {
        console.warn('⚠️ 指定的算法文档不是文件，跳过构建');
        return;
      }

      const mdContent = await fsp.readFile(algoDocPath, 'utf8');
      if (!mdContent || !mdContent.trim()) {
        console.warn('⚠️ 算法文档内容为空，跳过构建');
        return;
      }

      const documents = [{
        content: mdContent,
        metadata: {
          file_path: 'docs/cluster-boundary-algorithm.md',
          file_type: 'md',
          file_size: fileStat.size,
          last_modified: fileStat.mtime.toISOString()
        }
      }];

      // 可选增强：生成英文并行片段（不引入模拟数据，仅为同文档的派生翻译，提升跨语言检索）
      try {
        const englishParallel = this.generateEnglishParallel(mdContent);
        if (englishParallel && englishParallel.trim()) {
          documents.push({
            content: englishParallel,
            metadata: {
              file_path: 'docs/cluster-boundary-algorithm.md#en-parallel',
              file_type: 'md',
              derived_from: 'cluster-boundary-algorithm.md',
              language: 'en',
              last_modified: fileStat.mtime.toISOString()
            }
          });
        }
      } catch (e) {
        console.warn('⚠️ 英文并行片段生成失败，跳过：', e.message);
      }

      const chunks = this.splitDocuments(documents);
      await this.storeChunks(chunks);

      console.log(`📚 已加载算法文档 ${documents.length} 个文件、${chunks.length} 个片段到知识库`);
    } catch (err) {
      console.warn('⚠️ 加载算法文档失败，将跳过知识库构建：', err.message);
    }
  }

  /**
   * 获取系统状态
   */
  getStatus() {
    const docCount = this.vectorStore ? this.vectorStore.getCount() : 0;
    return {
      initialized: this.isInitialized,
      hasGeminiClient: !!this.gemini,
      hasCollection: this.vectorStore && docCount > 0,
      documentCount: docCount,
      knowledgeBaseBuilt: this.knowledgeBaseBuilt,
      model: this.currentModel?.name || 'none',
      modelType: this.currentModel?.type || 'none',
      embeddingModel: this.config.embeddingModel,
      fallbackMode: !this.currentModel
    };
  }

  /**
   * 简易检索向量库，返回最相关的片段
   */
  async retrieveRelevantChunks(question, maxResults = 5) {
    if (!this.vectorStore || this.vectorStore.getCount() === 0) {
      return [];
    }

    const queryEmbedding = this.generateSimpleEmbedding(question);
    const { documents, metadatas, distances } = await this.vectorStore.query(queryEmbedding, maxResults);

    const results = (documents[0] || []).map((doc, idx) => ({
      content: doc,
      metadata: (metadatas[0] || [])[idx] || {},
      distance: (distances[0] || [])[idx]
    }));

    return results;
  }
}

module.exports = RAGService;
