# 工业地理分析平台（Industrial Discovery）使用与架构详解

本文件为平台的中文使用说明与技术架构总览，内容覆盖：
- 顶部导航的使用方式与页面跳转路径
- 每个页面/模块的进入方式与功能清单
- 典型业务流程（从探索到详情、从 AI 分析到站点选择等）
- 后端接口映射与关键环境变量
- 启动、部署与数据来源说明

本文档适用于产品演示、内部协作、以及新成员快速上手。

---

## 1. 顶部导航与全局入口

平台首页（`/`，WelcomePage）顶部拥有玻璃拟态导航栏（GlassNavigation），主要菜单如下：

- Home（首页）：点击后回到欢迎页（`/`）
- About（关于我们）：预留项（当前无跳转）
- Products（产品）：悬停/点击展开下拉菜单
  - Dashboard：跳转至主地图页面（`/main`）
  - Site Analyzer：跳转至 AI 分析器（`/ai-analyzer`）
  - Consulting：预留项（当前无跳转）
- Pricing（价格方案）：预留项（当前无跳转）
- Solution（解决方案）：预留项（当前无跳转）
- Language（语言）：EN/中文开关（切换后存储到 `localStorage.preferredLanguage` 并刷新）
- 右侧用户区域：
  - 未登录：显示“Login / Register”，点击跳转 `'/login'`
  - 已登录：显示用户名，点击展开下拉菜单：
    - Profile：跳转 `'/profile'`
    - Logout：清除登录状态并刷新页面

欢迎页正文区还提供一键进入：
- “EXPLORE PLATFORM”（探索平台）按钮 → 跳转 `/main`
- “MEET OUR TEAM” → 页面内滚动到团队区域

---

## 2. 页面与功能详解

以下按用户可见的路由逐一说明：

### 2.1 欢迎页（`/`，WelcomePage）
- 进入方式：
  - 顶部导航 Home
  - 登录成功后默认返回首页
  - 直接访问网址根路径
- 重要入口：
  - 顶栏 Products → Dashboard → `/main`
  - 顶栏 Products → Site Analyzer → `/ai-analyzer`
  - 右上角“Login / Register” → `/login`
  - 主按钮“EXPLORE PLATFORM” → `/main`
- 页面功能：
  - 3D 粒子地球与科技感背景装饰
  - “GLOBAL INDUSTRIAL NETWORK”可视化（世界地图热点点击美国或泰国会跳转 `/main`）
  - 特色能力介绍（Key Features）卡片
  - 团队展示（Team Carousel）
  - 联系我们（Contact）与页脚

### 2.2 主地图（Dashboard，`/main`，MainPage）
- 进入方式：
  - 顶栏 Products → Dashboard
  - 欢迎页“EXPLORE PLATFORM”按钮
  - 直接访问 `/main`
- 页面结构：
  - 左侧主侧边栏：显示筛选结果列表（美国或泰国园区/地块数据）
  - 右侧地图：Leaflet 地图 + 图层/控件
  - 顶部/右上角地图控件：图层切换、语言切换、国家（USA/THAILAND）切换等
- 关键功能（地图与控件）：
  - 国家切换 USA / THAILAND
  - 地图样式：白天/夜间/卫星
  - 筛选功能（美国数据）：
    - 租金范围（min/max）
    - 州/城市多选
    - 排序（升序/降序）
    - 显示模式：全部/ports/cities（泰国数据对 ports/cities 使用不同坐标）
  - 图层：
    - USStatesLayer（美国州边界）
    - ThailandProvincesLayer（泰国省边界）
    - EconomicHotspotLayer（经济热点 LQ/重力模型）及图例/控制面板
    - EnergyDataLayer（能源数据）
    - ClusterBoundaryLayer（大规模地块的聚类边界显示）
  - 工具：
    - 自定义标记工具（CustomMarker）
    - 测量工具（Ruler）
    - 状态恢复：从详情页返回时恢复地图中心/缩放与筛选状态
- 交互与跳转：
  - 在侧边栏点击某个园区/地块，地图飞到该位置并高亮，弹出信息
  - 弹窗或列表中的“View Details”：
    - 若为美国数据且存在 `apn`（地块号），跳转地块详情 `/parcel/:apn`
    - 若为美国数据但无 `apn`，则在当前页打开 Cluster 信息面板（侧边）
    - 若为泰国园区，跳转园区详情 `/park/:id`
  - 点击地图上的聚类边界（ClusterBoundaryLayer）可显示对应 Cluster 信息面板

### 2.3 园区详情（`/park/:id`，ParkDetailPage）
- 进入方式：
  - 在主地图（泰国）列表或标记弹窗点击“View Details”
  - 直接访问 `/park/园区名称`
- 页面结构与功能：
  - 顶部：返回按钮（返回 `/main`）、标题、位置标签
  - 左侧：扁平化两层侧边栏菜单
    - Map（地图视图，默认）
    - Overview（概览）
    - 各类分析面板（劳动力趋势、劳动力经济、人口迁移、教育水平、婚姻状况、迁移类型/频率/预期停留/原因、资金往来等）
    - Park Layout（园区布局）
    - Contact Info（联系信息）
    - Park Highlights（园区亮点）
  - 右侧：地图+图层工具（MapStyle、Weather、测量工具、TimeSlider、EnvironmentMonitoring、MigrationType 等）
  - 地图特性：
    - 初次进入自动飞入园区轮廓（粉色 Polygon）
    - 切换到“地图”时，若已执行过飞入动画，则直接适配边界
    - 交通图层：园区到港口/机场/城市的连线与距离气泡
  - 返回：顶部“Back to Map”返回 `/main`（会恢复前次地图状态）

### 2.4 地块详情（`/parcel/:apn`，ParcelDetailPage）
- 进入方式：
  - 主地图（美国数据）列表或弹窗点击“View Details”，当该地块有 `apn`
  - 直接访问 `/parcel/某APN`
- 数据来源：`public/data/cluster_*.csv` 中按 `apn` 搜索
- 页面功能：
  - 顶部导航：Back to Map → `/main`
  - 统一信息卡：显示 NOI、建筑面积、最新成交价等
  - 信息面板：基础信息、财务信息、业主信息、位置信息
  - 底部动作区：
    - View on Map：在 Google Maps 打开地理位置
    - Calculate ROI：弹出 ROI 模态框（基于默认假设计算）
    - Share：调用 Web Share API 或复制链接

### 2.5 AI 分析器（`/ai-analyzer`，AIAnalyzerPage）
- 进入方式：
  - 顶栏 Products → Site Analyzer
  - 直接访问 `/ai-analyzer`
- 登录态：
  - 未登录：显示登录提示卡片（按钮跳转 `/login`）
  - 已登录：展示项目列表（支持收藏、删除、新建等）
- 功能：
  - 新建项目（名称/描述）
  - 打开项目 → 跳转到项目页 `/ai-project/:id`
  - 切换“全部项目 / 收藏夹”

### 2.6 AI 项目（`/ai-project/:id`，AIProjectPage）
- 进入方式：
  - AI 分析器项目卡片“Open”或直接访问 `/ai-project/:id`（需登录）
- 页面功能：
  - 双模式输入：表单结构化输入 或 粘贴邮件文本
  - 点击“Analyze with AI”（调用 OpenRouter 模型），在右侧弹出结果面板（可编辑、导出 JSON）
  - “Site Selection”按钮：携带分析结果跳转 `/site-selection`

### 2.7 站点选择（`/site-selection`，SiteSelectionPage）
- 进入方式：
  - AI 项目页点击“Site Selection”
  - 直接访问 `/site-selection`（若无分析输入，仍可手动填写参数搜索）
- 页面功能：
  - 左侧：搜索条件（目标区域、物流枢纽、面积范围、运营参数、候选数量等）
  - 中部：地图工作区（候选点、选择后自动运行财务分析）
  - 右侧：财务分析面板（总投资、面积、成本分解、IRR、NPV、Cash-on-Cash 与投资建议）
  - 视图切换：Dashboard / Map Focus（全屏地图）

### 2.8 登录/注册（`/login`，LoginPage）
- 进入方式：
  - 顶栏右上“Login / Register” 或未认证访问受限页面时跳转
- 功能：
  - 登录：支持邮箱或用户名 + 密码
  - 注册：用户名、邮箱、密码/确认、语言偏好
  - 顶部语言按钮一键切换 EN/中文
  - 登录成功：显示成功提示后返回首页 `/`

### 2.9 用户资料（`/profile`，ProfilePage）
- 进入方式：
  - 登录后，顶栏用户菜单 → Profile
- 功能：
  - 展示基础信息（ID、邮箱、用户名、首选语言、订阅状态、注册时间、最近登录）
  - 编辑：用户名、首选语言（保存后更新 `localStorage.preferredLanguage`）
  - 返回按钮：返回上一页

---

## 3. 典型流程演示

### 3.1 从首页进入主地图并查看详情
1. 首页顶部 **Products → Dashboard**（或点击 **EXPLORE PLATFORM**）进入 `/main`
2. 在侧边栏选择一个园区/地块 → 地图飞入并高亮
3. 点击弹出卡片中的 **View Details**：
   - 美国数据且有 APN → 跳转 `/parcel/:apn`
   - 美国无 APN → 打开 Cluster 信息面板（仍在 `/main`）
   - 泰国园区 → 跳转 `/park/:id`
4. 在详情页点击 **Back to Map** 返回 `/main`，地图状态自动恢复

### 3.2 AI 分析到站点选择
1. 顶栏 **Products → Site Analyzer** → `/ai-analyzer`
2. 未登录 → 点击 **Sign In** 跳转 `/login` 并完成登录
3. 回到 `/ai-analyzer`，新建或打开项目 → `/ai-project/:id`
4. 在项目页输入需求（表单或邮件），点击 **Analyze with AI** → 查看结果
5. 点击 **Site Selection** → `/site-selection`
6. 如有需要调整条件，点击 **Find Sites** 重新获取候选与财务分析

### 3.3 修改个人资料
1. 登录后，顶栏右侧用户名 → **Profile** → `/profile`
2. 点击 **Edit Profile** → 修改用户名/首选语言 → **Save Changes**
3. 返回上一页或继续其他操作

---

## 4. 后端接口与服务概览

- 统一前缀：`/api`
- 鉴权：JWT（`Authorization: Bearer <token>`）

主要路由：
- `/api/health`、`/api/db-status`：健康检查、数据库状态
- `/api/auth/*`：注册、登录、获取当前用户、更新资料（`authenticateToken` 中间件）
- `/api/projects/*`：项目 CRUD、收藏/取消收藏（需登录）
- `/api/analyze`：简单文本提取（OpenRouter）
- `/api/site-selection/*`：站点勘探/财务/压力测试（Node 调用 Python 脚本）
- `/api/cpi/*`：基于 BLS CPI 的成本上涨计算
- `/api/qcew/*`：基于 BLS Time Series（旧版，仍保留）
- `/api/us-labor/laus/*`：LAUS 可招聘余量计算（按 FIPS 匹配）
- `/api/us-labor/qcew/*`：CSV 驱动 QCEW（当前为 **禁用** 状态，健康检查会返回说明与替代建议）
- `/api/rag/*`：RAG 智能问答、流式 SSE、反馈与状态（集成 WebSearchService，可配置 Google CSE）

数据库：MySQL（`backend-server/config/database.js`），连接池、重试机制与表检查。

Python：`backend-server/site_selection_algorithm/*`，通过 `spawn('python3', api_wrapper.py, ...)` 调用，当前使用 Mock Regrid 数据（未来可接真 API）。

---

## 5. 运行与环境配置

### 5.1 本地开发
- 安装依赖：`npm install`
- 启动开发（前端+后端）：`npm run dev:all`
  - 前端：Vite（默认 5173/5174）
  - 后端：Express（默认 3001）
- 单独运行：
  - 前端：`npm run dev`
  - 后端：`npm run backend:dev`（在 `backend-server/`）

### 5.2 环境变量
- 前端：
  - `VITE_API_BASE_URL`（生产由 Amplify 写入 `.env.production`）
- 后端（需配置）：
  - 端口/数据库：`PORT`、`DB_HOST`、`DB_PORT`、`DB_USER`、`DB_PASSWORD`（可选）、`DB_NAME`
  - JWT：`JWT_SECRET`
  - AI：`OPENROUTER_API_URL`、`OPENROUTER_API_KEY`
  - 搜索（可选）：`GOOGLE_SEARCH_API_KEY`、`GOOGLE_SEARCH_ENGINE_ID`
  - 其它：`NODE_ENV`、（Python 相关依赖请在目标环境提前安装）

### 5.3 部署
- Amplify（前端）：`amplify.yml` 会在构建前写入 `.env.production` 中的 API 基础地址并打包 `dist/`
- 后端：建议独立部署（PM2/Nginx/HTTPS），并确保开放 CORS 源（`server.cjs` 中已配置白名单）

---

## 6. 数据来源与注意事项
- 主地图美国与泰国数据主要来自 `public/data/` 中的 CSV/GeoJSON/JSON（部分为演示用随机或衍生数据，详见 README）
- Python Site-Selection 当前使用 Mock Regrid 数据生成候选与成本（后续可接真实 Regrid API）
- 天气工具（ParkDetailPage）使用 OpenWeather（需要前端环境注入 API Key，详 README）

---

## 7. 安全与合规建议
- 发现疑似硬编码密钥：
  - BLS API Key（`backend-server/routes/cpi.js`、`routes/qcew.js`）
  - OpenRouter Key（`src/pages/AIProjectPage.jsx`）
- 建议：
  - 将密钥全部迁移至后端环境变量，并从前端移除
  - 对已泄露的密钥进行旋转
  - 区分开发/生产配置并限制调试端点在生产环境暴露

---

## 8. 路由与跳转清单（速查）

- 顶栏 Products → **Dashboard** → `/main`
- 顶栏 Products → **Site Analyzer** → `/ai-analyzer`
- 顶栏用户 → **Profile** → `/profile`
- 顶栏用户 → **Logout** → 清除登录状态并刷新
- 欢迎页 **EXPLORE PLATFORM** → `/main`
- 主地图（美国有 APN） → **View Details** → `/parcel/:apn`
- 主地图（美国无 APN） → **Cluster Info Panel**（当前页）
- 主地图（泰国） → **View Details** → `/park/:id`
- 园区详情/地块详情 → **Back to Map** → `/main`
- AI 分析器项目卡片 → **Open** → `/ai-project/:id`
- AI 项目页 → **Site Selection** → `/site-selection`
- 未登录任一需要权限页面 → 自动提示/跳转 `/login`

---

## 9. FAQ（常见问题）
- Q：为何进入 `/us-labor/qcew` 相关接口提示服务禁用？
  - A：为避免超大 CSV 导致启动内存占用，项目默认不加载 QCEW CSV，并在健康检查进行说明。可切换为使用在线 BLS API 或独立数据服务。
- Q：欢迎页地图热点点击后为何跳转 `/main`？
  - A：热点针对当前已支持的数据区域（美国、泰国），点击对应区域以快速进入 Dashboard。

---

如需进一步的技术细节（代码模块、算法、RAG 结构、数据处理流程），建议参考：
- 根目录 `README.md`
- `docs/cluster-boundary-algorithm.md`（聚类边界算法）
- `backend-server/README.md`（后端整体说明）
